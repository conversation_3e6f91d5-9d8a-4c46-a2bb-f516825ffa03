-- Fix item_notes JSONB structure
-- The current issue is that item_notes are stored as string '[]' instead of proper JSONB array []

-- Step 1: Convert string '[]' to proper JSONB array for existing records
UPDATE order_items 
SET item_notes = '[]'::jsonb 
WHERE item_notes = '"[]"'::jsonb OR item_notes = '[]'::jsonb::text::jsonb;

-- Step 2: Ensure default value is proper JSONB array
ALTER TABLE order_items 
ALTER COLUMN item_notes SET DEFAULT '[]'::jsonb;

-- Step 3: Add a constraint to ensure item_notes is always a valid JSONB array
ALTER TABLE order_items 
ADD CONSTRAINT item_notes_is_array 
CHECK (jsonb_typeof(item_notes) = 'array');

-- Step 4: Create a function to validate item note structure
CREATE OR REPLACE FUNCTION validate_item_note_structure(note_data jsonb)
RETURNS boolean AS $$
BEGIN
  -- Check if it's an object with required fields
  IF jsonb_typeof(note_data) != 'object' THEN
    RETURN false;
  END IF;
  
  -- Check for required fields
  IF NOT (
    note_data ? 'content' AND
    note_data ? 'type' AND
    note_data ? 'category' AND
    note_data ? 'created_at'
  ) THEN
    RETURN false;
  END IF;
  
  -- Validate note type
  IF NOT (note_data->>'type' = ANY(ARRAY['production', 'quality', 'design', 'delivery', 'client_request', 'internal', 'urgent', 'general'])) THEN
    RETURN false;
  END IF;
  
  -- Validate priority (if present)
  IF note_data ? 'priority' AND NOT (note_data->>'priority' = ANY(ARRAY['low', 'medium', 'high', 'urgent'])) THEN
    RETURN false;
  END IF;
  
  RETURN true;
END;
$$ LANGUAGE plpgsql;

-- Step 5: Add comment to document the expected structure
COMMENT ON COLUMN order_items.item_notes IS 
'JSONB array of item-specific notes. Each note should have structure: 
{
  "id": "unique_id", 
  "content": "note_content", 
  "type": "production|quality|design|delivery|client_request|internal|urgent|general",
  "category": "string",
  "priority": "low|medium|high|urgent",
  "created_at": "ISO_timestamp",
  "created_by": "user_id",
  "updated_at": "ISO_timestamp"
}';

-- Step 6: Create helper function to add item notes with validation
CREATE OR REPLACE FUNCTION add_item_note(
  p_item_id uuid,
  p_content text,
  p_note_type text DEFAULT 'general',
  p_category text DEFAULT 'general',
  p_priority text DEFAULT 'medium',
  p_created_by text DEFAULT NULL
)
RETURNS jsonb AS $$
DECLARE
  v_new_note jsonb;
  v_current_notes jsonb;
BEGIN
  -- Create the new note
  v_new_note := jsonb_build_object(
    'id', gen_random_uuid()::text,
    'content', p_content,
    'type', p_note_type,
    'category', p_category,
    'priority', p_priority,
    'created_at', now()::text,
    'created_by', p_created_by,
    'updated_at', now()::text
  );
  
  -- Validate the note structure
  IF NOT validate_item_note_structure(v_new_note) THEN
    RAISE EXCEPTION 'Invalid note structure';
  END IF;
  
  -- Get current notes
  SELECT COALESCE(item_notes, '[]'::jsonb) INTO v_current_notes
  FROM order_items 
  WHERE item_id = p_item_id;
  
  -- Add the new note to the array
  v_current_notes := v_current_notes || v_new_note;
  
  -- Update the item
  UPDATE order_items 
  SET item_notes = v_current_notes
  WHERE item_id = p_item_id;
  
  RETURN v_new_note;
END;
$$ LANGUAGE plpgsql;