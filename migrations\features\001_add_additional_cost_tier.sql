-- Migration: Add additional_cost_tier column to calculation_templates
-- Adds support for per-unit vs per-order additional cost tiers

-- Add the additional_cost_tier column
ALTER TABLE calculation_templates 
ADD COLUMN IF NOT EXISTS additional_cost_tier TEXT CHECK (additional_cost_tier IN ('per_unit', 'per_order'));

-- Add a comment to explain the column
COMMENT ON COLUMN calculation_templates.additional_cost_tier IS 
'Tier for additional cost templates: per_unit (cost × quantity) or per_order (fixed cost)';

-- Create an index for better query performance
CREATE INDEX IF NOT EXISTS idx_calculation_templates_tier 
ON calculation_templates(additional_cost_tier) 
WHERE additional_cost_tier IS NOT NULL;