import React, { useState } from 'react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from './dropdown-menu'
import { StatusBadge } from './status-badge'
import { Check, Loader2, ChevronDown } from 'lucide-react'
import { cn } from '../../lib/utils'

const AVAILABLE_ROLES = ['admin', 'supervisor', 'manager', 'editor', 'viewer', 'user']

interface QuickRoleSelectProps {
  userId: string
  currentRole: string
  size?: 'sm' | 'md' | 'lg'
  onRoleChange?: (userId: string, newRole: string) => Promise<void>
  disabled?: boolean
}

export function QuickRoleSelect({
  userId,
  currentRole,
  size = 'md',
  onRoleChange,
  disabled = false
}: QuickRoleSelectProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)

  const handleRoleSelect = async (newRole: string) => {
    if (!onRoleChange || newRole === currentRole) return
    
    try {
      setIsUpdating(true)
      await onRoleChange(userId, newRole)
      setIsOpen(false)
    } catch (error) {
      console.error('Failed to update role:', error)
    } finally {
      setIsUpdating(false)
    }
  }

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild disabled={disabled}>
        <div
          className={cn(
            "relative inline-flex items-center",
            isUpdating && "cursor-wait",
            !disabled && "cursor-pointer"
          )}
          onClick={(e) => e.stopPropagation()}
        >
          {isUpdating ? (
            <div className="flex items-center animate-pulse">
              <Loader2 className="h-3 w-3 animate-spin mr-1.5 text-gray-600" />
              <StatusBadge
                status={currentRole as any}
                size={size}
                withIcon={true}
                className="opacity-60 capitalize"
              >
                {currentRole}
              </StatusBadge>
            </div>
          ) : (
            <div className="group relative">
              <StatusBadge
                status={currentRole as any}
                size={size}
                withIcon={true}
                className={cn(
                  "pr-6 capitalize transition-all",
                  !disabled && "hover:opacity-90"
                )}
              >
                {currentRole}
              </StatusBadge>
              {!disabled && (
                <ChevronDown className="h-3 w-3 text-gray-600 dark:text-gray-400 absolute right-1.5 top-1/2 -translate-y-1/2 group-hover:text-gray-800 dark:group-hover:text-gray-200" />
              )}
            </div>
          )}
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent 
        align="start" 
        className="w-48 p-1"
        sideOffset={5}
        onClick={(e) => e.stopPropagation()}
      >
        {AVAILABLE_ROLES.map((role) => (
          <DropdownMenuItem
            key={role}
            onClick={(e) => {
              e.stopPropagation()
              handleRoleSelect(role)
            }}
            className={cn(
              "cursor-pointer transition-colors",
              currentRole === role && "bg-gray-50 dark:bg-gray-800"
            )}
            disabled={isUpdating || currentRole === role}
          >
            <div className="flex items-center justify-between w-full py-0.5">
              <StatusBadge
                status={role as any}
                size="sm"
                withIcon={false}
                className="capitalize"
              >
                {role}
              </StatusBadge>
              {currentRole === role && (
                <Check className="h-3.5 w-3.5 text-green-600 dark:text-green-400" />
              )}
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}