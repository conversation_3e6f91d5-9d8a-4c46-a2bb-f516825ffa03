-- <PERSON><PERSON><PERSON> to set up Supabase Realtime for the application
-- Run this script in the SQL Editor of your Supabase project

-- First, drop the existing publication if it exists
DROP PUBLICATION IF EXISTS supabase_realtime;

-- Create a new publication with no tables initially
CREATE PUBLICATION supabase_realtime;

-- Add tables to the publication
-- These are the tables we want to track changes for
ALTER PUBLICATION supabase_realtime ADD TABLE public.orders;
ALTER PUBLICATION supabase_realtime ADD TABLE public.order_items;
ALTER PUBLICATION supabase_realtime ADD TABLE public.products;
ALTER PUBLICATION supabase_realtime ADD TABLE public.attributes;

-- Create a policy to allow authenticated users to receive broadcasts
-- This is required for Realtime Authorization
CREATE POLICY "Authenticated users can receive broadcasts"
ON "realtime"."messages"
FOR SELECT
TO authenticated
USING ( true );

-- Set replica identity to full for better change tracking
-- This ensures that the old record values are included in DELETE events
ALTER TABLE public.orders REPLICA IDENTITY FULL;
ALTER TABLE public.order_items REPLICA IDENTITY FULL;
ALTER TABLE public.products REPLICA IDENTITY FULL;
ALTER TABLE public.attributes REPLICA IDENTITY FULL;

-- Verify the publication is set up correctly
SELECT * FROM pg_publication WHERE pubname = 'supabase_realtime';
SELECT * FROM pg_publication_tables WHERE pubname = 'supabase_realtime';
