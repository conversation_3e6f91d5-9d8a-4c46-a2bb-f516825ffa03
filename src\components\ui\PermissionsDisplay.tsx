import React from 'react'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from './dropdown-menu'
import { Badge } from './badge'
import { ChevronDown, Shield } from 'lucide-react'
import { formatPermissionLabel } from '../../utils/permissionFormatter'

interface PermissionsDisplayProps {
  permissions: string[]
  className?: string
}

/**
 * Display permissions as a count badge with dropdown showing all permissions
 * Uses human-readable labels, no backend codes visible to users
 */
export function PermissionsDisplay({ permissions, className = '' }: PermissionsDisplayProps) {
  if (permissions.length === 0) {
    return (
      <span className="text-xs text-muted-foreground">No permissions</span>
    )
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button 
          className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200 rounded-md hover:bg-blue-100 transition-colors ${className}`}
        >
          <Shield className="h-3 w-3" />
          {permissions.length} Permission{permissions.length !== 1 ? 's' : ''}
          <ChevronDown className="h-3 w-3" />
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-64">
        <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground border-b">
          Assigned Permissions ({permissions.length})
        </div>
        <div className="max-h-48 overflow-y-auto">
          {permissions.map((permission) => (
            <DropdownMenuItem key={permission} className="flex items-center gap-2">
              <div className="flex flex-col">
                <span className="text-sm font-medium">
                  {formatPermissionLabel(permission)}
                </span>
              </div>
            </DropdownMenuItem>
          ))}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}