-- Verification Script for Permissions System Migration V2
-- Run this after applying migration 043_modernize_permissions_system_v2.sql
-- Date: 2025-09-12

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

SELECT '=== MIGRATION VERIFICATION REPORT ===' as report_section;

-- 1. Check backup tables were created
SELECT 
  'BACKUP TABLES' as check_category,
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'permissions_backup') 
    THEN '✓ permissions_backup exists'
    ELSE '✗ permissions_backup missing'
  END as permissions_backup,
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'roles_backup') 
    THEN '✓ roles_backup exists'
    ELSE '✗ roles_backup missing'
  END as roles_backup,
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'authorized_users_backup') 
    THEN '✓ authorized_users_backup exists'
    ELSE '✗ authorized_users_backup missing'
  END as users_backup;

-- 2. Verify new permissions structure
SELECT 
  'PERMISSIONS STRUCTURE' as check_category,
  COUNT(*) as total_permissions,
  COUNT(CASE WHEN category = 'orders' THEN 1 END) as orders_permissions,
  COUNT(CASE WHEN category = 'products' THEN 1 END) as products_permissions,
  COUNT(CASE WHEN category = 'clients' THEN 1 END) as clients_permissions,
  COUNT(CASE WHEN category = 'analytics' THEN 1 END) as analytics_permissions,
  COUNT(CASE WHEN category = 'admin' THEN 1 END) as admin_permissions,
  COUNT(CASE WHEN category = 'system' THEN 1 END) as system_permissions
FROM permissions;

-- 3. Show all new permissions
SELECT 
  'NEW PERMISSIONS LIST' as check_category,
  category,
  key,
  name,
  is_active
FROM permissions 
ORDER BY category, key;

-- 4. Verify new roles structure
SELECT 
  'ROLES STRUCTURE' as check_category,
  COUNT(*) as total_roles,
  COUNT(CASE WHEN is_system_role = true THEN 1 END) as system_roles,
  COUNT(CASE WHEN is_active = true THEN 1 END) as active_roles
FROM roles;

-- 5. Show all new roles with permissions
SELECT 
  'NEW ROLES LIST' as check_category,
  name,
  display_name,
  array_length(permissions, 1) as permission_count,
  permissions,
  is_system_role,
  is_active
FROM roles 
ORDER BY 
  CASE name 
    WHEN 'viewer' THEN 1 
    WHEN 'operator' THEN 2 
    WHEN 'manager' THEN 3 
    WHEN 'admin' THEN 4 
    ELSE 5 
  END;

-- 6. Check user migration results
SELECT 
  'USER MIGRATION RESULTS' as check_category,
  COUNT(*) as total_users,
  COUNT(CASE WHEN is_active = true THEN 1 END) as active_users,
  COUNT(CASE WHEN role_template IS NOT NULL THEN 1 END) as users_with_roles,
  COUNT(CASE WHEN array_length(permissions, 1) > 0 THEN 1 END) as users_with_permissions
FROM authorized_users;

-- 7. Show role distribution after migration
SELECT 
  'ROLE DISTRIBUTION' as check_category,
  role_template,
  COUNT(*) as user_count,
  COUNT(CASE WHEN is_active = true THEN 1 END) as active_user_count
FROM authorized_users 
WHERE role_template IS NOT NULL
GROUP BY role_template
ORDER BY user_count DESC;

-- 8. Sample user permissions after migration
SELECT 
  'SAMPLE USER PERMISSIONS' as check_category,
  email,
  role_template,
  array_length(permissions, 1) as individual_permission_count,
  permissions as individual_permissions,
  is_active,
  updated_at
FROM authorized_users 
WHERE is_active = true
ORDER BY email
LIMIT 10;

-- 9. Verify permissions alignment with frontend expectations
WITH frontend_expected AS (
  SELECT unnest(ARRAY[
    'orders.view', 'orders.create', 'orders.edit', 'orders.delete',
    'products.view', 'products.create', 'products.edit', 'products.delete', 
    'clients.view', 'clients.create', 'clients.edit', 'clients.delete',
    'analytics.view', 'analytics.export',
    'settings.view', 'settings.edit',
    'admin.users', 'admin.permissions',
    'system.admin'
  ]) as expected_key
),
database_actual AS (
  SELECT key as actual_key FROM permissions WHERE is_active = true
)
SELECT 
  'FRONTEND ALIGNMENT CHECK' as check_category,
  fe.expected_key,
  CASE 
    WHEN da.actual_key IS NOT NULL THEN '✓ Found'
    ELSE '✗ Missing'
  END as status
FROM frontend_expected fe
LEFT JOIN database_actual da ON fe.expected_key = da.actual_key
ORDER BY fe.expected_key;

-- 10. Check for any orphaned old permissions in user accounts
SELECT 
  'ORPHANED PERMISSIONS CHECK' as check_category,
  perm_key,
  COUNT(*) as users_with_this_permission
FROM (
  SELECT 
    unnest(permissions) as perm_key
  FROM authorized_users 
  WHERE array_length(permissions, 1) > 0
) user_perms
LEFT JOIN permissions p ON user_perms.perm_key = p.key
WHERE p.key IS NULL
GROUP BY perm_key
ORDER BY users_with_this_permission DESC;

-- 11. Final validation summary
SELECT 
  'MIGRATION SUCCESS SUMMARY' as check_category,
  CASE 
    WHEN (SELECT COUNT(*) FROM permissions WHERE category IN ('orders', 'products', 'clients', 'analytics', 'admin', 'system')) = 17 
    THEN '✓ All 17 expected permissions created'
    ELSE '✗ Permission count mismatch'
  END as permissions_check,
  CASE 
    WHEN (SELECT COUNT(*) FROM roles WHERE name IN ('viewer', 'operator', 'manager', 'admin')) = 4 
    THEN '✓ All 4 expected roles created'
    ELSE '✗ Role count mismatch'
  END as roles_check,
  CASE 
    WHEN (SELECT COUNT(*) FROM authorized_users WHERE role_template NOT IN ('viewer', 'operator', 'manager', 'admin') AND role_template IS NOT NULL) = 0
    THEN '✓ All users migrated to new roles'
    ELSE '✗ Some users have unmigrated roles'
  END as user_migration_check;

SELECT '=== END VERIFICATION REPORT ===' as report_end;