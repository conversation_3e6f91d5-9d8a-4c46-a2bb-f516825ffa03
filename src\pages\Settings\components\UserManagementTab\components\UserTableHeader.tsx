import React from 'react'
import { Input } from '../../../../../components/ui/input'
import { Button } from '../../../../../components/ui/button'
import { MetricBadge } from '../../../../../components/ui/metric-badge'
import { Search, Plus } from 'lucide-react'
import type { AuthorizedUser } from '../../../../../services/admin'
import type { UserFilters } from '../types'

interface UserTableHeaderProps {
  users: AuthorizedUser[]
  filters: UserFilters
  onSearchChange: (search: string) => void
  onActiveFilterChange: (filter: boolean | null) => void
  onCreateUser: () => void
  canCreate: boolean
}

export function UserTableHeader({
  users,
  filters,
  onSearchChange,
  onActiveFilterChange,
  onCreateUser,
  canCreate
}: UserTableHeaderProps) {
  return (
    <div className="flex justify-between items-center">
      <div className="flex items-center gap-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search users..."
            value={filters.search}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10 w-64"
          />
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant={filters.activeFilter === null ? "default" : "outline"}
            size="sm"
            onClick={() => onActiveFilterChange(null)}
            className={filters.activeFilter === null ? "bg-gray-700 text-white hover:bg-gray-800" : "border-gray-400 text-gray-600 hover:bg-gray-100"}
          >
            All <MetricBadge value={users.length} variant="neutral" className="ml-1 text-xs" />
          </Button>
          <Button
            variant={filters.activeFilter === true ? "default" : "outline"}
            size="sm"
            onClick={() => onActiveFilterChange(true)}
            className={filters.activeFilter === true ? "bg-gray-700 text-white hover:bg-gray-800" : "border-gray-400 text-gray-600 hover:bg-gray-100"}
          >
            Active <MetricBadge value={users.filter(u => u.is_active).length} variant="success" className="ml-1 text-xs" />
          </Button>
          <Button
            variant={filters.activeFilter === false ? "default" : "outline"}
            size="sm"
            onClick={() => onActiveFilterChange(false)}
            className={filters.activeFilter === false ? "bg-gray-700 text-white hover:bg-gray-800" : "border-gray-400 text-gray-600 hover:bg-gray-100"}
          >
            Inactive <MetricBadge value={users.filter(u => !u.is_active).length} variant="warning" className="ml-1 text-xs" />
          </Button>
        </div>
      </div>

      {canCreate && (
        <Button onClick={onCreateUser} className="bg-gray-700 hover:bg-gray-800 text-white">
          <Plus className="h-4 w-4 mr-2" />
          Add User
        </Button>
      )}
    </div>
  )
}