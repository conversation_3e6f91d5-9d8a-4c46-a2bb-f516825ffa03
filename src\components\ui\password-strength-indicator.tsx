/**
 * Password Strength Indicator Component
 * Shows real-time password strength and requirements
 * Following CLAUDE.md guidelines - focused component under 250 lines
 */

import React from 'react';
import { Check, X, Shield, AlertCircle } from 'lucide-react';
import { 
  validatePassword, 
  getPasswordStrengthColor, 
  getPasswordStrengthProgressColor,
  type PasswordStrength 
} from '../../utils/passwordValidation';

interface PasswordStrengthIndicatorProps {
  password: string;
  className?: string;
  showRequirements?: boolean;
}

export const PasswordStrengthIndicator: React.FC<PasswordStrengthIndicatorProps> = ({
  password,
  className = '',
  showRequirements = true
}) => {
  const strength = validatePassword(password);
  
  // Don't show anything for empty passwords
  if (!password.trim()) {
    return null;
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Strength Bar and Label */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Shield className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Password Strength</span>
          </div>
          <span className={`text-sm font-medium capitalize ${getPasswordStrengthColor(strength.level)}`}>
            {strength.level.replace('-', ' ')} ({strength.score}%)
          </span>
        </div>
        
        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className={`h-2 rounded-full transition-all duration-300 ${getPasswordStrengthProgressColor(strength.level)}`}
            style={{ width: `${strength.score}%` }}
          />
        </div>
      </div>


      {/* Requirements List - Simplified */}
      {showRequirements && strength.requirements.filter(req => !req.passed).length > 0 && (
        <div className="space-y-1">
          <div className="text-xs text-gray-600">
            <span className="font-medium">Missing: </span>
            {strength.requirements
              .filter(req => !req.passed)
              .map((req, index, arr) => (
                <span key={req.id}>
                  {req.label.toLowerCase().replace('contains ', '').replace('at least ', '')}
                  {index < arr.length - 1 ? ', ' : ''}
                </span>
              ))
            }
          </div>
        </div>
      )}
    </div>
  );
};

export default PasswordStrengthIndicator;