-- ============================================
-- Migration 021: Harmonize Manual Trigger with Breakdown System
-- ============================================
-- Updates the calculate_order_production_costs function to use the breakdown-aware
-- calculation so manual triggers create both production cost and detailed breakdown data

-- Replace the existing function to use breakdown calculation
CREATE OR REPLACE FUNCTION calculate_order_production_costs(
  p_order_ids UUID[] DEFAULT NULL,
  p_date_from DATE DEFAULT NULL,
  p_date_to DATE DEFAULT NULL,
  p_force_recalculate BOOLEAN DEFAULT FALSE,
  p_dry_run BOOLEAN DEFAULT FALSE
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  v_processed_count INTEGER := 0;
  v_updated_count INTEGER := 0;
  v_error_count INTEGER := 0;
  v_total_production_cost NUMERIC := 0;
  v_breakdown_success_count INTEGER := 0;
  v_errors JSONB := '[]'::JSONB;
  v_item_record RECORD;
  v_mapping_result RECORD;
  v_cost_result RECORD;
  v_calculated_cost NUMERIC;
  v_new_profit_amount NUMERIC;
  v_new_profit_margin NUMERIC;
  v_error_message TEXT;
  v_breakdown_data JSONB;
BEGIN
  -- Build the query to get order items based on filters
  FOR v_item_record IN
    SELECT 
      oi.item_id,
      oi.order_id,
      oi.product,
      oi.product_type,
      oi.size,
      oi.qty,
      oi.nos,
      oi.discount_amount,
      oi.production_cost as current_production_cost,
      oi.calculation_breakdown,
      o.created_at
    FROM order_items oi
    INNER JOIN orders o ON oi.order_id = o.order_id
    WHERE 
      -- Filter by order IDs if specified
      (p_order_ids IS NULL OR oi.order_id = ANY(p_order_ids))
      -- Filter by date range if specified
      AND (p_date_from IS NULL OR o.created_at::DATE >= p_date_from)
      AND (p_date_to IS NULL OR o.created_at::DATE <= p_date_to)
      -- Only process items with valid data
      AND oi.product IS NOT NULL 
      AND oi.product != ''
      AND oi.product_type IS NOT NULL
      AND oi.size IS NOT NULL
      -- Skip if already calculated (unless force recalculate)
      -- Check both production_cost and calculation_breakdown for completeness
      AND (
        p_force_recalculate = TRUE 
        OR oi.production_cost = 0 
        OR oi.production_cost IS NULL
        OR oi.calculation_breakdown IS NULL
      )
  LOOP
    v_processed_count := v_processed_count + 1;
    v_error_message := NULL;
    v_calculated_cost := 0;
    v_breakdown_data := NULL;

    BEGIN
      -- Step 1: Map order item attributes to IDs
      SELECT * INTO v_mapping_result
      FROM map_order_item_to_attributes(
        v_item_record.product,
        v_item_record.product_type,
        v_item_record.size
      );

      IF NOT v_mapping_result.mapping_success THEN
        v_error_message := 'Failed to map attributes to IDs';
        RAISE EXCEPTION '%', v_error_message;
      END IF;

      -- Step 2: Calculate production cost WITH breakdown data
      SELECT * INTO v_cost_result
      FROM calculate_item_production_cost_with_breakdown(
        v_item_record.item_id,
        v_mapping_result.category_id,
        v_mapping_result.product_type_id,
        v_mapping_result.size_id,
        COALESCE(v_item_record.qty, 1),
        COALESCE(v_item_record.nos, 1)
      );

      IF NOT v_cost_result.calculation_success THEN
        v_error_message := 'Production cost calculation with breakdown failed';
        RAISE EXCEPTION '%', v_error_message;
      END IF;

      v_calculated_cost := v_cost_result.total_cost;
      v_breakdown_data := v_cost_result.breakdown_data;

      -- Step 3: Calculate new profit metrics
      v_new_profit_amount := COALESCE(v_item_record.discount_amount, 0) - v_calculated_cost;
      
      IF COALESCE(v_item_record.discount_amount, 0) > 0 THEN
        v_new_profit_margin := (v_new_profit_amount / v_item_record.discount_amount) * 100;
      ELSE
        v_new_profit_margin := 0;
      END IF;

      -- Step 4: Update the order item with production cost, breakdown data, and profit metrics
      IF NOT p_dry_run THEN
        UPDATE order_items
        SET 
          production_cost = v_calculated_cost,
          calculation_breakdown = v_breakdown_data,
          profit_amount = v_new_profit_amount,
          profit_margin_percentage = v_new_profit_margin
        WHERE item_id = v_item_record.item_id;
      END IF;

      v_updated_count := v_updated_count + 1;
      v_total_production_cost := v_total_production_cost + v_calculated_cost;
      
      -- Track breakdown success
      IF v_breakdown_data IS NOT NULL THEN
        v_breakdown_success_count := v_breakdown_success_count + 1;
      END IF;

    EXCEPTION WHEN OTHERS THEN
      v_error_count := v_error_count + 1;
      v_error_message := COALESCE(v_error_message, SQLERRM);
      
      -- Add error to error list
      v_errors := v_errors || jsonb_build_object(
        'item_id', v_item_record.item_id,
        'order_id', v_item_record.order_id,
        'product', v_item_record.product,
        'product_type', v_item_record.product_type,
        'size', v_item_record.size,
        'error', v_error_message
      );
    END;
  END LOOP;

  -- Return enhanced summary results with breakdown information
  RETURN jsonb_build_object(
    'processed_count', v_processed_count,
    'updated_count', v_updated_count,
    'error_count', v_error_count,
    'breakdown_success_count', v_breakdown_success_count,
    'total_production_cost', v_total_production_cost,
    'average_production_cost', CASE 
      WHEN v_updated_count > 0 THEN v_total_production_cost / v_updated_count 
      ELSE 0 
    END,
    'breakdown_success_rate', CASE 
      WHEN v_updated_count > 0 THEN (v_breakdown_success_count::NUMERIC / v_updated_count) * 100
      ELSE 0
    END,
    'dry_run', p_dry_run,
    'errors', v_errors,
    'summary', jsonb_build_object(
      'success_rate', CASE 
        WHEN v_processed_count > 0 THEN (v_updated_count::NUMERIC / v_processed_count) * 100
        ELSE 0
      END,
      'filters_applied', jsonb_build_object(
        'order_ids', p_order_ids,
        'date_from', p_date_from,
        'date_to', p_date_to,
        'force_recalculate', p_force_recalculate
      )
    )
  );
END;
$$;

-- Add a comment to document the harmonization
COMMENT ON FUNCTION calculate_order_production_costs IS 
'Harmonized manual trigger function that calculates production costs WITH detailed breakdown data. Uses calculate_item_production_cost_with_breakdown to ensure breakdown data is created alongside production costs.';