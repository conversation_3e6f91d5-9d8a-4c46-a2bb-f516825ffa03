import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../../../components/ui/card'
import { MetricBadge } from '../../../../../components/ui/metric-badge'
import { Checkbox } from '../../../../../components/ui/checkbox'
import { Button } from '../../../../../components/ui/button'
import { ChevronDown, ChevronRight, Shield, Users, Settings, BarChart3, Package, ShoppingCart, UserCheck, Lock } from 'lucide-react'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '../../../../../components/ui/collapsible'
import { getPermissionsByCategory, type PermissionCategory } from '../../CreateUserSheet/utils/permissionUtils'
import type { EditUserFormData } from '../types'

interface PermissionsTabProps {
  formData: EditUserFormData
  onPermissionToggle: (permission: string, checked: boolean) => void
}

const getCategoryIcon = (categoryKey: string) => {
  const iconMap: Record<string, React.ComponentType<any>> = {
    system: Settings,
    pages: Users,
    orders: ShoppingCart,
    products: Package,
    clients: UserCheck,
    analytics: BarChart3,
    production_cost: Shield,
  }
  return iconMap[categoryKey] || Users
}

export function PermissionsTab({ 
  formData, 
  onPermissionToggle 
}: PermissionsTabProps) {
  const permissionCategories = getPermissionsByCategory()
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set(['system', 'pages']))

  const toggleCategory = (categoryKey: string) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(categoryKey)) {
      newExpanded.delete(categoryKey)
    } else {
      newExpanded.add(categoryKey)
    }
    setExpandedCategories(newExpanded)
  }

  const getCategorySelectedCount = (category: PermissionCategory) => {
    return category.permissions.filter(p => formData.permissions.includes(p)).length
  }

  const toggleAllCategoryPermissions = (category: PermissionCategory) => {
    const selectedCount = getCategorySelectedCount(category)
    const shouldSelectAll = selectedCount < category.permissions.length
    
    category.permissions.forEach(permission => {
      const isSelected = formData.permissions.includes(permission)
      if (shouldSelectAll && !isSelected) {
        onPermissionToggle(permission, true)
      } else if (!shouldSelectAll && isSelected) {
        onPermissionToggle(permission, false)
      }
    })
  }

  return (
    <div className="space-y-6">
      {/* Custom Permissions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            User Permissions
            <MetricBadge value={formData.permissions.length} variant="primary" className="text-xs" />
          </CardTitle>
          <CardDescription>
            Manage individual permissions and access capabilities. Categories are ordered by access level.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            {permissionCategories.map((category) => {
              const IconComponent = getCategoryIcon(category.key)
              const selectedCount = getCategorySelectedCount(category)
              const isExpanded = expandedCategories.has(category.key)
              const isFullySelected = selectedCount === category.permissions.length
              const isPartiallySelected = selectedCount > 0 && selectedCount < category.permissions.length
              const isDeactivated = !category.isActive

              return (
                <Collapsible 
                  key={category.key}
                  open={isExpanded && !isDeactivated}
                  onOpenChange={() => !isDeactivated && toggleCategory(category.key)}
                >
                  <Card className={`transition-colors ${
                    isDeactivated 
                      ? 'border-gray-200 bg-gray-50/50 opacity-60' 
                      : isPartiallySelected 
                        ? 'border-blue-200 bg-blue-50/30' 
                        : isFullySelected 
                          ? 'border-green-200 bg-green-50/30' 
                          : ''
                  }`}>
                    <CollapsibleTrigger asChild>
                      <CardHeader className={`transition-colors pb-3 ${
                        isDeactivated 
                          ? 'cursor-not-allowed' 
                          : 'cursor-pointer hover:bg-gray-50/50'
                      }`}>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className={`h-8 w-8 rounded-full flex items-center justify-center ${
                              isDeactivated ? 'bg-gray-100' : 'bg-gray-100'
                            }`}>
                              {isDeactivated ? (
                                <Lock className="h-4 w-4 text-gray-400" />
                              ) : (
                                <IconComponent className="h-4 w-4 text-gray-600" />
                              )}
                            </div>
                            <div>
                              <CardTitle className={`text-base flex items-center gap-2 ${
                                isDeactivated ? 'text-gray-500' : ''
                              }`}>
                                {category.title}
                                {isDeactivated ? (
                                  <span className="bg-gray-200 text-gray-500 text-xs font-medium px-2 py-1 rounded-md">
                                    Coming Soon
                                  </span>
                                ) : (
                                  <MetricBadge 
                                    value={selectedCount} 
                                    label={`of ${category.permissions.length}`}
                                    variant={isFullySelected ? "success" : isPartiallySelected ? "warning" : "secondary"} 
                                    className="text-xs" 
                                  />
                                )}
                              </CardTitle>
                              <CardDescription className={`text-xs mt-1 mb-0 ${
                                isDeactivated ? 'text-gray-400' : ''
                              }`}>
                                {isDeactivated 
                                  ? `${category.description} (Currently disabled)`
                                  : category.description
                                }
                              </CardDescription>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {!isDeactivated && category.permissions.length > 1 && (
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  toggleAllCategoryPermissions(category)
                                }}
                                className="text-xs h-7 px-2"
                              >
                                {isFullySelected ? 'Deselect All' : 'Select All'}
                              </Button>
                            )}
                            {!isDeactivated && (
                              isExpanded ? (
                                <ChevronDown className="h-4 w-4 text-gray-500" />
                              ) : (
                                <ChevronRight className="h-4 w-4 text-gray-500" />
                              )
                            )}
                          </div>
                        </div>
                      </CardHeader>
                    </CollapsibleTrigger>
                    
                    {!isDeactivated && (
                      <CollapsibleContent>
                        <CardContent className="pt-0 space-y-2">
                          <div className="grid grid-cols-1 gap-3">
                            {category.permissions.map((permission) => {
                              const isSelected = formData.permissions.includes(permission)
                              return (
                                <div 
                                  key={permission} 
                                  className={`flex items-start space-x-3 p-3 rounded-lg border transition-colors cursor-pointer hover:bg-gray-50 ${
                                    isSelected 
                                      ? 'border-black bg-black text-white hover:bg-gray-900' 
                                      : 'border-gray-200 bg-white hover:border-gray-300'
                                  }`}
                                  onClick={() => onPermissionToggle(permission, !isSelected)}
                                >
                                  <Checkbox
                                    id={permission}
                                    checked={isSelected}
                                    onCheckedChange={(checked) => 
                                      onPermissionToggle(permission, checked as boolean)
                                    }
                                    className={`mt-0.5 ${
                                      isSelected 
                                        ? 'border-white data-[state=checked]:bg-white data-[state=checked]:text-black' 
                                        : 'border-gray-400'
                                    }`}
                                    onClick={(e) => e.stopPropagation()}
                                  />
                                  <div className="flex-1 min-w-0">
                                    <label 
                                      htmlFor={permission} 
                                      className={`text-sm font-medium cursor-pointer block mb-1 ${
                                        isSelected ? 'text-white' : 'text-gray-900'
                                      }`}
                                    >
                                      {permission.split('.').pop()?.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                    </label>
                                    <p className={`text-xs ${isSelected ? 'text-gray-200' : 'text-gray-500'} mb-0`}>
                                      {permission}
                                    </p>
                                  </div>
                                </div>
                              )
                            })}
                          </div>
                        </CardContent>
                      </CollapsibleContent>
                    )}
                  </Card>
                </Collapsible>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}