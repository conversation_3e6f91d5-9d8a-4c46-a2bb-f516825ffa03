import React, { useState, useEffect, useRef } from 'react'
import { usePermissions } from '../../hooks/permissions'
import { useAdminData } from '../../hooks/admin'
import { PERMISSIONS } from '../../types/permissions.types'
import { useToast } from '../../hooks/use-toast'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '../../components/ui/tabs'
import { AdminErrorBoundary } from '../../components/ui/admin-error-boundary'
import { UserManagementTab } from './components/UserManagementTab'
import { PermissionsTab } from './components/PermissionsTab'
import { Badge } from '../../components/ui/badge'
import { StatusBadge } from '../../components/ui/status-badge'
import { MetricBadge } from '../../components/ui/metric-badge'
import { 
  Users,
  Shield,
  AlertCircle 
} from 'lucide-react'

export default function AdminSettings() {
  const { checkPermission } = usePermissions()
  const { toast } = useToast()
  
  const [activeTab, setActiveTab] = useState('users')

  // Check admin permissions
  const hasUserManagement = checkPermission('admin.users')
  const hasUserCreate = checkPermission('admin.users')
  const hasPermissionAssign = checkPermission('admin.permissions')
  const hasFullAccess = checkPermission('system.admin')

  // Use SWR for data fetching (only fetch if user has permissions)
  const { 
    users, 
    userStats, 
    totalUsers, 
    activeUsers, 
    error, 
    isLoading, 
    refresh 
  } = useAdminData(
    { limit: 100 },
    { enabled: hasUserManagement }
  )

  // Track error notifications to prevent duplicates
  const lastErrorRef = useRef<Error | null>(null)

  // Handle SWR errors with user feedback (only once per error)
  useEffect(() => {
    if (error && hasUserManagement && error !== lastErrorRef.current) {
      lastErrorRef.current = error
      toast({
        title: 'Error Loading Admin Data',
        description: error instanceof Error ? error.message : 'Failed to load admin data',
        variant: 'destructive'
      })
    }
  }, [error, hasUserManagement, toast])

  // Access control check
  if (!hasUserManagement) {
    return (
      <div className="container mx-auto py-8 px-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <AlertCircle className="h-5 w-5" />
              Access Denied
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              You don't have permission to access the admin settings. 
              Contact your administrator if you need access to user management features.
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading admin settings...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error && !users.length) {
    return (
      <div className="container mx-auto py-8 px-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <AlertCircle className="h-5 w-5" />
              Error Loading Admin Settings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              {error instanceof Error ? error.message : 'Failed to load admin data'}
            </p>
            <button 
              onClick={() => refresh()}
              className="px-4 py-2 bg-gray-700 text-white rounded hover:bg-gray-800"
            >
              Retry
            </button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight mb-2">Admin Settings</h1>
        <div className="flex items-center gap-2">
          <p className="text-muted-foreground">
            Manage users, roles, and system permissions.
          </p>
          {hasFullAccess && (
            <StatusBadge status="admin" className="ml-2">
              Full Access
            </StatusBadge>
          )}
        </div>
      </div>

      {/* Admin Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            User Management
          </TabsTrigger>
          <TabsTrigger 
            value="permissions" 
            className="flex items-center gap-2 opacity-60 cursor-not-allowed"
            disabled={true}
          >
            <Shield className="h-4 w-4" />
            Role Templates
            <Badge variant="secondary" className="ml-2 text-xs">
              Coming Soon
            </Badge>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="users">
          <AdminErrorBoundary
            onReset={() => refresh()}
            resetKeys={[users.length, activeTab]}
          >
            <UserManagementTab
              users={users}
              onRefresh={refresh}
              canCreate={hasUserCreate}
              canEdit={hasPermissionAssign}
              canDelete={hasFullAccess}
            />
          </AdminErrorBoundary>
        </TabsContent>

        <TabsContent value="permissions">
          <PermissionsTab
            canManageTemplates={hasPermissionAssign}
            hasFullAccess={hasFullAccess}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}