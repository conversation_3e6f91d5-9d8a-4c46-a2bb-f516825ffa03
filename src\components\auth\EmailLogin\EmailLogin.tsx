import React from 'react';
import { ErrorMessage, SuccessMessage } from '../../ui/message-display';
import { EmailStep } from './components/EmailStep';
import { PasswordStep } from './components/PasswordStep';
import { useEmailLogin } from './hooks/useEmailLogin';

interface EmailLoginProps {
  className?: string;
}

/**
 * EmailLogin component with adaptive authentication flow
 * 
 * Shows different auth methods based on user type:
 * - Admin users: Email -> Password
 * - Regular users: Email -> PIN
 */
const EmailLogin: React.FC<EmailLoginProps> = ({ className = '' }) => {
  const {
    // State
    email,
    password,
    currentStep,
    userType,
    userState,
    loading,
    error,
    success,

    // Actions
    setPassword,
    handleEmailChange,
    handleEmailSubmit,
    handlePasswordLogin,
    clearMessages
  } = useEmailLogin();

  // Handle form submission based on current step
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    switch (currentStep) {
      case 'email':
        handleEmailSubmit();
        break;
      case 'password':
        handlePasswordLogin();
        break;
    }
  };

  return (
    <form onSubmit={handleSubmit} className={`space-y-4 ${className}`}>
      {/* Email Step */}
      {currentStep === 'email' && (
        <EmailStep
          email={email}
          onEmailChange={handleEmailChange}
          onSubmit={handleEmailSubmit}
          loading={loading}
        />
      )}

      {/* Password/PIN Step */}
      {currentStep === 'password' && (
        <PasswordStep
          email={email}
          password={password}
          userType={userType}
          onPasswordChange={setPassword}
          onSubmit={handlePasswordLogin}
          loading={loading}
          clearMessages={clearMessages}
        />
      )}

      {/* Error Display */}
      {error && (
        <ErrorMessage 
          message={error}
          subtitle={userState && !userState.canLogin ? 
            `State: ${userState.state.replace('_', ' ')} • ${userState.nextAction}` : 
            undefined
          }
          className="mt-4"
        />
      )}

      {/* Success Display */}
      {success && (
        <SuccessMessage 
          message={success}
          subtitle={userState ? `Account Status: ${userState.message}` : undefined}
          className="mt-4"
        />
      )}
    </form>
  );
};

export default EmailLogin;