import React, { useState, useEffect } from 'react'
import { Settings as SettingsIcon, User, Bell, Lock, Database, CreditCard, AlertTriangle, Plus, Trash2, Edit3, Eye, MessageSquare, CheckCircle, RefreshCw } from 'lucide-react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '../../components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import { Textarea } from '../../components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select'
import { Switch } from '../../components/ui/switch'
import { Badge } from '../../components/ui/badge'
import { useToast } from '../../hooks/use-toast'
import { DynamicAnnouncementsService, type DatabaseAnnouncement } from '../../services/dynamicAnnouncements.service'
import { useAnnouncements } from '../../contexts/AnnouncementContext'
import { PermissionGuardV2 } from '../../components/permissions/PermissionGuardV2'
import { useAuth } from '../../contexts/AuthContext'
import { updateProfile } from '../../services/profileService'
import { ProfileSection, AnnouncementsSection } from './components'

const Settings = () => {
  const [activeTab, setActiveTab] = useState('profile')
  const [announcements, setAnnouncements] = useState<DatabaseAnnouncement[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const { toast } = useToast()
  const { announcements: contextAnnouncements } = useAnnouncements()
  const { user, profile, profileLoading, refreshProfile } = useAuth()

  // Profile form state
  const [profileForm, setProfileForm] = useState({
    firstName: '',
    lastName: '',
    bio: '',
    phone: '',
    avatar_url: ''
  })
  const [isProfileSaving, setIsProfileSaving] = useState(false)

  // New announcement form state
  const [newAnnouncement, setNewAnnouncement] = useState({
    title: '',
    message: '',
    type: 'info' as 'info' | 'warning' | 'success' | 'update' | 'new' | 'error',
    priority: 'medium' as 'low' | 'medium' | 'high' | 'critical',
    category: 'general',
    target_audience: ['admin'] as string[],
    display_until: '',
    is_dismissible: true,
    auto_dismiss_after_seconds: 30
  })

  // Initialize profile form when profile loads
  useEffect(() => {
    if (profile) {
      const nameParts = profile.full_name?.split(' ') || ['', '']
      setProfileForm({
        firstName: nameParts[0] || '',
        lastName: nameParts.slice(1).join(' ') || '',
        bio: '', // Add bio field to profile if needed
        phone: '', // Add phone field to profile if needed
        avatar_url: profile.avatar_url || ''
      })
    }
  }, [profile])

  // Load announcements when Announcements tab is selected
  useEffect(() => {
    if (activeTab === 'announcements') {
      loadAnnouncements()
    }
  }, [activeTab])

  const loadAnnouncements = async () => {
    setIsLoading(true)
    try {
      // For now, we'll get active announcements and show basic info
      // In a real implementation, we'd have an admin endpoint to get all announcements
      const active = await DynamicAnnouncementsService.fetchActiveAnnouncements('admin')
      // Convert to DatabaseAnnouncement format for display
      const dbFormat = active.map(a => ({
        id: a.id || '',
        title: a.title,
        message: a.message,
        type: a.type,
        priority: a.priority,
        category: 'general',
        source_component: 'manual',
        target_audience: ['admin'],
        display_until: a.expiresAt?.toISOString(),
        is_active: true,
        is_dismissible: true,
        auto_dismiss_after_seconds: 30,
        error_context: null,
        related_entity_type: null,
        related_entity_id: null,
        created_at: a.createdAt.toISOString(),
        view_count: 0,
        dismiss_count: 0
      } as DatabaseAnnouncement))
      setAnnouncements(dbFormat)
    } catch (error) {
      console.error('Failed to load announcements:', error)
      toast({
        title: "Error",
        description: "Failed to load announcements",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateTestAnnouncement = async () => {
    try {
      await DynamicAnnouncementsService.createTestCalculationRulesAnnouncement()
      toast({
        title: "Success",
        description: "Test announcement created successfully",
      })
      loadAnnouncements()
    } catch (error) {
      toast({
        title: "Error", 
        description: "Failed to create test announcement",
        variant: "destructive"
      })
    }
  }

  const handleProfileFormChange = (field: string, value: string) => {
    setProfileForm(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSaveProfile = async () => {
    if (!user?.id) {
      toast({
        title: "Error",
        description: "You must be logged in to update your profile",
        variant: "destructive"
      })
      return
    }

    setIsProfileSaving(true)
    try {
      const fullName = `${profileForm.firstName} ${profileForm.lastName}`.trim()
      const updatedProfile = await updateProfile(user.id, {
        full_name: fullName || null,
        avatar_url: profileForm.avatar_url || null
      })

      if (updatedProfile) {
        toast({
          title: "Success",
          description: "Profile updated successfully"
        })
        refreshProfile()
      } else {
        throw new Error('Failed to update profile')
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update profile",
        variant: "destructive"
      })
    } finally {
      setIsProfileSaving(false)
    }
  }

  return (
    <PermissionGuardV2 permission="settings.view" fallback={
      <div className="flex flex-col items-center justify-center py-12">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-orange-500 mx-auto mb-4" />
          <h2 className="text-lg font-semibold mb-2">Access Restricted</h2>
          <p className="text-gray-600 mb-0">You don't have permission to access Settings.</p>
        </div>
      </div>
    }>
      <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center mb-4">
        <div className="bg-[#F0ECFD] p-2 rounded-md mr-4">
          <SettingsIcon className="w-5 h-5 text-[#613AEB]" />
        </div>
        <div>
          <h1 className="text-xl font-bold text-gray-800">Settings</h1>
          <p className="text-gray-600 text-sm">Manage your account and system settings</p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="profile" className="flex items-center gap-2">
            <User className="w-4 h-4" />
            Profile
          </TabsTrigger>
          <TabsTrigger value="announcements" className="flex items-center gap-2">
            <Bell className="w-4 h-4" />
            Announcements
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-2">
            <Lock className="w-4 h-4" />
            Security
          </TabsTrigger>
          <TabsTrigger value="data" className="flex items-center gap-2">
            <Database className="w-4 h-4" />
            Data
          </TabsTrigger>
          <TabsTrigger value="billing" className="flex items-center gap-2">
            <CreditCard className="w-4 h-4" />
            Billing
          </TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-6">
          <ProfileSection
            profile={profile}
            user={user}
            profileLoading={profileLoading}
            profileForm={profileForm}
            isProfileSaving={isProfileSaving}
            onProfileFormChange={handleProfileFormChange}
            onSaveProfile={handleSaveProfile}
          />

          <Card>
            <CardHeader>
              <CardTitle>Company Information</CardTitle>
              <CardDescription>Manage your company details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="companyName">Company Name</Label>
                  <Input id="companyName" defaultValue="Aming Trading" />
                </div>
                <div>
                  <Label htmlFor="industry">Industry</Label>
                  <Input id="industry" defaultValue="Retail" />
                </div>
                <div>
                  <Label htmlFor="companyEmail">Company Email</Label>
                  <Input id="companyEmail" type="email" defaultValue="<EMAIL>" />
                </div>
                <div>
                  <Label htmlFor="companyPhone">Company Phone</Label>
                  <Input id="companyPhone" type="tel" defaultValue="+****************" />
                </div>
                <div className="md:col-span-2">
                  <Label htmlFor="address">Address</Label>
                  <Input id="address" defaultValue="123 Business St, Suite 456" />
                </div>
                <div>
                  <Label htmlFor="city">City</Label>
                  <Input id="city" defaultValue="San Francisco" />
                </div>
                <div>
                  <Label htmlFor="country">Country</Label>
                  <Input id="country" defaultValue="United States" />
                </div>
              </div>

              <div className="flex justify-end">
                <Button>Save Changes</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="announcements" className="space-y-6">
          <AnnouncementsSection
            announcements={announcements}
            contextAnnouncements={contextAnnouncements}
            isLoading={isLoading}
            onCreateTestAnnouncement={handleCreateTestAnnouncement}
            onLoadAnnouncements={loadAnnouncements}
          />
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
              <CardDescription>Manage your account security</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500">Security settings coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="data" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Data Management</CardTitle>
              <CardDescription>Manage your data and exports</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500">Data management features coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="billing" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Billing & Subscription</CardTitle>
              <CardDescription>Manage your subscription and billing</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500">Billing features coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      </div>
    </PermissionGuardV2>
  )
}

export default Settings
