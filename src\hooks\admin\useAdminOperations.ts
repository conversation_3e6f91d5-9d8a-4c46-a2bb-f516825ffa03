import { mutate } from 'swr'
import { ADMIN_KEYS } from './useAdminData'
import type { AuthorizedUser } from '../../services/admin'

/**
 * Admin Operations Hook
 * 
 * Provides optimistic cache updates for admin operations.
 * Now that RLS circular dependencies are fixed, no special guards needed.
 * 
 * CRITICAL: Must use correct filter parameters to match component cache keys
 */
export function useAdminOperations(
  filters: { limit?: number; offset?: number; search?: string; active?: boolean } = { limit: 100 }
) {
  
  /**
   * Update user in SWR cache with optimistic update
   * DISABLED: Edit functionality has been removed
   */
  const updateUserInCache = async (updatedUser: AuthorizedUser) => {
    console.log('updateUserInCache: Edit functionality has been disabled')
    // No longer performs any cache updates
    return Promise.resolve()
  }
  
  /**
   * Add user to SWR cache with optimistic update
   */
  const addUserToCache = async (newUser: AuthorizedUser) => {
    // Update SWR cache optimistically with correct filter-aware cache key
    await mutate(
      ADMIN_KEYS.combined(filters),
      async (currentData) => {
        if (!currentData) return currentData
        
        return {
          ...currentData,
          users: [newUser, ...currentData.users],
          totalUsers: currentData.totalUsers + 1,
          activeUsers: newUser.is_active ? currentData.activeUsers + 1 : currentData.activeUsers
        }
      },
      { revalidate: false } // Don't refetch - use our optimistic update
    )
  }
  
  return {
    updateUserInCache,
    addUserToCache
  }
}