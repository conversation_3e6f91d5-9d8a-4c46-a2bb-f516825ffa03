import React, { memo } from 'react'
import type { AuthorizedUser } from '../../../../../services/admin'
import { UserTableRow } from './UserTableRow'

interface MemoizedUserTableRowProps {
  user: AuthorizedUser
  onEdit: (user: AuthorizedUser) => void
  onToggleStatus: (user: AuthorizedUser) => void
  onDelete: (user: AuthorizedUser) => void
  onPermissionUpdate: (userId: string, permissions: string[]) => Promise<void>
  processingUserId: string | null
  canEdit: boolean
  canDelete: boolean
}

// Memoized version of UserTableRow to prevent unnecessary re-renders
export const MemoizedUserTableRow = memo<MemoizedUserTableRowProps>(
  function MemoizedUserTableRow({
    user,
    onEdit,
    onToggleStatus,
    onDelete,
    onPermissionUpdate,
    processingUserId,
    canEdit,
    canDelete
  }) {
    return (
      <UserTableRow
        user={user}
        onEdit={onEdit}
        onToggleStatus={onToggleStatus}
        onDelete={onDelete}
        onPermissionUpdate={onPermissionUpdate}
        processingUserId={processingUserId}
        canEdit={canEdit}
        canDelete={canDelete}
      />
    )
  },
  (prevProps, nextProps) => {
    // Quick comparison - if it's the same object reference, no need to re-render
    if (prevProps.user === nextProps.user && 
        prevProps.processingUserId === nextProps.processingUserId) {
      return true
    }
    
    // Only do detailed comparison if objects are different references
    return (
      prevProps.user.id === nextProps.user.id &&
      prevProps.user.first_name === nextProps.user.first_name &&
      prevProps.user.last_name === nextProps.user.last_name &&
      prevProps.user.email === nextProps.user.email &&
      prevProps.user.is_active === nextProps.user.is_active &&
      prevProps.user.department === nextProps.user.department &&
      prevProps.user.permissions.length === nextProps.user.permissions.length &&
      prevProps.user.permissions.every(p => nextProps.user.permissions.includes(p)) &&
      prevProps.processingUserId === nextProps.processingUserId &&
      prevProps.canEdit === nextProps.canEdit &&
      prevProps.canDelete === nextProps.canDelete
    )
  }
)