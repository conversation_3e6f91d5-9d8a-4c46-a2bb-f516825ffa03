-- Basic RLS Security Implementation - Phase 1
-- Remove wide-open policies and implement basic authenticated user security
-- Following CLAUDE.md guidelines - focused migration

-- ============================================================================
-- STEP 1: Remove existing wide-open policies
-- ============================================================================

-- Remove permissive policies from clients table
DROP POLICY IF EXISTS "Allow anonymous users to delete clients" ON clients;
DROP POLICY IF EXISTS "Allow anonymous users to insert clients" ON clients;
DROP POLICY IF EXISTS "Allow anonymous users to read clients" ON clients;
DROP POLICY IF EXISTS "Allow anonymous users to update clients" ON clients;
DROP POLICY IF EXISTS "Allow authenticated users to delete clients" ON clients;
DROP POLICY IF EXISTS "Allow authenticated users to insert clients" ON clients;
DROP POLICY IF EXISTS "Allow authenticated users to read clients" ON clients;
DROP POLICY IF EXISTS "Allow authenticated users to update clients" ON clients;

-- Remove permissive policies from other tables
DROP POLICY IF EXISTS "Allow all operations on calculation_templates" ON calculation_templates;
DROP POLICY IF EXISTS "Allow all operations on template_applications" ON template_applications;
DROP POLICY IF EXISTS "Allow all operations on template_calculation_results" ON template_calculation_results;

-- ============================================================================
-- STEP 2: Create basic authorization functions
-- ============================================================================

-- Basic function to check if user is authenticated
CREATE OR REPLACE FUNCTION is_authenticated_user()
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN auth.role() = 'authenticated' AND auth.uid() IS NOT NULL;
END;
$$;

-- Function to get current user email from JWT
CREATE OR REPLACE FUNCTION get_current_user_email()
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN auth.jwt() ->> 'email';
END;
$$;

-- Basic function to check if user exists in authorized_users
CREATE OR REPLACE FUNCTION is_authorized_user()
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_email text;
BEGIN
  -- Get current user email
  user_email := get_current_user_email();
  
  -- Return false if no email
  IF user_email IS NULL THEN
    RETURN false;
  END IF;
  
  -- Check if user exists in authorized_users and is active
  RETURN EXISTS (
    SELECT 1 
    FROM authorized_users au
    WHERE au.email = user_email
    AND au.is_active = true
  );
END;
$$;

-- ============================================================================
-- STEP 3: Enable RLS on core tables
-- ============================================================================

-- Enable RLS on core application tables that don't have it yet
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE notes ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- STEP 4: Create basic security policies - authenticated and authorized users only
-- ============================================================================

-- Orders policies - authenticated and authorized users
CREATE POLICY "orders_authenticated_access" ON orders
  FOR ALL TO authenticated
  USING (is_authorized_user());

-- Order items policies
CREATE POLICY "order_items_authenticated_access" ON order_items
  FOR ALL TO authenticated
  USING (is_authorized_user());

-- Order payments policies
CREATE POLICY "order_payments_authenticated_access" ON order_payments
  FOR ALL TO authenticated
  USING (is_authorized_user());

-- Products policies - shared organizational data
CREATE POLICY "products_authenticated_access" ON products
  FOR ALL TO authenticated
  USING (is_authorized_user());

-- Profiles policies - users can manage their own profile
CREATE POLICY "profiles_own_profile" ON profiles
  FOR ALL TO authenticated
  USING ((SELECT auth.uid()) = id);

-- Notes policies - users access their own notes
CREATE POLICY "notes_user_access" ON notes
  FOR ALL TO authenticated
  USING (is_authorized_user() AND (SELECT auth.uid()) = user_id);

-- Clients policies - replace wide-open with secure policies
CREATE POLICY "clients_authenticated_access" ON clients
  FOR ALL TO authenticated
  USING (is_authorized_user());

-- Secure calculation templates - authorized users only
CREATE POLICY "calculation_templates_authorized_access" ON calculation_templates
  FOR ALL TO authenticated
  USING (is_authorized_user());

-- Secure template applications - authorized users only
CREATE POLICY "template_applications_authorized_access" ON template_applications
  FOR ALL TO authenticated
  USING (is_authorized_user());

-- Secure template calculation results - authorized users only
CREATE POLICY "template_calculation_results_authorized_access" ON template_calculation_results
  FOR ALL TO authenticated
  USING (is_authorized_user());

-- ============================================================================
-- STEP 5: System tables - read-only access for authorized users
-- ============================================================================

-- Permissions table - read-only for authorized users
CREATE POLICY "permissions_read_only" ON permissions
  FOR SELECT TO authenticated
  USING (is_authorized_user());

-- Roles table - read-only for authorized users  
CREATE POLICY "roles_read_only" ON roles
  FOR SELECT TO authenticated
  USING (is_authorized_user());

-- ============================================================================
-- STEP 6: Create indexes for RLS performance
-- ============================================================================

-- Add indexes on columns used in RLS policies for performance
CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id);
CREATE INDEX IF NOT EXISTS idx_notes_user_id ON notes(user_id);
CREATE INDEX IF NOT EXISTS idx_profiles_id ON profiles(id);

-- Comment on the migration
COMMENT ON FUNCTION is_authenticated_user() IS 'Basic function to verify user is authenticated';
COMMENT ON FUNCTION is_authorized_user() IS 'Function to verify user is in authorized_users table and active';
COMMENT ON FUNCTION get_current_user_email() IS 'Helper function to get current user email from JWT';