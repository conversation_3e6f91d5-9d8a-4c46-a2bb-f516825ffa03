-- SAFE Migration: Profile Enhancements
-- Date: 2025-01-11  
-- Purpose: Safely add missing department column and enhance existing trigger function
-- SAFETY: This migration only ADDS features, never drops or modifies existing data

-- Add missing department column (safe - only adds, doesn't modify existing data)
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS department TEXT;

-- Update existing function to handle department and improved name logic (safe replacement)
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
BEGIN
    -- Extract names from user metadata with improved logic
    DECLARE
        first_name TEXT := COALESCE(NEW.raw_user_meta_data->>'first_name', '');
        last_name TEXT := COALESCE(NEW.raw_user_meta_data->>'last_name', '');
        full_name_computed TEXT;
    BEGIN
        -- Construct full name intelligently
        IF first_name != '' AND last_name != '' THEN
            full_name_computed := first_name || ' ' || last_name;
        ELSIF first_name != '' THEN
            full_name_computed := first_name;
        ELSIF last_name != '' THEN
            full_name_computed := last_name;
        ELSE
            -- Fallback to existing full_name or email username
            full_name_computed := COALESCE(
                NEW.raw_user_meta_data->>'full_name',
                SPLIT_PART(NEW.email, '@', 1)
            );
        END IF;

        -- Insert profile record with all fields
        INSERT INTO public.profiles (
            id,
            email,
            full_name,
            avatar_url,
            role,
            department,
            created_at,
            updated_at
        ) VALUES (
            NEW.id,
            NEW.email,
            full_name_computed,
            NEW.raw_user_meta_data->>'avatar_url',
            COALESCE(NEW.raw_user_meta_data->>'role', 'user'),
            NEW.raw_user_meta_data->>'department',
            NOW(),
            NOW()
        );

        RETURN NEW;
    END;
END;
$$;

-- Add user update trigger function (safe - creates new function)
CREATE OR REPLACE FUNCTION public.handle_user_update()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
BEGIN
    -- Only update if metadata has actually changed (efficiency)
    IF OLD.raw_user_meta_data IS DISTINCT FROM NEW.raw_user_meta_data THEN
        DECLARE
            first_name TEXT := COALESCE(NEW.raw_user_meta_data->>'first_name', '');
            last_name TEXT := COALESCE(NEW.raw_user_meta_data->>'last_name', '');
            full_name_computed TEXT;
        BEGIN
            -- Construct full name intelligently
            IF first_name != '' AND last_name != '' THEN
                full_name_computed := first_name || ' ' || last_name;
            ELSIF first_name != '' THEN
                full_name_computed := first_name;
            ELSIF last_name != '' THEN
                full_name_computed := last_name;
            ELSE
                -- Keep existing full_name if no names in metadata
                full_name_computed := COALESCE(
                    NEW.raw_user_meta_data->>'full_name',
                    (SELECT full_name FROM public.profiles WHERE id = NEW.id)
                );
            END IF;

            -- Update profile record (only if it exists)
            UPDATE public.profiles SET
                email = NEW.email,
                full_name = COALESCE(full_name_computed, full_name),
                avatar_url = COALESCE(NEW.raw_user_meta_data->>'avatar_url', avatar_url),
                role = COALESCE(NEW.raw_user_meta_data->>'role', role),
                department = COALESCE(NEW.raw_user_meta_data->>'department', department),
                updated_at = NOW()
            WHERE id = NEW.id;
        END;
    END IF;

    RETURN NEW;
END;
$$;

-- Add update trigger (safe - only adds if not exists)
DROP TRIGGER IF EXISTS on_auth_user_updated ON auth.users;
CREATE TRIGGER on_auth_user_updated
    AFTER UPDATE ON auth.users
    FOR EACH ROW 
    EXECUTE FUNCTION public.handle_user_update();

-- Create helpful indexes (safe - only improves performance)
CREATE INDEX IF NOT EXISTS profiles_email_idx ON public.profiles(email);
CREATE INDEX IF NOT EXISTS profiles_role_idx ON public.profiles(role);
CREATE INDEX IF NOT EXISTS profiles_department_idx ON public.profiles(department);

-- Add helpful comments
COMMENT ON COLUMN public.profiles.department IS 'User department from authorization system';
COMMENT ON FUNCTION public.handle_new_user() IS 'Enhanced: Creates user profile with intelligent name construction';
COMMENT ON FUNCTION public.handle_user_update() IS 'New: Syncs user profile when auth metadata changes';

-- Verify the migration worked
SELECT 
    'Migration completed successfully. Profiles table enhanced.' as status,
    COUNT(*) as existing_profiles,
    column_name
FROM information_schema.columns 
WHERE table_name = 'profiles' 
  AND table_schema = 'public'
  AND column_name = 'department',
(SELECT COUNT(*) FROM public.profiles) as profile_count;