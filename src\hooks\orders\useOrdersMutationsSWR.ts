import useSWRMutation from 'swr/mutation'
import {
  addOrder as addOrderTo<PERSON><PERSON>,
  updateOrder as updateOrderIn<PERSON><PERSON>,
  deleteOrder as deleteOrder<PERSON>rom<PERSON><PERSON>
} from '../../services'
import type { Order } from '../../pages/Orders/types'
import { logger } from '../../utils/logger'
import { getOrdersKey } from './useOrdersBasic'

// Fetcher functions for useSWRMutation
const createOrderFetcher = async (url: string, { arg }: { arg: Omit<Order, 'id'> }) => {
  const newOrder = await addOrderToApi(arg)
  return newOrder
}

const updateOrderFetcher = async (url: string, { arg }: { arg: { id: string; data: Partial<Order> } }) => {
  const { id, data } = arg
  const updatedOrder = await updateOrderInApi({ ...data, id } as Order)
  return { ...updatedOrder, id }
}

const deleteOrderFetcher = async (url: string, { arg }: { arg: string }) => {
  await deleteOrderFrom<PERSON>pi(arg)
  return arg
}

/**
 * Modern SWR mutation hooks for orders with optimistic updates
 */
export function useOrderMutations() {
  
  // Create order mutation
  const { 
    trigger: createOrder, 
    isMutating: isCreating,
    error: createError 
  } = useSWRMutation(
    getOrdersKey(),
    createOrderFetcher,
    {
      // Optimistic update: add new order immediately
      optimisticData: (currentOrders: Order[] = [], newOrderData: Omit<Order, 'id'>) => {
        const tempOrder: Order = {
          ...newOrderData,
          id: `temp-${Date.now()}`,
          orderNo: 'Pending...'
        }
        return [tempOrder, ...currentOrders]
      },
      
      // Rollback on error
      rollbackOnError: true,
      
      // Update cache with server response
      populateCache: (result: Order, currentOrders: Order[] = []) => {
        // Remove any temporary orders and add the real one
        const filteredOrders = currentOrders.filter(
          order => !order.id.startsWith('temp-') && order.id !== result.id
        )
        return [result, ...filteredOrders]
      },
      
      // Revalidate after successful mutation
      revalidate: false, // We're using optimistic updates
      
      onSuccess: (data) => {
        logger.debug('Order created successfully:', data.id)
      },
      
      onError: (error) => {
        logger.error('Failed to create order:', error)
      }
    }
  )

  // Update order mutation
  const { 
    trigger: updateOrder, 
    isMutating: isUpdating,
    error: updateError 
  } = useSWRMutation(
    getOrdersKey(),
    updateOrderFetcher,
    {
      // Optimistic update: update order immediately
      optimisticData: (currentOrders: Order[] = [], { id, data }: { id: string; data: Partial<Order> }) => {
        return currentOrders.map(order =>
          order.id === id 
            ? { ...order, ...data, id, updatedAt: new Date().toISOString() }
            : order
        )
      },
      
      rollbackOnError: true,
      
      // Update cache with server response
      populateCache: (result: Order, currentOrders: Order[] = []) => {
        return currentOrders.map(order =>
          order.id === result.id ? result : order
        )
      },
      
      revalidate: false,
      
      onSuccess: (data) => {
        logger.debug('Order updated successfully:', data.id)
      },
      
      onError: (error) => {
        logger.error('Failed to update order:', error)
      }
    }
  )

  // Delete order mutation
  const { 
    trigger: deleteOrder, 
    isMutating: isDeleting,
    error: deleteError 
  } = useSWRMutation(
    getOrdersKey(),
    deleteOrderFetcher,
    {
      // Optimistic update: remove order immediately
      optimisticData: (currentOrders: Order[] = [], orderId: string) => {
        return currentOrders.filter(order => order.id !== orderId)
      },
      
      rollbackOnError: true,
      
      // No need to populate cache for delete - optimistic update is sufficient
      populateCache: false,
      
      revalidate: false,
      
      onSuccess: (deletedId) => {
        logger.debug('Order deleted successfully:', deletedId)
      },
      
      onError: (error) => {
        logger.error('Failed to delete order:', error)
      }
    }
  )

  return {
    // Mutation functions
    createOrder: (orderData: Omit<Order, 'id'>) => createOrder(orderData),
    updateOrder: (id: string, data: Partial<Order>) => updateOrder({ id, data }),
    deleteOrder: (id: string) => deleteOrder(id),
    
    // Loading states
    isCreating,
    isUpdating,
    isDeleting,
    isMutating: isCreating || isUpdating || isDeleting,
    
    // Error states
    createError,
    updateError,
    deleteError,
    error: createError || updateError || deleteError
  }
}