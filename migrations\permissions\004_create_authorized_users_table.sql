-- Migration: Create authorized_users table for pre-authorization system
-- Based on industry research: email-only authentication with self-service pre-authorization
-- Date: 2025-01-08
-- Phase: Authentication System Implementation - Phase 1

-- Create authorized_users table (Single Source of Truth for Authorization)
CREATE TABLE IF NOT EXISTS authorized_users (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    email text UNIQUE NOT NULL,
    
    -- Normalized Name Fields (Industry Best Practice)
    first_name text NOT NULL,
    last_name text NOT NULL,
    
    -- Optional Context Fields
    department text,
    
    -- Permission Storage as JSON (Flexible & Fast)
    permissions jsonb DEFAULT '[]'::jsonb,
    role_template text, -- Optional role template reference
    
    -- Account Management
    is_active boolean DEFAULT true,
    invited_by uuid, -- Will reference auth.users(id) after auth system is active
    invited_at timestamptz DEFAULT now(),
    first_login_at timestamptz,
    last_login_at timestamptz,
    
    -- Administrative Fields
    notes text,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    
    -- Constraints
    CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT valid_permissions CHECK (jsonb_typeof(permissions) = 'array'),
    CONSTRAINT valid_names CHECK (LENGTH(TRIM(first_name)) > 0 AND LENGTH(TRIM(last_name)) > 0)
);

-- Create indexes for performance (critical for RLS policies)
CREATE INDEX IF NOT EXISTS idx_authorized_users_email ON authorized_users(email);
CREATE INDEX IF NOT EXISTS idx_authorized_users_active ON authorized_users(is_active);
CREATE INDEX IF NOT EXISTS idx_authorized_users_permissions ON authorized_users USING GIN(permissions);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_authorized_users_updated_at 
    BEFORE UPDATE ON authorized_users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Add helpful comments
COMMENT ON TABLE authorized_users IS 'Pre-authorization table for controlled access. Users must be added here before they can authenticate.';
COMMENT ON COLUMN authorized_users.permissions IS 'JSON array of permission keys. Example: ["orders.view", "clients.delete"]';
COMMENT ON COLUMN authorized_users.role_template IS 'Optional reference to roles table for bulk permission assignment';
COMMENT ON COLUMN authorized_users.invited_by IS 'Admin user who authorized this user (will be FK after auth system active)';