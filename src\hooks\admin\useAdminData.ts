import useSWR from 'swr'
import { AdminService, type AuthorizedUser, type UserStats } from '../../services/admin'

export interface AdminData {
  users: AuthorizedUser[]
  stats: UserStats
  totalUsers: number
  activeUsers: number
}

// SWR keys for admin data
export const ADMIN_KEYS = {
  users: (filters?: { limit?: number; offset?: number; search?: string; active?: boolean }) => 
    ['admin', 'users', filters] as const,
  stats: () => ['admin', 'stats'] as const,
  combined: (filters?: { limit?: number; offset?: number; search?: string; active?: boolean }) =>
    ['admin', 'combined', filters] as const
}

// SWR fetchers
const fetchUsers = async (filters?: { limit?: number; offset?: number; search?: string; active?: boolean }) => {
  const result = await AdminService.getUsers(filters || { limit: 100 })
  return result.users
}

const fetchStats = async () => {
  return AdminService.getUserStats()
}

const fetchCombinedData = async (filters?: { limit?: number; offset?: number; search?: string; active?: boolean }): Promise<AdminData> => {
  const [usersResult, stats] = await Promise.all([
    AdminService.getUsers(filters || { limit: 100 }),
    AdminService.getUserStats()
  ])

  return {
    users: usersResult.users,
    stats,
    totalUsers: usersResult.totalCount,
    activeUsers: stats.active_users
  }
}

// Main SWR hook for admin data
export function useAdminData(
  filters?: { limit?: number; offset?: number; search?: string; active?: boolean },
  options?: { enabled?: boolean }
) {
  const { enabled = true } = options || {}
  
  const { data, error, isLoading, mutate } = useSWR(
    enabled ? ADMIN_KEYS.combined(filters) : null, // null disables fetching
    enabled ? () => fetchCombinedData(filters) : null,
    {
      revalidateOnFocus: false,
      dedupingInterval: 30000,
      keepPreviousData: true,
      errorRetryCount: 3,
      errorRetryInterval: 1000
    }
  )

  return {
    users: data?.users || [],
    userStats: data?.stats || null,
    totalUsers: data?.totalUsers || 0,
    activeUsers: data?.activeUsers || 0,
    error,
    isLoading,
    refresh: async () => {
      // Follow successful patterns: use specific cache key and force revalidation  
      await mutate()
    }
  }
}

// Individual data hooks for more granular control
export function useUsers(filters?: { limit?: number; offset?: number; search?: string; active?: boolean }) {
  const { data, error, isLoading, mutate } = useSWR(
    ADMIN_KEYS.users(filters),
    () => fetchUsers(filters),
    {
      revalidateOnFocus: false,
      dedupingInterval: 30000,
      keepPreviousData: true
    }
  )

  return {
    users: data || [],
    error,
    isLoading,
    refresh: mutate
  }
}

export function useUserStats() {
  const { data, error, isLoading, mutate } = useSWR(
    ADMIN_KEYS.stats(),
    fetchStats,
    {
      revalidateOnFocus: false,
      dedupingInterval: 60000, // Stats change less frequently
      keepPreviousData: true
    }
  )

  return {
    stats: data || null,
    error,
    isLoading,
    refresh: mutate
  }
}