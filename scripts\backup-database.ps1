# =============================================================================
# DATABASE BACKUP SCRIPT - Supabase PostgreSQL (PowerShell)
# =============================================================================
# Creates a complete backup of your Supabase database including:
# - Full schema (tables, functions, triggers, indexes)  
# - All data in all tables
# - Row Level Security (RLS) policies
# - User permissions and roles
#
# Usage: .\scripts\backup-database.ps1
# =============================================================================

# Enable error handling
$ErrorActionPreference = "Stop"

# Colors for output
function Write-Status($message) { Write-Host "[INFO] $message" -ForegroundColor Blue }
function Write-Success($message) { Write-Host "[SUCCESS] $message" -ForegroundColor Green }
function Write-Warning($message) { Write-Host "[WARNING] $message" -ForegroundColor Yellow }
function Write-Error($message) { Write-Host "[ERROR] $message" -ForegroundColor Red }

# =============================================================================
# CONFIGURATION
# =============================================================================

Write-Status "Starting database backup process..."

# Check if .env file exists
if (-not (Test-Path ".env")) {
    Write-Error ".env file not found. Please create it with DATABASE_URL."
    Write-Error "Add this line to your .env file:"
    Write-Error "DATABASE_URL=postgresql://postgres:[password]@db.your-project-id.supabase.co:5432/postgres"
    exit 1
}

# Load environment variables from .env
Get-Content ".env" | ForEach-Object {
    if ($_ -match "^([^=]+)=(.*)$" -and -not $_.StartsWith("#")) {
        $name = $matches[1]
        $value = $matches[2]
        Set-Variable -Name $name -Value $value
    }
}

# Check if DATABASE_URL is set
if (-not $DATABASE_URL) {
    Write-Error "DATABASE_URL not found in .env file."
    Write-Error "Please add your Supabase database connection string to .env"
    exit 1
}

# Create backup directory with timestamp
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$backupDir = "database-backups\$timestamp"
New-Item -ItemType Directory -Path $backupDir -Force | Out-Null

Write-Status "Backup directory created: $backupDir"

# =============================================================================
# CHECK PREREQUISITES
# =============================================================================

# Check if pg_dump is available
try {
    $null = Get-Command pg_dump -ErrorAction Stop
    Write-Success "pg_dump found"
} catch {
    Write-Error "pg_dump not found. Please install PostgreSQL client tools:"
    Write-Error "1. Download from: https://www.postgresql.org/download/windows/"
    Write-Error "2. Or install via chocolatey: choco install postgresql"
    Write-Error "3. Add PostgreSQL bin directory to your PATH"
    exit 1
}

# =============================================================================
# BACKUP EXECUTION
# =============================================================================

try {
    Write-Status "Creating comprehensive database backup..."

    # 1. Full database dump (schema + data)
    Write-Status "1. Creating full database backup (schema + data)..."
    $dumpFile = "$backupDir\full_database_backup.dump"
    $logFile = "$backupDir\backup.log"
    
    & pg_dump $DATABASE_URL --verbose --no-owner --no-privileges --format=custom --file=$dumpFile 2>&1 | Tee-Object -FilePath $logFile
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Full database backup completed"
    } else {
        throw "pg_dump failed with exit code $LASTEXITCODE"
    }

    # 2. Schema-only backup
    Write-Status "2. Creating schema-only backup..."
    & pg_dump $DATABASE_URL --schema-only --verbose --no-owner --no-privileges --file="$backupDir\schema_only.sql" 2>&1 | Tee-Object -FilePath $logFile -Append

    # 3. Data-only backup
    Write-Status "3. Creating data-only backup..."
    & pg_dump $DATABASE_URL --data-only --verbose --no-owner --no-privileges --file="$backupDir\data_only.sql" 2>&1 | Tee-Object -FilePath $logFile -Append

    # 4. Permissions-specific backup
    Write-Status "4. Creating permissions system backup..."
    & pg_dump $DATABASE_URL --verbose --no-owner --no-privileges --table=permissions --table=roles --table=authorized_users --file="$backupDir\permissions_system_backup.sql" 2>&1 | Tee-Object -FilePath $logFile -Append

    # 5. Create backup metadata
    Write-Status "5. Creating backup metadata..."
    $backupInfo = @"
=============================================================================
DATABASE BACKUP INFORMATION  
=============================================================================
Backup Date: $(Get-Date)
Database: Supabase PostgreSQL
Project: Aming-app Permission System
Backup Type: Full (Schema + Data)

Files Created:
- full_database_backup.dump    : Complete database (use pg_restore)
- schema_only.sql             : Database structure only
- data_only.sql              : Data only  
- permissions_system_backup.sql : Permissions tables only
- backup.log                 : Backup execution log
- backup_info.txt            : This information file

Restore Instructions:
===================
To restore the full database:
pg_restore --clean --if-exists --verbose --dbname="$DATABASE_URL" full_database_backup.dump

To restore specific tables:  
psql "$DATABASE_URL" -f permissions_system_backup.sql

Size Information:
================
"@

    $backupInfo | Out-File "$backupDir\backup_info.txt"
    Get-ChildItem $backupDir | Format-Table Name, Length, LastWriteTime | Out-File "$backupDir\backup_info.txt" -Append

    # 6. Test backup integrity
    Write-Status "6. Testing backup integrity..."
    & pg_restore --list $dumpFile > "$backupDir\backup_contents.txt" 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Backup integrity verified"
    } else {
        Write-Warning "Could not verify backup integrity (but backup may still be valid)"
    }

    # Create PowerShell restore script
    $restoreScript = @"
# Quick restore script for this backup
# Usage: .\restore.ps1

Write-Host "🚨 WARNING: This will OVERWRITE your current database!" -ForegroundColor Red
Write-Host "Current backup: $(Split-Path -Leaf `$PSScriptRoot)" -ForegroundColor Yellow
Write-Host ""
`$response = Read-Host "Are you sure you want to restore? (yes/no)"
if (`$response -eq "yes") {
    Write-Host "Restoring database..." -ForegroundColor Green
    pg_restore --clean --if-exists --verbose --dbname="$DATABASE_URL" "full_database_backup.dump"
    Write-Host "Restore completed!" -ForegroundColor Green
} else {
    Write-Host "Restore cancelled." -ForegroundColor Yellow
}
"@
    
    $restoreScript | Out-File "$backupDir\restore.ps1"
    Write-Success "Quick restore script created: $backupDir\restore.ps1"

    # =============================================================================
    # BACKUP SUMMARY
    # =============================================================================

    $backupSize = (Get-ChildItem $backupDir -Recurse | Measure-Object -Property Length -Sum).Sum
    $backupSizeMB = [math]::Round($backupSize / 1MB, 2)
    $fileCount = (Get-ChildItem $backupDir).Count

    Write-Success "Database backup completed successfully!"
    Write-Host ""
    Write-Host "📊 Backup Summary:" -ForegroundColor Cyan
    Write-Host "   Directory: $backupDir" -ForegroundColor White
    Write-Host "   Total Size: $backupSizeMB MB" -ForegroundColor White  
    Write-Host "   Files Created: $fileCount" -ForegroundColor White
    Write-Host ""
    Write-Host "📋 Files:" -ForegroundColor Cyan
    Write-Host "   • full_database_backup.dump (primary backup - use pg_restore)" -ForegroundColor White
    Write-Host "   • schema_only.sql (structure reference)" -ForegroundColor White
    Write-Host "   • data_only.sql (data reference)" -ForegroundColor White
    Write-Host "   • permissions_system_backup.sql (permissions tables)" -ForegroundColor White
    Write-Host "   • backup.log (execution log)" -ForegroundColor White
    Write-Host "   • backup_info.txt (metadata)" -ForegroundColor White
    Write-Host "   • backup_contents.txt (backup contents list)" -ForegroundColor White
    Write-Host "   • restore.ps1 (quick restore script)" -ForegroundColor White
    Write-Host ""
    Write-Host "🔄 To restore:" -ForegroundColor Cyan
    Write-Host "   pg_restore --clean --if-exists --verbose --dbname=`"`$DATABASE_URL`" `"$backupDir\full_database_backup.dump`"" -ForegroundColor White
    Write-Host ""
    Write-Warning "📁 IMPORTANT: Store this backup in a secure location before running migrations!"
    Write-Success "🎉 Backup process completed successfully!"

} catch {
    Write-Error "Backup failed: $_"
    Write-Error "Check the log file for details: $backupDir\backup.log"
    exit 1
}