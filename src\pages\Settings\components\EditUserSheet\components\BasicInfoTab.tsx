import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../../../components/ui/card'
import { Input } from '../../../../../components/ui/input'
import { Label } from '../../../../../components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../../../components/ui/select'
import { Switch } from '../../../../../components/ui/switch'
import { Building, Users, Settings, BarChart3, ShoppingCart, Headphones, Shield, Briefcase } from 'lucide-react'
import type { EditUserFormData } from '../types'

interface BasicInfoTabProps {
  formData: EditUserFormData
  onInputChange: (field: string, value: string | boolean) => void
}

const DEPARTMENT_OPTIONS = [
  { value: 'administration', label: 'Administration', icon: Settings },
  { value: 'sales', label: 'Sales & Marketing', icon: ShoppingCart },
  { value: 'operations', label: 'Operations', icon: Building },
  { value: 'customer_service', label: 'Customer Service', icon: Headphones },
  { value: 'finance', label: 'Finance & Accounting', icon: BarChart3 },
  { value: 'human_resources', label: 'Human Resources', icon: Users },
  { value: 'it_support', label: 'IT Support', icon: Shield },
  { value: 'management', label: 'Management', icon: Briefcase }
]

export function BasicInfoTab({ formData, onInputChange }: BasicInfoTabProps) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Personal Information</CardTitle>
          <CardDescription>
            Update the user's basic contact and identification details.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="first_name">First Name *</Label>
              <Input
                id="first_name"
                placeholder="John"
                value={formData.first_name}
                onChange={(e) => onInputChange('first_name', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="last_name">Last Name *</Label>
              <Input
                id="last_name"
                placeholder="Doe"
                value={formData.last_name}
                onChange={(e) => onInputChange('last_name', e.target.value)}
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="department">Department</Label>
            <Select value={formData.department || ''} onValueChange={(value) => onInputChange('department', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select a department" />
              </SelectTrigger>
              <SelectContent>
                {DEPARTMENT_OPTIONS.map((dept) => {
                  const IconComponent = dept.icon
                  return (
                    <SelectItem key={dept.value} value={dept.value}>
                      <div className="flex items-center gap-2">
                        <IconComponent className="h-4 w-4 text-gray-500" />
                        {dept.label}
                      </div>
                    </SelectItem>
                  )
                })}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Account Status</CardTitle>
          <CardDescription>
            Control whether this user account is active and can access the system.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="is_active" className="text-base">Account Active</Label>
              <p className="text-sm text-muted-foreground">
                When disabled, the user cannot log in or access the system.
              </p>
            </div>
            <Switch
              id="is_active"
              checked={formData.is_active}
              onCheckedChange={(checked) => onInputChange('is_active', checked)}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}