import { supabase } from '../lib/supabase';

export interface RoleTemplate {
  id: number;
  name: string;
  display_name: string;
  description: string;
  permissions: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * Role Template Service
 * Manages role templates for user permission assignment
 * Following CLAUDE.md guidelines - focused, under 250 lines
 */
export class RoleTemplateService {
  /**
   * Get all active role templates
   */
  static async getActiveRoles(): Promise<RoleTemplate[]> {
    try {
      const { data, error } = await supabase
        .from('roles')
        .select('*')
        .eq('is_active', true)
        .order('display_name');

      if (error) {
        throw new Error(`Failed to fetch roles: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching active roles:', error);
      return [];
    }
  }

  /**
   * Get role template by name
   */
  static async getRoleByName(name: string): Promise<RoleTemplate | null> {
    try {
      const { data, error } = await supabase
        .from('roles')
        .select('*')
        .eq('name', name)
        .eq('is_active', true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // No matching row found
        }
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error fetching role by name:', error);
      return null;
    }
  }

  /**
   * Get permissions for a role template
   */
  static async getRolePermissions(roleName: string): Promise<string[]> {
    try {
      const role = await this.getRoleByName(roleName);
      return role?.permissions || [];
    } catch (error) {
      console.error('Error getting role permissions:', error);
      return [];
    }
  }

  /**
   * Apply role template to user (update authorized_users permissions)
   */
  static async applyRoleToUser(userEmail: string, roleName: string): Promise<boolean> {
    try {
      const role = await this.getRoleByName(roleName);
      if (!role) {
        throw new Error(`Role template '${roleName}' not found`);
      }

      const { error } = await supabase
        .from('authorized_users')
        .update({
          permissions: role.permissions,
          role_template: roleName,
          updated_at: new Date().toISOString()
        })
        .eq('email', userEmail)
        .eq('is_active', true);

      if (error) {
        throw new Error(`Failed to apply role: ${error.message}`);
      }

      return true;
    } catch (error) {
      console.error('Error applying role to user:', error);
      return false;
    }
  }

  /**
   * Get role template options for UI dropdowns
   */
  static async getRoleOptions(): Promise<Array<{value: string; label: string; description: string}>> {
    try {
      const roles = await this.getActiveRoles();
      
      return roles.map(role => ({
        value: role.name,
        label: role.display_name,
        description: role.description || ''
      }));
    } catch (error) {
      console.error('Error getting role options:', error);
      return [];
    }
  }
}