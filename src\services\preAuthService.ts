export interface AuthorizedUser {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  department?: string;
  permissions: string[];
  role?: string;
  role_template?: string;
  is_active: boolean;
}

interface ValidateEmailResponse {
  authorized: boolean;
  user?: AuthorizedUser;
  error?: string;
}

/**
 * Pre-authorization service for validating emails via Edge Function
 * Following CLAUDE.md: <250 lines, single responsibility, production-grade
 * Uses SERVICE_ROLE_KEY via Edge Function to bypass RLS
 */
export class PreAuthService {
  /**
   * Validate email against authorized_users table via Edge Function
   * @param email Email to validate
   * @returns Promise<AuthorizedUser | null>
   */
  static async validateEmail(email: string): Promise<AuthorizedUser | null> {
    try {
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      
      if (!supabaseUrl) {
        console.error('Missing VITE_SUPABASE_URL environment variable');
        return null;
      }

      console.log('Attempting to validate email via Edge Function:', email);

      // Call Edge Function for server-side validation with SERVICE_ROLE_KEY
      const response = await fetch(`${supabaseUrl}/functions/v1/validate-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        console.error(`Edge Function HTTP error: ${response.status}: ${response.statusText}`);
        console.error('This might indicate the Edge Function is not deployed or configured correctly');
        
        // If Edge Function fails, try a direct database fallback
        console.log('Attempting fallback: direct database query...');
        return await this.validateEmailFallback(email);
      }

      const result: ValidateEmailResponse = await response.json();

      if (result.error && !result.authorized) {
        // Handle authorization failures (not system errors)
        if (result.error.includes('not authorized')) {
          console.log('User not authorized:', email);
          return null; // Email not in authorized_users
        }
        console.error('Edge Function returned error:', result.error);
        return await this.validateEmailFallback(email);
      }

      console.log('Email validation successful via Edge Function');
      return result.user || null;
    } catch (error) {
      console.error('Error validating email via Edge Function:', error);
      console.log('Attempting fallback: direct database query...');
      return await this.validateEmailFallback(email);
    }
  }

  /**
   * Fallback method to validate email directly via database
   * Used when Edge Function is unavailable
   * @param email Email to validate
   * @returns Promise<AuthorizedUser | null>
   */
  private static async validateEmailFallback(email: string): Promise<AuthorizedUser | null> {
    try {
      console.log('Using fallback validation for email:', email);
      
      // Import Supabase client
      const { supabase } = await import('../lib/supabase');
      
      // Try direct query (this may fail due to RLS, but worth trying)
      const { data, error } = await supabase
        .from('authorized_users')
        .select(`
          id,
          email,
          first_name,
          last_name,
          department,
          permissions,
          role_template,
          is_active
        `)
        .eq('email', email.toLowerCase().trim())
        .eq('is_active', true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          console.log('User not found in authorized_users table:', email);
          return null;
        }
        console.error('Database fallback failed:', error);
        console.error('This suggests RLS policies prevent direct access (expected behavior)');
        return null;
      }

      console.log('Email validation successful via database fallback');
      return data as AuthorizedUser;
    } catch (error) {
      console.error('Database fallback validation failed:', error);
      return null;
    }
  }

  /**
   * Check if user is authorized (simple boolean check)
   * @param email Email to check
   * @returns Promise<boolean>
   */
  static async isAuthorized(email: string): Promise<boolean> {
    try {
      const user = await this.validateEmail(email);
      return user !== null;
    } catch (error) {
      console.error('Error checking authorization:', error);
      return false;
    }
  }

  /**
   * Get user permissions by email
   * @param email Email to check
   * @returns Promise<string[]>
   */
  static async getUserPermissions(email: string): Promise<string[]> {
    try {
      const user = await this.validateEmail(email);
      return user?.permissions || [];
    } catch (error) {
      console.error('Error getting user permissions:', error);
      return [];
    }
  }
}