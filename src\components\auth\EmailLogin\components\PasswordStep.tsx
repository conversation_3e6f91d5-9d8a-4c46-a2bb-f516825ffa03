import React from 'react';
import { Button } from '../../../ui/button';
import { Input } from '../../../ui/input';
import { Label } from '../../../ui/label';
import { PasswordField } from '../../../ui/password-field';
import { ButtonLoading } from '../../../ui/loading-indicator';
import { Mail, KeyRound } from 'lucide-react';
import type { UserType } from '../types';

interface PasswordStepProps {
  email: string;
  password: string;
  userType: UserType;
  onPasswordChange: (value: string) => void;
  onSubmit: () => void;
  loading: boolean;
  clearMessages: () => void;
}

export const PasswordStep: React.FC<PasswordStepProps> = ({
  email,
  password,
  userType,
  onPasswordChange,
  onSubmit,
  loading,
  clearMessages
}) => {
  return (
    <>
      <div className="space-y-2">
        <Label htmlFor="email" className="flex items-center gap-2 text-black text-sm font-medium">
          <Mail className="h-4 w-4" />
          Email
        </Label>
        <Input
          value={email}
          disabled
          className="bg-gray-100 border-0 rounded-lg px-4 py-3 text-gray-600"
        />
      </div>

      {userType === 'admin' ? (
        // Traditional password field for admins
        <PasswordField
          id="password"
          label="Password"
          placeholder="Password"
          value={password}
          onChange={(value) => {
            onPasswordChange(value);
            clearMessages();
          }}
          disabled={loading}
          required
          autoComplete="current-password"
        />
      ) : (
        // PIN field for regular users  
        <div className="space-y-2">
          <Label htmlFor="pin" className="flex items-center gap-2 text-black text-sm font-medium">
            <KeyRound className="h-4 w-4" />
            PIN
          </Label>
          <Input
            id="pin"
            type="password"
            value={password} // Reuse password state
            onChange={(e) => {
              const pinValue = e.target.value.replace(/\D/g, '').slice(0, 6);
              onPasswordChange(pinValue);
              clearMessages();
            }}
            placeholder="Enter PIN"
            className="bg-gray-100 border-0 rounded-lg px-4 py-3 text-center text-lg tracking-wider"
            disabled={loading}
            maxLength={6}
            pattern="[0-9]*"
            inputMode="numeric"
          />
        </div>
      )}

      <Button
        type="submit"
        className="w-full bg-black hover:bg-gray-800 text-white py-3 rounded-lg font-medium transition-all duration-200"
        disabled={loading || !password}
      >
        {loading ? (
          <ButtonLoading text="Signing in..." />
        ) : (
          'Sign in'
        )}
      </Button>
    </>
  );
};