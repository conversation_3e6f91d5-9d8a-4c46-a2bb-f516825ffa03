# Supabase Realtime Troubleshooting Guide

This guide provides solutions for common issues with Supabase Realtime subscriptions in the Aming App.

## Common Errors

### "Failed to subscribe to [table]: CLOSED" or "CHANNEL_ERROR"

These errors indicate that the Realtime connection was closed or encountered an error. This can happen for several reasons:

1. **Supabase Realtime is not enabled**: Make sure Realtime is enabled in your Supabase project.
2. **Publication not configured**: The `supabase_realtime` publication might not be properly configured.
3. **Connection limits**: You might be hitting connection limits for your Supabase plan.
4. **Network issues**: There might be network connectivity problems.

## Fixing Realtime Issues

### 1. Check Realtime Diagnostics

Navigate to the Realtime Diagnostics page in the app:

```
/realtime-diagnostics
```

This page will help diagnose issues with your Realtime setup and provide solutions.

### 2. Configure Supabase Realtime Publication

Run the following SQL in the Supabase SQL Editor to properly set up the Realtime publication:

```sql
-- Drop the existing publication if it exists
DROP PUBLICATION IF EXISTS supabase_realtime;

-- Create a new publication with no tables initially
CREATE PUBLICATION supabase_realtime;

-- Add tables to the publication
ALTER PUBLICATION supabase_realtime ADD TABLE public.orders;
ALTER PUBLICATION supabase_realtime ADD TABLE public.order_items;
ALTER PUBLICATION supabase_realtime ADD TABLE public.products;
ALTER PUBLICATION supabase_realtime ADD TABLE public.attributes;

-- Create a policy to allow authenticated users to receive broadcasts
CREATE POLICY "Authenticated users can receive broadcasts"
ON "realtime"."messages"
FOR SELECT
TO authenticated
USING ( true );

-- Set replica identity to full for better change tracking
ALTER TABLE public.orders REPLICA IDENTITY FULL;
ALTER TABLE public.order_items REPLICA IDENTITY FULL;
ALTER TABLE public.products REPLICA IDENTITY FULL;
ALTER TABLE public.attributes REPLICA IDENTITY FULL;
```

### 3. Check Connection Limits

Supabase has different connection limits based on your plan:

| Plan | Concurrent Connections | Channels per Connection | Messages per Second |
|------|------------------------|-------------------------|---------------------|
| Free | 200                    | 100                     | 100                 |
| Pro  | 500                    | 100                     | 500                 |
| Team | 10,000                 | 100                     | 2,500               |

If you're hitting these limits, consider:
- Consolidating subscriptions
- Using fewer channels
- Upgrading your Supabase plan

### 4. Implement Reconnection Logic

The app now includes robust reconnection logic in the `useRealtimeSubscription` hook. This will automatically attempt to reconnect when a subscription fails, using exponential backoff to avoid overwhelming the server.

## Best Practices

### 1. Limit the Number of Channels

Create fewer channels by grouping related tables together when possible. For example, instead of creating separate channels for each table, you can create a single channel for related tables.

### 2. Use Filters

Apply filters to limit the data you receive and reduce network traffic:

```typescript
useRealtimeSubscription(
  'orders',
  cacheKey,
  'public',
  { id: 'eq.123' } // Filter for a specific order
);
```

### 3. Set REPLICA IDENTITY FULL

Setting `REPLICA IDENTITY FULL` on your tables ensures you receive the full old record on DELETE events, which is useful for optimistic UI updates.

### 4. Monitor Realtime Status

Use the Realtime Diagnostics page to monitor the status of your Realtime connections and quickly identify issues.

## Advanced Troubleshooting

### Check Supabase Logs

If you're still experiencing issues, check the Supabase logs in the dashboard:

1. Go to the Supabase Dashboard
2. Navigate to your project
3. Go to Database > Logs
4. Look for any errors related to Realtime

### Check Network Connectivity

Use browser developer tools to inspect WebSocket connections:

1. Open browser developer tools (F12)
2. Go to the Network tab
3. Filter for "WS" to see WebSocket connections
4. Look for any failed connections or error messages

### Contact Supabase Support

If you've tried all the above solutions and are still experiencing issues, contact Supabase support with:

1. Your project ID
2. Error messages you're seeing
3. Steps you've taken to troubleshoot
4. Any relevant code snippets

## Additional Resources

- [Supabase Realtime Documentation](https://supabase.com/docs/guides/realtime)
- [Supabase Realtime Quotas](https://supabase.com/docs/guides/realtime/quotas)
- [Supabase Realtime Error Codes](https://supabase.com/docs/guides/realtime/error_codes)
