-- Migration 020: Add calculation breakdown storage and enhanced production cost function
-- This migration adds breakdown storage capability and creates an optimized function
-- that calculates both production cost and detailed breakdown in a single operation

-- Step 1: Add calculation_breakdown column to order_items
ALTER TABLE order_items 
ADD COLUMN IF NOT EXISTS calculation_breakdown JSONB;

-- Add index for JSONB queries
CREATE INDEX IF NOT EXISTS idx_order_items_calculation_breakdown_gin 
ON order_items USING gin (calculation_breakdown);

-- Step 2: Create enhanced production cost function with integrated breakdown
CREATE OR REPLACE FUNCTION calculate_item_production_cost_with_breakdown(
    p_order_item_id UUID,
    p_category_id UUID,
    p_product_type_id UUID,
    p_size_id UUID,
    p_quantity INTEGER DEFAULT 1,
    p_nos INTEGER DEFAULT 1
)
RETURNS TABLE(
    base_cost NUMERIC,
    additional_cost NUMERIC,
    total_cost NUMERIC,
    calculation_breakdown JSONB,
    calculation_success BOOLEAN,
    rule_applied BOOLEAN,
    rule_type TEXT,
    error_logged BOOLEAN,
    announcement_created BOOLEAN
) AS $$
DECLARE
    v_base_cost NUMERIC := 0;
    v_additional_cost NUMERIC := 0;
    v_total_cost NUMERIC := 0;
    v_component_record RECORD;
    v_tier_type TEXT;
    v_component_value NUMERIC;
    
    -- Product info for error reporting and breakdown
    v_product_category_name TEXT;
    v_product_type_name TEXT;
    v_size_name TEXT;
    
    -- Calculation rule variables
    v_rule_result RECORD;
    v_rule_applied BOOLEAN := false;
    v_rule_type TEXT := 'none';
    
    -- Error handling variables
    v_error_logged BOOLEAN := false;
    v_announcement_created BOOLEAN := false;
    v_error_id UUID;
    v_announcement_id UUID;
    
    -- Breakdown data collection
    v_applied_templates JSONB := '[]'::JSONB;
    v_component_calculations JSONB := '[]'::JSONB;
    v_tier_distributions JSONB := '[]'::JSONB;
    v_calculation_breakdown JSONB;
    v_component_breakdown JSONB;
    v_tier_info JSONB;
BEGIN
    -- Get product names for error reporting and breakdown display
    SELECT pa1.value, pa2.value, pa3.value 
    INTO v_product_category_name, v_product_type_name, v_size_name
    FROM product_attributes pa1, product_attributes pa2, product_attributes pa3
    WHERE pa1.id = p_category_id 
      AND pa2.id = p_product_type_id 
      AND pa3.id = p_size_id;

    -- Check if calculation rules exist for this product category
    SELECT rule_type, additional_info::TEXT
    INTO v_rule_result
    FROM calculation_rules 
    WHERE product_category = v_product_category_name 
      AND rule_scope = 'production_cost'
    LIMIT 1;

    IF FOUND THEN
        v_rule_applied := true;
        v_rule_type := v_rule_result.rule_type;
    END IF;

    -- Calculate base costs from components and collect breakdown data
    FOR v_component_record IN
        SELECT 
            pcv.value,
            pcv.tier_metadata,
            pcc.name as component_name,
            pcc.category as component_category,
            pct.name as template_name,
            pct.id as template_id
        FROM production_cost_component_values pcv
        INNER JOIN production_cost_components pcc ON pcv.component_id = pcc.id
        LEFT JOIN production_cost_templates pct ON pcv.template_id = pct.id
        WHERE pcv.product_category_id = p_category_id
          AND pcv.product_type_id = p_product_type_id
          AND pcv.size_id = p_size_id
    LOOP
        -- Extract tier type for calculation
        v_tier_type := COALESCE(v_component_record.tier_metadata->>'tier_type', 'per_unit');
        v_component_value := v_component_record.value;

        -- Apply tier-aware calculations and collect breakdown data
        CASE v_tier_type
            WHEN 'per_unit' THEN
                -- Apply calculation rule logic
                IF v_rule_type = 'multiply_by_quantity_and_nos' THEN
                    v_component_value := v_component_record.value * p_quantity * p_nos;
                ELSE
                    v_component_value := v_component_record.value * p_quantity;
                END IF;
                
                -- Build component breakdown
                v_component_breakdown := jsonb_build_object(
                    'id', gen_random_uuid(),
                    'name', v_component_record.component_name,
                    'base_value', v_component_record.value,
                    'transformation', CASE 
                        WHEN v_rule_type = 'multiply_by_quantity_and_nos' THEN 'multiply_qty_nos'
                        ELSE 'multiply_qty' 
                    END,
                    'final_value', v_component_value,
                    'formula', CASE 
                        WHEN v_rule_type = 'multiply_by_quantity_and_nos' THEN 
                            v_component_record.value::TEXT || ' × ' || p_quantity::TEXT || ' × ' || p_nos::TEXT
                        ELSE 
                            v_component_record.value::TEXT || ' × ' || p_quantity::TEXT
                    END
                );

            WHEN 'per_order' THEN
                v_component_value := v_component_record.value;
                
                v_component_breakdown := jsonb_build_object(
                    'id', gen_random_uuid(),
                    'name', v_component_record.component_name,
                    'base_value', v_component_record.value,
                    'transformation', 'add_to_final',
                    'final_value', v_component_value,
                    'formula', v_component_record.value::TEXT || ' (fixed per order)'
                );

            WHEN 'distributed_by_nos' THEN
                v_component_value := v_component_record.value / GREATEST(p_nos, 1);
                
                v_component_breakdown := jsonb_build_object(
                    'id', gen_random_uuid(),
                    'name', v_component_record.component_name,
                    'base_value', v_component_record.value,
                    'transformation', 'divide_nos',
                    'final_value', v_component_value,
                    'formula', v_component_record.value::TEXT || ' ÷ ' || p_nos::TEXT || ' sheets'
                );

            WHEN 'distributed_by_qty' THEN
                v_component_value := v_component_record.value / GREATEST(p_quantity, 1);
                
                v_component_breakdown := jsonb_build_object(
                    'id', gen_random_uuid(),
                    'name', v_component_record.component_name,
                    'base_value', v_component_record.value,
                    'transformation', 'divide_qty',
                    'final_value', v_component_value,
                    'formula', v_component_record.value::TEXT || ' ÷ ' || p_quantity::TEXT || ' units'
                );
                
            ELSE
                -- Default case
                v_component_value := v_component_record.value;
                
                v_component_breakdown := jsonb_build_object(
                    'id', gen_random_uuid(),
                    'name', v_component_record.component_name,
                    'base_value', v_component_record.value,
                    'transformation', 'none',
                    'final_value', v_component_value,
                    'formula', v_component_record.value::TEXT || ' (direct value)'
                );
        END CASE;

        -- Add to component calculations array
        v_component_calculations := v_component_calculations || v_component_breakdown;

        -- Accumulate costs by category
        IF v_component_record.component_category = 'basic_cost' THEN
            v_base_cost := v_base_cost + v_component_value;
        ELSE
            v_additional_cost := v_additional_cost + v_component_value;
        END IF;

        -- Collect applied template information
        IF v_component_record.template_name IS NOT NULL THEN
            v_applied_templates := v_applied_templates || jsonb_build_object(
                'id', v_component_record.template_id,
                'name', v_component_record.template_name,
                'type', CASE 
                    WHEN v_component_record.component_category = 'basic_cost' THEN 'foundation'
                    ELSE 'additional'
                END,
                'tier', v_tier_type,
                'value', v_component_value
            );
        END IF;

        -- Collect tier distribution information
        IF v_tier_type != 'per_unit' THEN
            v_tier_distributions := v_tier_distributions || jsonb_build_object(
                'component_name', v_component_record.component_name,
                'tier_type', v_tier_type,
                'original_value', v_component_record.value,
                'distributed_value', v_component_value,
                'distribution_factor', CASE 
                    WHEN v_tier_type = 'distributed_by_nos' THEN p_nos
                    WHEN v_tier_type = 'distributed_by_qty' THEN p_quantity
                    ELSE 1
                END
            );
        END IF;

    END LOOP;

    -- Calculate total cost
    v_total_cost := v_base_cost + v_additional_cost;

    -- Handle missing calculation rules (create error and announcement)
    IF NOT v_rule_applied THEN
        -- Log error
        INSERT INTO calculation_rules_errors (
            product_category, 
            product_type, 
            size, 
            error_type,
            error_message,
            created_at
        ) VALUES (
            v_product_category_name,
            v_product_type_name,
            v_size_name,
            'missing_rule',
            'No calculation rule found for product category: ' || v_product_category_name,
            NOW()
        ) RETURNING id INTO v_error_id;
        
        v_error_logged := true;

        -- Create announcement
        INSERT INTO system_announcements (
            title,
            message,
            type,
            priority,
            category,
            target_audience,
            is_active,
            created_at,
            metadata
        ) VALUES (
            'Missing Production Cost Calculation Rule',
            'Production cost calculation rule is missing for product category "' || v_product_category_name || '". Using default calculation method.',
            'warning',
            'medium',
            'production_cost',
            'admin',
            true,
            NOW(),
            jsonb_build_object(
                'error_id', v_error_id,
                'product_category', v_product_category_name,
                'calculation_method', 'default_fallback'
            )
        ) RETURNING id INTO v_announcement_id;
        
        v_announcement_created := true;
    END IF;

    -- Build final calculation breakdown
    v_calculation_breakdown := jsonb_build_object(
        'calculation_timestamp', NOW(),
        'total_cost', v_total_cost,
        'base_cost', v_base_cost,
        'additional_cost', v_additional_cost,
        'rule_applied', v_rule_applied,
        'rule_type', v_rule_type,
        'applied_templates', v_applied_templates,
        'component_calculations', v_component_calculations,
        'tier_distributions', v_tier_distributions,
        'calculation_context', jsonb_build_object(
            'product_category', v_product_category_name,
            'product_type', v_product_type_name,
            'size', v_size_name,
            'quantity', p_quantity,
            'nos', p_nos
        )
    );

    -- Update order_items with both production cost and breakdown
    IF p_order_item_id IS NOT NULL THEN
        UPDATE order_items 
        SET 
            production_cost = v_total_cost,
            calculation_breakdown = v_calculation_breakdown,
            updated_at = NOW()
        WHERE item_id = p_order_item_id;
    END IF;

    -- Return results
    RETURN QUERY SELECT 
        v_base_cost,
        v_additional_cost, 
        v_total_cost,
        v_calculation_breakdown,
        true::BOOLEAN as calculation_success,
        v_rule_applied,
        v_rule_type,
        v_error_logged,
        v_announcement_created;

EXCEPTION WHEN OTHERS THEN
    -- Error handling
    RAISE WARNING 'Error in calculate_item_production_cost_with_breakdown: %', SQLERRM;
    
    RETURN QUERY SELECT 
        0::NUMERIC as base_cost,
        0::NUMERIC as additional_cost,
        0::NUMERIC as total_cost,
        '{}'::JSONB as calculation_breakdown,
        false::BOOLEAN as calculation_success,
        false::BOOLEAN as rule_applied,
        'error'::TEXT as rule_type,
        false::BOOLEAN as error_logged,
        false::BOOLEAN as announcement_created;
END;
$$ LANGUAGE plpgsql;

-- Step 3: Create convenience function for order item updates
CREATE OR REPLACE FUNCTION update_order_item_production_cost_with_breakdown(
    p_order_item_id UUID
)
RETURNS JSONB AS $$
DECLARE
    v_order_item RECORD;
    v_result RECORD;
    v_category_id UUID;
    v_product_type_id UUID;
    v_size_id UUID;
BEGIN
    -- Get order item details
    SELECT * INTO v_order_item
    FROM order_items 
    WHERE item_id = p_order_item_id;
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object('error', 'Order item not found');
    END IF;
    
    -- Map product attributes to IDs
    SELECT id INTO v_category_id 
    FROM product_attributes 
    WHERE value = v_order_item.product_category AND category = 'category';
    
    SELECT id INTO v_product_type_id 
    FROM product_attributes 
    WHERE value = v_order_item.product_type AND category = 'product_type';
    
    SELECT id INTO v_size_id 
    FROM product_attributes 
    WHERE value = v_order_item.size AND category = 'size';
    
    -- Calculate with breakdown
    SELECT * INTO v_result
    FROM calculate_item_production_cost_with_breakdown(
        p_order_item_id,
        v_category_id,
        v_product_type_id, 
        v_size_id,
        v_order_item.quantity,
        COALESCE(v_order_item.nos, 1)
    );
    
    RETURN jsonb_build_object(
        'success', v_result.calculation_success,
        'production_cost', v_result.total_cost,
        'breakdown', v_result.calculation_breakdown,
        'rule_applied', v_result.rule_applied
    );
END;
$$ LANGUAGE plpgsql;

-- Step 4: Create bulk update function for analytics
CREATE OR REPLACE FUNCTION bulk_update_production_cost_breakdowns(
    p_limit INTEGER DEFAULT 100
)
RETURNS TABLE(
    processed_count INTEGER,
    success_count INTEGER,
    error_count INTEGER
) AS $$
DECLARE
    v_processed INTEGER := 0;
    v_success INTEGER := 0;
    v_error INTEGER := 0;
    v_item_record RECORD;
    v_result JSONB;
BEGIN
    -- Process order items that don't have breakdown data
    FOR v_item_record IN
        SELECT item_id 
        FROM order_items 
        WHERE calculation_breakdown IS NULL
        LIMIT p_limit
    LOOP
        BEGIN
            v_result := update_order_item_production_cost_with_breakdown(v_item_record.item_id);
            
            v_processed := v_processed + 1;
            
            IF (v_result->>'success')::BOOLEAN THEN
                v_success := v_success + 1;
            ELSE
                v_error := v_error + 1;
            END IF;
            
        EXCEPTION WHEN OTHERS THEN
            v_error := v_error + 1;
            v_processed := v_processed + 1;
        END;
    END LOOP;
    
    RETURN QUERY SELECT v_processed, v_success, v_error;
END;
$$ LANGUAGE plpgsql;

-- Step 5: Add helpful comments
COMMENT ON COLUMN order_items.calculation_breakdown IS 'JSONB field storing detailed production cost calculation breakdown including applied templates, component calculations, and tier distributions';

COMMENT ON FUNCTION calculate_item_production_cost_with_breakdown IS 'Enhanced production cost calculation function that computes both cost and detailed breakdown in a single operation for maximum efficiency';

COMMENT ON FUNCTION update_order_item_production_cost_with_breakdown IS 'Convenience function to update production cost and breakdown for a specific order item';

COMMENT ON FUNCTION bulk_update_production_cost_breakdowns IS 'Bulk processing function to populate breakdown data for existing order items that lack it';