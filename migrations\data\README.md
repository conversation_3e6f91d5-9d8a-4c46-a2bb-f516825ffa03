# Data Migrations

Business logic implementations, data transformations, and application behavior modifications.

## Files in this directory:

### Business Rules & Logic
- `001_business_rule_enforcement.sql` - Core business rule implementation
- `002_simplify_calculation_methods.sql` - Simplify calculation logic
- `007_system_announcements_calculation_rules.sql` - System announcements for calculation rules

### Data Management
- `003_cleanup_unused_fields.sql` - Remove unused database fields
- `004_editing_session_management.sql` - Session management system
- `006_hard_delete_products_function.sql` - Hard delete functionality for products
- `008_harmonize_manual_trigger_with_breakdown.sql` - Harmonize manual triggers
- `rollback_003_calculation_methods.sql` - Rollback script for calculation methods

### Order & Product Enhancements
- `005_add_order_notes_column.sql` - Add order notes functionality
- `009_update_item_notes_validation_for_simplified_types.sql` - Update item notes validation
- `010_add_enable_notes_column.sql` - Add enable notes column

## Execution Order

Execute data migrations **after** schema migrations:

1. Business rules first (`001_business_rule_enforcement.sql`)
2. Core functionality (`002_*`, `003_*`, `004_*`)
3. Feature enhancements (`005_*` through `008_*`)
4. Recent updates (`009_*`, `010_*`)

## Dependencies

- Requires schema migrations to be completed first
- Some files depend on specific tables/columns from schema migrations
- Business rule files should be executed before feature files

## Notes

- These migrations modify application behavior
- May require testing of business logic after execution
- Rollback scripts available for some migrations
- Contains both DDL and DML operations