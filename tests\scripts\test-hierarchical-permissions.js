#!/usr/bin/env node
/**
 * Test script for hierarchical permissions system
 * Verifies that page access permissions grant full control
 */

const { hasPermissionWithHierarchy, getEffectivePermissions, PAGE_PERMISSION_INCLUDES } = require('../../src/utils/permissionHierarchy');

console.log('🧪 Testing Hierarchical Permissions System\n');

// Test cases
const testCases = [
  {
    name: 'User with orders page access can create orders',
    userPermissions: ['pages.orders_access'],
    checkPermission: 'orders.create',
    expected: true,
    description: 'Page access should include CRUD permissions'
  },
  {
    name: 'User with orders page access can delete orders',
    userPermissions: ['pages.orders_access'],
    checkPermission: 'orders.delete',
    expected: true,
    description: 'Page access should include delete permissions'
  },
  {
    name: 'User with orders view-only cannot create orders',
    userPermissions: ['pages.orders_access', 'orders.view_only'],
    checkPermission: 'orders.create',
    expected: false,
    description: 'View-only should override page access for CRUD operations'
  },
  {
    name: 'User with orders view-only can still view page',
    userPermissions: ['pages.orders_access', 'orders.view_only'],
    checkPermission: 'pages.orders_access',
    expected: true,
    description: 'View-only should not block page access itself'
  },
  {
    name: 'User with products page access can edit pricing',
    userPermissions: ['pages.products_access'],
    checkPermission: 'products.pricing_edit',
    expected: true,
    description: 'Page access should include specialized permissions'
  },
  {
    name: 'User without permissions cannot do anything',
    userPermissions: [],
    checkPermission: 'orders.create',
    expected: false,
    description: 'No permissions should block access'
  },
  {
    name: 'System admin can do everything',
    userPermissions: ['system.full_access'],
    checkPermission: 'orders.create',
    expected: true,
    description: 'Full access should override everything'
  }
];

// Run tests
let passed = 0;
let total = testCases.length;

testCases.forEach((test, index) => {
  try {
    const result = hasPermissionWithHierarchy(test.userPermissions, test.checkPermission);
    const success = result === test.expected;
    
    if (success) {
      console.log(`✅ Test ${index + 1}: ${test.name}`);
      passed++;
    } else {
      console.log(`❌ Test ${index + 1}: ${test.name}`);
      console.log(`   Expected: ${test.expected}, Got: ${result}`);
      console.log(`   Description: ${test.description}`);
    }
  } catch (error) {
    console.log(`💥 Test ${index + 1}: ${test.name} - ERROR`);
    console.log(`   ${error.message}`);
  }
});

console.log(`\n📊 Results: ${passed}/${total} tests passed`);

// Display permission hierarchy
console.log('\n🏗️  Permission Hierarchy:');
Object.entries(PAGE_PERMISSION_INCLUDES).forEach(([pagePermission, includedPermissions]) => {
  console.log(`\n${pagePermission} includes:`);
  includedPermissions.forEach(permission => {
    console.log(`  - ${permission}`);
  });
});

// Test effective permissions calculation
console.log('\n🔧 Effective Permissions Test:');
const testUser = ['pages.orders_access', 'pages.clients_access'];
const effectivePermissions = getEffectivePermissions(testUser);

console.log(`User has: ${testUser.join(', ')}`);
console.log(`Effective permissions: ${effectivePermissions.length} total`);
effectivePermissions.forEach(perm => console.log(`  - ${perm}`));

if (passed === total) {
  console.log('\n🎉 All tests passed! Hierarchical permissions working correctly.');
  process.exit(0);
} else {
  console.log('\n🚨 Some tests failed. Permission system needs fixes.');
  process.exit(1);
}