# Production Cost System - Cache Architecture Analysis & Solutions

## 🚨 Critical Issue Identified

The production cost system has **systemic cache synchronization problems** causing cascading failures across templates, products, components, and cost calculations.

## Current Cache Architecture Problems

### 1. **Fragmented Cache Keys**
```
production-cost-templates
production-cost-templates-product-{categoryId}-{productTypeId}-{sizeId}
production-cost-templates-replacement-{...}
production-cost-template-{id}
production-cost-components
production-cost-components-category-{categoryId}
production-cost-component-values-{productId}
production-cost-component-values-batch
production-cost-component-values-editing-{...}
production-cost-component-values-combinations
production-cost-component-values-template-{templateId}
production-costs-product-line
production-costs-product
```

### 2. **Missing Interdependency Management**

When you change one entity, related caches aren't invalidated:

| Action | What Should Be Invalidated | Currently Missing |
|--------|---------------------------|-------------------|
| Delete Template | - Template caches<br>- Component values using template<br>- Production costs<br>- Product line costs | ❌ Only partial |
| Apply Template | - Product component values<br>- Production costs<br>- Product line costs | ❌ Not invalidated |
| Update Component Value | - Production costs<br>- Product line costs<br>- Template calculations | ❌ Not invalidated |
| Delete Component | - Component values<br>- Templates using component<br>- Production costs | ❌ Not invalidated |

### 3. **Race Conditions**

Multiple users or operations can cause:
- Stale data display
- Overwritten changes
- Inconsistent calculations
- "Ghost" templates/values appearing

## Root Causes

1. **No Central Cache Management**
   - Each hook manages its own cache
   - No awareness of dependencies
   - No coordination between mutations

2. **Optimistic Updates Without Full Sync**
   - Partial cache updates
   - Missing rollback scenarios
   - Incomplete revalidation

3. **Complex Data Relationships**
   ```
   Templates → Component Values → Production Costs
        ↓            ↓              ↓
   Applications → Products → Product Line
   ```

## Comprehensive Solution

### Phase 1: Central Cache Invalidation Service

Create a central service to manage cache dependencies:

```typescript
// cacheInvalidation.service.ts
export class CacheInvalidationService {
  private dependencies = {
    'template-deleted': [
      'production-cost-templates*',
      'production-cost-component-values-template-{id}',
      'production-costs-*',
      'product-line-*'
    ],
    'template-applied': [
      'production-cost-component-values-*',
      'production-costs-*',
      'product-line-*'
    ],
    'component-value-updated': [
      'production-costs-*',
      'product-line-*',
      'production-cost-templates-*'
    ],
    'component-deleted': [
      'production-cost-components*',
      'production-cost-component-values-*',
      'production-cost-templates*',
      'production-costs-*'
    ]
  };

  invalidateRelated(action: string, params: any) {
    const patterns = this.dependencies[action];
    const { cache, mutate } = useSWRConfig();
    
    patterns.forEach(pattern => {
      const regex = new RegExp(pattern.replace('*', '.*').replace('{id}', params.id));
      cache.keys().forEach(key => {
        if (regex.test(key)) {
          mutate(key);
        }
      });
    });
  }
}
```

### Phase 2: Enhanced Mutation Hooks

Update all mutation hooks to use the central service:

```typescript
// Example: useTemplateMutations
const deleteTemplateMutation = useCallback(async (id: string) => {
  try {
    // Optimistic update all related caches
    cacheInvalidation.optimisticUpdate('template-deleted', { id });
    
    const result = await deleteTemplate(id);
    
    // Full cache invalidation
    cacheInvalidation.invalidateRelated('template-deleted', { id });
    
    return result;
  } catch (error) {
    // Rollback all caches
    cacheInvalidation.rollback('template-deleted', { id });
    throw error;
  }
}, []);
```

### Phase 3: Global Cache Sync Hook

Create a hook that ensures cache consistency:

```typescript
export function useCacheSync() {
  const { cache, mutate } = useSWRConfig();
  
  // Listen for global invalidation events
  useEffect(() => {
    const handleGlobalInvalidation = (event: CustomEvent) => {
      const { pattern } = event.detail;
      invalidateCachesByPattern(pattern);
    };
    
    window.addEventListener('swr-invalidate', handleGlobalInvalidation);
    return () => window.removeEventListener('swr-invalidate', handleGlobalInvalidation);
  }, []);
}
```

### Phase 4: Implement Cache Versioning

Add version tracking to detect stale data:

```typescript
const CACHE_VERSION = '1.0.0';

const fetcher = async (key: string) => {
  const data = await fetchData(key);
  return { ...data, _cacheVersion: CACHE_VERSION };
};
```

## Implementation Priority

### 🔴 Critical (Do First)
1. Fix template deletion cache sync (DONE ✅)
2. Fix template application cache invalidation
3. Fix component value updates to invalidate costs

### 🟡 High Priority
4. Implement central cache service
5. Add global cache sync
6. Fix component deletion cascades

### 🟢 Medium Priority
7. Add cache versioning
8. Implement optimistic locking
9. Add cache health monitoring

## Quick Wins

### 1. Add Missing Invalidations
```typescript
// In applyTemplateMutation
await mutate('production-cost-component-values-*');
await mutate('production-costs-*');
```

### 2. Use Broader Cache Patterns
```typescript
// Instead of specific keys
mutate(`production-cost-component-values-${productId}`);

// Use pattern matching
mutate((key) => key.startsWith('production-cost-component-values'));
```

### 3. Add Cache Debugging
```typescript
export function debugCacheState() {
  const { cache } = useSWRConfig();
  console.log('Active caches:', Array.from(cache.keys()));
}
```

## Monitoring & Testing

### Cache Health Checks
- Monitor cache hit/miss ratios
- Track invalidation frequency
- Detect orphaned caches
- Log cache errors

### Testing Strategy
1. Multi-user scenarios
2. Rapid mutations
3. Network failure recovery
4. Cache consistency validation

## Conclusion

The production cost system needs a **complete cache architecture overhaul** to prevent cascading failures. The current approach of isolated cache management creates data inconsistencies that compound across the system.

Implementing these solutions will:
- Eliminate "ghost" data issues
- Prevent race conditions
- Ensure data consistency
- Improve performance
- Reduce user-reported bugs

This is a **critical infrastructure issue** that affects the entire production cost system reliability.