import React from 'react'
import { SWRConfig } from 'swr'

interface SWRConfigProviderProps {
  children: React.ReactNode
}

// Global fetcher function for all SWR requests
const globalFetcher = async (url: string) => {
  const response = await fetch(url)
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }
  return response.json()
}

// Global error handler for SWR
const handleSWRError = (error: Error, key: string) => {
  console.error('SWR Error:', { error: error.message, key })
  // Add error tracking service here if needed
  // errorTracker.captureException(error, { key })
}

/**
 * Global SWR Configuration Provider
 * 
 * Provides consistent SWR configuration across the entire application
 * including fetcher, error handling, revalidation settings, and caching
 */
export function SWRConfigProvider({ children }: SWRConfigProviderProps) {
  return (
    <SWRConfig
      value={{
        // Global fetcher - can be overridden by individual hooks if needed
        fetcher: globalFetcher,
        
        // Global error handling
        onError: handleSWRError,
        
        // Revalidation settings for performance
        revalidateOnFocus: false,     // Prevent excessive refetches on window focus
        revalidateOnReconnect: true,  // Refetch when network reconnects
        
        // Caching and deduplication
        dedupingInterval: 30000,      // 30 seconds - prevent duplicate requests
        focusThrottleInterval: 10000, // 10 seconds - throttle focus-based revalidation
        
        // Error retry configuration
        errorRetryCount: 3,           // Retry failed requests up to 3 times
        errorRetryInterval: 1000,     // 1 second initial retry interval
        shouldRetryOnError: true,     // Enable automatic retries
        
        // Performance optimizations
        keepPreviousData: true,       // Show previous data while revalidating
        
        // Conditional retry logic with exponential backoff
        onErrorRetry: (error, key, config, revalidate, { retryCount }) => {
          // Don't retry on 4xx errors (client errors)
          if (error.status >= 400 && error.status < 500) return
          
          // Don't retry more than 3 times
          if (retryCount >= 3) return
          
          // Exponential backoff: 1s, 2s, 4s
          const retryInterval = Math.min(1000 * (2 ** retryCount), 10000)
          
          setTimeout(() => {
            revalidate({ retryCount: retryCount + 1 })
          }, retryInterval)
        },
        
        // Custom comparison function for better performance
        compare: (a, b) => {
          // Use JSON.stringify for deep comparison (can be optimized further if needed)
          return JSON.stringify(a) === JSON.stringify(b)
        }
      }}
    >
      {children}
    </SWRConfig>
  )
}

// Export global fetcher for use in custom hooks that need specific fetch logic
export { globalFetcher }