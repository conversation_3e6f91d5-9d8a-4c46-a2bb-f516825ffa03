/**
 * PermissionsContext - Context provider for permissions state
 * 
 * Following CLAUDE.md guidelines:
 * - Single responsibility: provide permissions context
 * - <250 lines
 * - Pure functions and immutable data patterns
 */

import React, { createContext, useContext, type ReactNode } from 'react';
import { usePermissions } from '../hooks/permissions';
import type { UsePermissionsResult } from '../types/permissions.types';

// ============================================================================
// PERMISSIONS CONTEXT
// ============================================================================

// Secure default context - deny all permissions by default
const defaultContext: UsePermissionsResult = {
  permissions: [],
  loading: true,
  error: null,
  checkPermission: () => false,  // Secure default: deny access
  checkMultiplePermissions: () => ({}),
  hasFullAccess: false,
  refreshPermissions: async () => {}
};

const PermissionsContext = createContext<UsePermissionsResult>(defaultContext);

// ============================================================================
// PERMISSIONS PROVIDER
// ============================================================================

interface PermissionsProviderProps {
  readonly children: ReactNode;
}

export const PermissionsProvider: React.FC<PermissionsProviderProps> = ({ children }) => {
  const permissionsValue = usePermissions();
  
  return (
    <PermissionsContext.Provider value={permissionsValue}>
      {children}
    </PermissionsContext.Provider>
  );
};

// ============================================================================
// PERMISSIONS CONTEXT HOOK
// ============================================================================

/**
 * Hook to access permissions context
 * Throws error if used outside PermissionsProvider
 */
export const usePermissionsContext = (): UsePermissionsResult => {
  const context = useContext(PermissionsContext);
  
  if (!context) {
    throw new Error('usePermissionsContext must be used within PermissionsProvider');
  }
  
  return context;
};