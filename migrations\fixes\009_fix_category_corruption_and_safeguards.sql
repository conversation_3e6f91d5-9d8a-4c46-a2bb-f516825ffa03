-- Migration: Fix Category Corruption and Add Comprehensive Safeguards
-- This migration addresses the root causes of category mapping issues

BEGIN;

-- ============================================
-- PART 1: DATA AUDIT AND LOGGING
-- ============================================

-- Create audit table for tracking data corruption issues
CREATE TABLE IF NOT EXISTS data_integrity_audit (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    audit_type TEXT NOT NULL,
    table_name TEXT NOT NULL,
    record_id UUID,
    issue_description TEXT NOT NULL,
    old_value JSONB,
    new_value JSONB,
    fix_applied BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    fixed_at TIMESTAMPTZ
);

-- Log current corrupted data before fixing
INSERT INTO data_integrity_audit (
    audit_type, 
    table_name, 
    record_id, 
    issue_description, 
    old_value,
    fix_applied
)
SELECT 
    'CATEGORY_CORRUPTION',
    'order_items',
    item_id,
    'Order item has invalid Default category - should be: ' || 
    CASE 
        WHEN product_type = 'Photo Board Normal' THEN 'Photo Boards'
        WHEN product_type = 'VintageX' THEN 'Vintage Family'
        ELSE 'UNKNOWN_MAPPING'
    END,
    jsonb_build_object(
        'item_id', item_id,
        'current_category', product,
        'product_type', product_type,
        'size', size,
        'created_at', created_at
    ),
    false
FROM order_items 
WHERE product = 'Default' OR product = '' OR product IS NULL;

-- ============================================
-- PART 2: FIX CORRUPTED ORDER ITEMS
-- ============================================

-- Fix Photo Board Normal items (Default -> Photo Boards)
UPDATE order_items 
SET 
    product = 'Photo Boards',
    updated_at = NOW()
WHERE product = 'Default' 
AND product_type = 'Photo Board Normal';

-- Fix VintageX items (Default -> Vintage Family)  
UPDATE order_items 
SET 
    product = 'Vintage Family',
    updated_at = NOW()
WHERE product = 'Default' 
AND product_type = 'VintageX';

-- Fix empty category items that have valid product types
UPDATE order_items 
SET 
    product = CASE
        WHEN product_type ILIKE '%photo board%' THEN 'Photo Boards'
        WHEN product_type ILIKE '%photo book%' THEN 'Photo Books'
        WHEN product_type ILIKE '%photo frame%' THEN 'Photo Frames'
        WHEN product_type ILIKE '%photo print%' THEN 'Photo Prints'
        WHEN product_type ILIKE '%vintage%' THEN 'Vintage Family'
        WHEN product_type ILIKE '%ark%' OR product_type ILIKE '%key%' OR product_type ILIKE '%branded%' THEN 'Branded Items'
        ELSE 'Photo Boards'  -- Default fallback for unmatched
    END,
    updated_at = NOW()
WHERE (product = '' OR product IS NULL) 
AND product_type IS NOT NULL 
AND product_type != '';

-- Update audit records as fixed
UPDATE data_integrity_audit 
SET 
    fix_applied = true,
    fixed_at = NOW(),
    new_value = jsonb_build_object(
        'fix_method', 'migration_009',
        'fixed_category', CASE 
            WHEN old_value->>'product_type' = 'Photo Board Normal' THEN 'Photo Boards'
            WHEN old_value->>'product_type' = 'VintageX' THEN 'Vintage Family'
            ELSE 'INFERRED_FROM_PRODUCT_TYPE'
        END
    )
WHERE audit_type = 'CATEGORY_CORRUPTION'
AND table_name = 'order_items'
AND fix_applied = false;

-- ============================================
-- PART 3: CREATE MISSING PRODUCTION_COST_CALCULATIONS TABLE
-- ============================================

-- Create the table that was referenced but never created
CREATE TABLE IF NOT EXISTS production_cost_calculations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    product_id UUID REFERENCES products(id),
    total_production_cost NUMERIC NOT NULL DEFAULT 0,
    components_breakdown JSONB DEFAULT '[]'::JSONB,
    calculation_date TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    -- Additional columns from the migration that tried to ALTER this table
    order_item_id UUID REFERENCES order_items(item_id),
    quantity INTEGER,
    base_production_cost NUMERIC,
    additional_costs JSONB,
    rule_version INTEGER,
    calculation_context JSONB
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_production_cost_calculations_product_id 
ON production_cost_calculations(product_id);

CREATE INDEX IF NOT EXISTS idx_production_cost_calculations_order_item_id 
ON production_cost_calculations(order_item_id);

-- ============================================
-- PART 4: DATA INTEGRITY CONSTRAINTS
-- ============================================

-- Create function to validate order item categories
CREATE OR REPLACE FUNCTION validate_order_item_category()
RETURNS TRIGGER AS $$
BEGIN
    -- Ensure category is not 'Default'
    IF NEW.product = 'Default' THEN
        RAISE EXCEPTION 'Invalid category "Default" not allowed. Use a valid category from product_attributes table.';
    END IF;
    
    -- Ensure category exists in product_attributes
    IF NEW.product IS NOT NULL AND NEW.product != '' THEN
        IF NOT EXISTS (
            SELECT 1 FROM product_attributes 
            WHERE attribute_type = 'CATEGORY' 
            AND value = NEW.product 
            AND status = 'active'
        ) THEN
            RAISE EXCEPTION 'Category "%" does not exist in product_attributes table. Valid categories are: %', 
                NEW.product,
                (SELECT string_agg(value, ', ' ORDER BY value) 
                 FROM product_attributes 
                 WHERE attribute_type = 'CATEGORY' AND status = 'active');
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to validate categories on insert/update
DROP TRIGGER IF EXISTS validate_order_item_category_trigger ON order_items;
CREATE TRIGGER validate_order_item_category_trigger
    BEFORE INSERT OR UPDATE OF product ON order_items
    FOR EACH ROW
    EXECUTE FUNCTION validate_order_item_category();

-- ============================================
-- PART 5: MONITORING AND HEALTH CHECKS
-- ============================================

-- Create function to check for data integrity issues
CREATE OR REPLACE FUNCTION check_category_data_integrity()
RETURNS TABLE (
    issue_type TEXT,
    table_name TEXT,
    issue_count BIGINT,
    sample_records JSONB,
    recommended_action TEXT
) AS $$
BEGIN
    -- Check for invalid categories in order_items
    RETURN QUERY
    SELECT 
        'INVALID_CATEGORY'::TEXT,
        'order_items'::TEXT,
        COUNT(*)::BIGINT,
        jsonb_agg(
            jsonb_build_object(
                'item_id', oi.item_id,
                'category', oi.product,
                'product_type', oi.product_type
            )
        ),
        'Update category to valid value from product_attributes table'::TEXT
    FROM order_items oi
    LEFT JOIN product_attributes pa ON pa.value = oi.product AND pa.attribute_type = 'CATEGORY'
    WHERE oi.product IS NOT NULL 
    AND oi.product != ''
    AND pa.id IS NULL
    HAVING COUNT(*) > 0;
    
    -- Check for null/empty categories
    RETURN QUERY
    SELECT 
        'MISSING_CATEGORY'::TEXT,
        'order_items'::TEXT,
        COUNT(*)::BIGINT,
        jsonb_agg(
            jsonb_build_object(
                'item_id', oi.item_id,
                'product_type', oi.product_type,
                'size', oi.size
            )
        ),
        'Set category based on product_type mapping'::TEXT
    FROM order_items oi
    WHERE oi.product IS NULL OR oi.product = ''
    HAVING COUNT(*) > 0;
    
    -- Check for orphaned production cost data
    RETURN QUERY
    SELECT 
        'ORPHANED_PRODUCTION_COST'::TEXT,
        'order_items'::TEXT,
        COUNT(*)::BIGINT,
        jsonb_agg(
            jsonb_build_object(
                'item_id', oi.item_id,
                'production_cost', oi.production_cost,
                'category', oi.product
            )
        ),
        'Recalculate production cost with correct category mapping'::TEXT
    FROM order_items oi
    WHERE oi.production_cost = 0 
    AND oi.product IS NOT NULL 
    AND oi.product != ''
    AND EXISTS (
        SELECT 1 FROM product_attributes 
        WHERE attribute_type = 'CATEGORY' 
        AND value = oi.product
    )
    HAVING COUNT(*) > 0;
END;
$$ LANGUAGE plpgsql;

-- ============================================
-- PART 6: FORCE PRODUCTION COST RECALCULATION
-- ============================================

-- Update the previously fixed items to trigger production cost recalculation
UPDATE order_items 
SET updated_at = NOW()
WHERE item_id IN (
    SELECT record_id::UUID 
    FROM data_integrity_audit 
    WHERE audit_type = 'CATEGORY_CORRUPTION' 
    AND fix_applied = true
);

-- ============================================
-- PART 7: LOG MIGRATION COMPLETION
-- ============================================

INSERT INTO data_integrity_audit (
    audit_type,
    table_name,
    issue_description,
    new_value,
    fix_applied
) VALUES (
    'MIGRATION_COMPLETION',
    'system',
    'Migration 009: Category corruption fix and safeguards applied',
    jsonb_build_object(
        'migration_version', '009',
        'timestamp', NOW(),
        'actions_performed', ARRAY[
            'fixed_corrupted_categories',
            'created_missing_table',
            'added_validation_triggers',
            'created_monitoring_functions'
        ]
    ),
    true
);

COMMIT;

-- ============================================
-- POST-MIGRATION VERIFICATION
-- ============================================

-- Check if any integrity issues remain
SELECT * FROM check_category_data_integrity();