import { supabase } from '../../lib/supabase'

export class PermissionQueries {
  /**
   * Get all available permissions
   */
  static async getPermissions(): Promise<Array<{
    key: string
    name: string
    description: string | null
    category: string
  }>> {
    const { data, error } = await supabase
      .from('permissions')
      .select('key, name, description, category')
      .eq('is_active', true)
      .order('category, name')

    if (error) {
      throw new Error(`Failed to fetch permissions: ${error.message}`)
    }

    return data || []
  }

  /**
   * Get user activity/audit log
   */
  static async getUserActivity(userId?: string, limit = 50): Promise<any[]> {
    // Note: This would require implementing an audit log system
    // For now, return empty array as placeholder
    return []
  }
}