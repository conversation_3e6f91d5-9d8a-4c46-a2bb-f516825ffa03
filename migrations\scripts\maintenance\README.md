# Maintenance Scripts

Migration runners, database maintenance utilities, and system administration tools.

## Files in this directory:

### Migration Management
- `migrate-permissions-v2.js` - Permission system v2 migration utility
- `run-migration-v2.sh` - Migration runner for v2 system updates

## Usage

### Permission System Migration
```bash
# Run permission system v2 migration
node migrate-permissions-v2.js
```

### General Migration Runner
```bash
# Run migration scripts
./run-migration-v2.sh
```

## Features

- **Automated migration execution** - Run multiple migrations in sequence
- **Error handling** - Detect and report migration failures
- **Rollback support** - Ability to revert failed migrations
- **Progress tracking** - Monitor migration progress and status

## Migration Best Practices

1. **Backup first** - Always create backups before migrations
2. **Test in development** - Validate migrations in dev environment
3. **Monitor logs** - Check migration logs for errors or warnings
4. **Verify results** - Confirm migrations completed successfully

## Configuration

- Update database connection settings in scripts
- Configure migration paths and sequences
- Set appropriate logging levels
- Define rollback procedures

## Troubleshooting

- Check script logs for detailed error information
- Verify database connectivity and permissions
- Ensure migration files are accessible
- Validate migration prerequisites are met

## Notes

- Always test maintenance scripts in development first
- Keep migration scripts version-controlled
- Document any manual steps required
- Monitor system performance during migrations