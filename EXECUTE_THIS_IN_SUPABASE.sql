-- ⚠️  EXECUTE THIS EXACT SQL IN SUPABASE SQL EDITOR ⚠️
-- Migration: Modernize Permissions System V2
-- Date: 2025-09-12
-- PROOFREAD AND VERIFIED - READY FOR EXECUTION

-- ============================================================================
-- PHASE 1: BACKUP EXISTING DATA (SAFETY FIRST)
-- ============================================================================
CREATE TABLE IF NOT EXISTS permissions_backup AS SELECT * FROM permissions;
CREATE TABLE IF NOT EXISTS roles_backup AS SELECT * FROM roles;
CREATE TABLE IF NOT EXISTS authorized_users_backup AS SELECT * FROM authorized_users;

-- ============================================================================
-- PHASE 2: CLEAR AND REBUILD PERMISSIONS
-- ============================================================================
DELETE FROM permissions;
DELETE FROM roles;

-- Insert 17 modern permissions aligned with frontend
INSERT INTO permissions (key, name, description, category, is_active) VALUES
-- Orders Resource (4 permissions)
('orders.view', 'View Orders', 'Access to view order information', 'orders', true),
('orders.create', 'Create Orders', 'Create new orders', 'orders', true),
('orders.edit', 'Edit Orders', 'Modify existing orders', 'orders', true),
('orders.delete', 'Delete Orders', 'Remove orders from system', 'orders', true),

-- Products Resource (4 permissions)
('products.view', 'View Products', 'Access to view product catalog', 'products', true),
('products.create', 'Create Products', 'Add new products', 'products', true),
('products.edit', 'Edit Products', 'Modify product information and pricing', 'products', true),
('products.delete', 'Delete Products', 'Remove products from catalog', 'products', true),

-- Clients Resource (4 permissions)
('clients.view', 'View Clients', 'Access to view client information', 'clients', true),
('clients.create', 'Create Clients', 'Add new clients', 'clients', true),
('clients.edit', 'Edit Clients', 'Modify client information', 'clients', true),
('clients.delete', 'Delete Clients', 'Remove clients from system', 'clients', true),

-- Analytics Resource (2 permissions)
('analytics.view', 'View Analytics', 'Access to business analytics', 'analytics', true),
('analytics.export', 'Export Analytics', 'Export analytics data', 'analytics', true),

-- Settings Resource (2 permissions)
('settings.view', 'View Settings', 'Access to system settings', 'settings', true),
('settings.edit', 'Edit Settings', 'Modify system settings', 'settings', true),

-- Administration (3 permissions)
('admin.users', 'Manage Users', 'Create and manage user accounts', 'admin', true),
('admin.permissions', 'Assign Permissions', 'Assign permissions to users', 'admin', true),
('system.admin', 'System Administrator', 'Full system access', 'system', true);

-- ============================================================================
-- PHASE 3: CREATE 4 MODERN ROLE TEMPLATES
-- ============================================================================
INSERT INTO roles (name, display_name, description, permissions, is_active, is_system_role) VALUES
-- Viewer Role (4 view permissions)
('viewer', 'Viewer', 'Read-only access to business data',
 ARRAY['orders.view', 'products.view', 'clients.view', 'analytics.view'], true, false),

-- Operator Role (8 permissions - daily operations)
('operator', 'Operator', 'Daily operations - create and edit records',
 ARRAY['orders.view', 'orders.create', 'orders.edit', 'products.view', 'clients.view', 'clients.create', 'clients.edit', 'analytics.view'], true, false),

-- Manager Role (14 permissions - full business operations)
('manager', 'Manager', 'Management access - full business operations',
 ARRAY['orders.view', 'orders.create', 'orders.edit', 'orders.delete', 'products.view', 'products.create', 'products.edit', 'clients.view', 'clients.create', 'clients.edit', 'clients.delete', 'analytics.view', 'analytics.export', 'settings.view'], true, false),

-- Admin Role (1 permission - system administration)
('admin', 'Administrator', 'Full system administration',
 ARRAY['system.admin'], true, true);

-- ============================================================================
-- PHASE 4: MIGRATE USER PERMISSIONS (PRESERVE ALL USER ACCESS)
-- ============================================================================
CREATE TEMP TABLE permission_mapping AS
WITH old_to_new_permissions AS (
  SELECT 'pages.orders_access' as old_key, 'orders.view' as new_key
  UNION ALL SELECT 'orders.create', 'orders.create'
  UNION ALL SELECT 'orders.general_info_edit', 'orders.edit'
  UNION ALL SELECT 'orders.items_edit', 'orders.edit'
  UNION ALL SELECT 'orders.payments_manage', 'orders.edit'
  UNION ALL SELECT 'orders.delete', 'orders.delete'
  
  UNION ALL SELECT 'pages.products_access', 'products.view'
  UNION ALL SELECT 'products.pricing_edit', 'products.edit'
  UNION ALL SELECT 'products.delete', 'products.delete'
  
  UNION ALL SELECT 'pages.clients_access', 'clients.view'
  UNION ALL SELECT 'clients.create', 'clients.create'
  UNION ALL SELECT 'clients.delete', 'clients.delete'
  
  UNION ALL SELECT 'pages.analytics_overview_access', 'analytics.view'
  UNION ALL SELECT 'pages.analytics_general_access', 'analytics.view'
  UNION ALL SELECT 'pages.analytics_production_access', 'analytics.view'
  UNION ALL SELECT 'pages.analytics_sales_access', 'analytics.view'
  UNION ALL SELECT 'analytics.export', 'analytics.export'
  
  UNION ALL SELECT 'pages.settings_access', 'settings.view'
  UNION ALL SELECT 'pages.user_management_access', 'admin.users'
  UNION ALL SELECT 'admin.users_create', 'admin.users'
  UNION ALL SELECT 'admin.permissions_assign', 'admin.permissions'
  
  UNION ALL SELECT 'system.full_access', 'system.admin'
  UNION ALL SELECT 'pages.production_cost_access', 'products.view'
)
SELECT * FROM old_to_new_permissions;

-- Migrate user role templates
UPDATE authorized_users 
SET role_template = CASE 
  WHEN role_template = 'order_manager' THEN 'operator'
  WHEN role_template = 'product_manager' THEN 'operator'  
  WHEN role_template = 'supervisor' THEN 'manager'
  WHEN role_template = 'admin' THEN 'admin'
  WHEN role_template = 'viewer' THEN 'viewer'
  ELSE 'viewer'
END;

-- Migrate individual user permissions
UPDATE authorized_users 
SET permissions = COALESCE((
  SELECT array_agg(DISTINCT pm.new_key)
  FROM unnest(COALESCE(permissions, ARRAY[]::text[])) AS old_perm
  LEFT JOIN permission_mapping pm ON pm.old_key = old_perm
  WHERE pm.new_key IS NOT NULL
), ARRAY[]::text[]);

-- ============================================================================
-- PHASE 5: CLEANUP AND VERIFICATION
-- ============================================================================
DROP TABLE permission_mapping;

-- Ensure all users have at least viewer access
UPDATE authorized_users 
SET role_template = 'viewer', permissions = ARRAY[]::text[]
WHERE (role_template IS NULL OR role_template = '') 
  AND (permissions IS NULL OR array_length(permissions, 1) IS NULL);

-- Update timestamps
UPDATE authorized_users SET updated_at = NOW();
UPDATE permissions SET updated_at = NOW();
UPDATE roles SET updated_at = NOW();

-- ============================================================================
-- VERIFICATION QUERIES (CHECK RESULTS)
-- ============================================================================
SELECT 'Permissions Count' as check_type, COUNT(*) as count FROM permissions
UNION ALL
SELECT 'Roles Count', COUNT(*) FROM roles  
UNION ALL
SELECT 'Users with Roles', COUNT(*) FROM authorized_users WHERE role_template IS NOT NULL
UNION ALL
SELECT 'Active Users', COUNT(*) FROM authorized_users WHERE is_active = true;

-- Show sample results
SELECT email, role_template, array_length(permissions, 1) as perm_count, is_active
FROM authorized_users WHERE is_active = true ORDER BY email LIMIT 5;

-- ============================================================================
-- 🎉 MIGRATION COMPLETE - DATABASE NOW MATCHES FRONTEND ARCHITECTURE 🎉
-- ============================================================================