-- ============================================================================
-- AUTHORIZED USERS TABLE BACKUP - Created via MCP
-- Backup Date: 2025-01-05 12:42:44  
-- Source: Supabase Project wheufegilqkbcsixkoka (aming-test)
-- Total Records: 5 users
-- ============================================================================

-- Current user authorization data (contains sensitive information)
INSERT INTO authorized_users (id, email, first_name, last_name, department, permissions, role_template, is_active, invited_at, first_login_at, last_login_at, notes, created_at, updated_at) VALUES
('331f58a0-ae3a-49ac-8558-f10e184bf020', '<EMAIL>', 'System', 'Administrator', null, 
 '["system.full_access"]', 'admin', true, 
 '2025-08-08 20:43:04.411113+00', null, '2025-08-14 21:45:51.099+00', 
 'Initial system administrator - created during Phase 1 setup', 
 '2025-08-08 20:43:04.411113+00', '2025-08-14 21:45:52.319894+00'),

('e80166d3-b0f3-43a8-bf5f-57872d97f7f2', '<EMAIL>', 'mark', 'ran', 'operations', 
 '["pages.orders_access","pages.clients_access","pages.production_cost_access"]', null, true, 
 '2025-08-12 15:28:57.139847+00', null, null, null, 
 '2025-08-12 15:28:57.139847+00', '2025-08-12 18:59:57.914156+00'),

('54e33f44-b7bf-416b-8dca-1a2aa6616a12', '<EMAIL>', 'mark', 'LIO', 'human_resources', 
 '["system.full_access"]', null, true, 
 '2025-08-12 19:05:07.780258+00', null, null, null, 
 '2025-08-12 19:05:07.780258+00', '2025-08-14 06:45:26.460544+00'),

('3f6cc4a6-3a64-446c-a521-5cc5134a270b', '<EMAIL>', 'Saniget', 'Multimedia', 'operations', 
 '["system.full_access"]', 'admin', true, 
 '2025-08-13 07:29:05.794477+00', null, '2025-08-13 08:11:43.661+00', null, 
 '2025-08-13 07:29:05.794477+00', '2025-08-14 06:06:07.928703+00'),

('26d5f03c-8547-4354-9c33-0076ad5690c9', '<EMAIL>', 'Ken', 'Kan', 'management', 
 '["pages.orders_access","pages.production_cost_access","pages.products_access","pages.clients_access"]', null, true, 
 '2025-08-14 07:21:05.922745+00', null, null, null, 
 '2025-08-14 07:21:05.922745+00', '2025-08-17 08:31:53.142211+00');

-- ============================================================================
-- ANALYSIS OF CURRENT USER PERMISSIONS (Migration Strategy)  
-- ============================================================================

/*
USER PERMISSION ANALYSIS:

1. ADMIN USERS (Will migrate to 'admin' role):
   - <EMAIL>: Has system.full_access + admin role ✓
   - <EMAIL>: Has system.full_access + admin role ✓  
   - <EMAIL>: Has system.full_access but no role template

2. OPERATIONAL USERS (Will migrate to 'manager' or 'operator'):
   - <EMAIL>: Has 3 page-level permissions, operations dept
   - <EMAIL>: Has 4 page-level permissions, management dept

MIGRATION STRATEGY:

✅ ADMIN USERS:
   - Users with 'system.full_access' → 'admin' role
   - Clear permissions array to use role-based only
   
✅ MANAGER USERS:  
   - Users with 4+ permissions → 'manager' role
   - Management department → 'manager' role
   
✅ OPERATOR USERS:
   - Users with create permissions → 'operator' role  
   - Operations department → 'operator' role
   
✅ VIEWER USERS:
   - Users with only view permissions → 'viewer' role
   - Safe default for uncertain cases

EXPECTED MIGRATION RESULTS:
- <EMAIL> → admin (already has role)
- <EMAIL> → admin (already has role)  
- <EMAIL> → admin (has system.full_access)
- <EMAIL> → operator (operations dept, 3 perms)
- <EMAIL> → manager (management dept, 4 perms)

POST-MIGRATION:
- All users will have clear role_template assignments
- Permission arrays simplified (most will be empty, inheriting from role)
- Consistent permission format across all users
*/