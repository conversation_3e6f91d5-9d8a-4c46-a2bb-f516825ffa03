import { useState, useCallback } from 'react';
import { isPasswordValid } from '../../utils/passwordValidation';

interface AuthFormState {
  loading: boolean;
  error: string | null;
  success: string | null;
}

interface AuthFormActions {
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setSuccess: (success: string | null) => void;
  clearMessages: () => void;
}

interface UseAuthFormOptions {
  onSuccess?: (data?: any) => void;
  onError?: (error: string) => void;
}

/**
 * Common auth form state management hook
 * Following CLAUDE.md guidelines - focused functionality under 250 lines
 */
export function useAuthForm(options: UseAuthFormOptions = {}) {
  const [state, setState] = useState<AuthFormState>({
    loading: false,
    error: null,
    success: null
  });

  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({ ...prev, loading }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState(prev => ({ ...prev, error }));
    if (error && options.onError) {
      options.onError(error);
    }
  }, [options]);

  const setSuccess = useCallback((success: string | null, data?: any) => {
    setState(prev => ({ ...prev, success }));
    if (success && options.onSuccess) {
      options.onSuccess(data);
    }
  }, [options]);

  const clearMessages = useCallback(() => {
    setState(prev => ({ ...prev, error: null, success: null }));
  }, []);

  const actions: AuthFormActions = {
    setLoading,
    setError,
    setSuccess,
    clearMessages
  };

  return {
    ...state,
    ...actions
  };
}

/**
 * Email validation utilities
 */
export const validateEmail = (email: string): string | null => {
  if (!email.trim()) {
    return 'Please enter your email address';
  }

  const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
  if (!emailRegex.test(email)) {
    return 'Please enter a valid email address';
  }

  return null;
};

/**
 * Password validation utilities
 */
export const validatePassword = (password: string, isRequired = true): string | null => {
  if (isRequired && !password) {
    return 'Password is required';
  }

  if (password && !isPasswordValid(password)) {
    return 'Password does not meet security requirements';
  }

  return null;
};

/**
 * Password confirmation validation
 */
export const validatePasswordConfirmation = (
  password: string, 
  confirmPassword: string
): string | null => {
  if (!confirmPassword) {
    return 'Please confirm your password';
  }

  if (password !== confirmPassword) {
    return 'Passwords do not match';
  }

  return null;
};

/**
 * Name validation utilities
 */
export const validateName = (name: string, fieldName = 'Name'): string | null => {
  if (!name.trim()) {
    return `${fieldName} is required`;
  }

  if (name.trim().length < 2) {
    return `${fieldName} must be at least 2 characters`;
  }

  return null;
};

/**
 * OTP/PIN validation
 */
export const validateOTP = (otp: string, expectedLength = 6): string | null => {
  if (!otp.trim()) {
    return 'Please enter the verification code';
  }

  if (otp.length !== expectedLength) {
    return `Verification code must be ${expectedLength} digits`;
  }

  if (!/^\d+$/.test(otp)) {
    return 'Verification code must contain only numbers';
  }

  return null;
};

/**
 * Comprehensive form validation for auth forms
 */
interface AuthFormData {
  email?: string;
  password?: string;
  confirmPassword?: string;
  firstName?: string;
  lastName?: string;
  otp?: string;
}

interface ValidationOptions {
  requirePassword?: boolean;
  requireConfirmation?: boolean;
  requireNames?: boolean;
  requireOTP?: boolean;
}

export const validateAuthForm = (
  data: AuthFormData, 
  options: ValidationOptions = {}
): string | null => {
  // Email validation
  if (data.email !== undefined) {
    const emailError = validateEmail(data.email);
    if (emailError) return emailError;
  }

  // Password validation
  if (options.requirePassword || data.password) {
    const passwordError = validatePassword(data.password || '', options.requirePassword);
    if (passwordError) return passwordError;
  }

  // Password confirmation validation
  if (options.requireConfirmation && data.password && data.confirmPassword !== undefined) {
    const confirmError = validatePasswordConfirmation(data.password, data.confirmPassword);
    if (confirmError) return confirmError;
  }

  // Name validation
  if (options.requireNames) {
    if (data.firstName !== undefined) {
      const firstNameError = validateName(data.firstName, 'First name');
      if (firstNameError) return firstNameError;
    }

    if (data.lastName !== undefined) {
      const lastNameError = validateName(data.lastName, 'Last name');
      if (lastNameError) return lastNameError;
    }
  }

  // OTP validation
  if (options.requireOTP && data.otp !== undefined) {
    const otpError = validateOTP(data.otp);
    if (otpError) return otpError;
  }

  return null;
};