/**
 * usePermissions Hook - Main permission management hook
 * 
 * Following CLAUDE.md guidelines:
 * - Max 3 useState, 2 useEffect
 * - Single responsibility: permission state management
 * - <250 lines
 */

import { useCallback } from 'react';
import useSWR from 'swr';
import { useAuth } from '../../contexts/AuthContext';
import { PermissionsService } from '../../services/permissions';
import type {
  PermissionKey,
  BulkPermissionCheckResult,
  UsePermissionsResult
} from '../../types/permissions.types';

// ============================================================================
// MAIN PERMISSIONS HOOK WITH SWR (CLAUDE.md REQUIREMENT)
// ============================================================================

export const usePermissions = (): UsePermissionsResult => {
  const { user, authorized, permissions: userPermissions } = useAuth();
  
  // Use permissions directly from AuthContext (already cached there)
  const { data: permissions, error, isLoading, mutate } = useSWR(
    // Array-based cache key (CLAUDE.md requirement)
    user?.email && authorized ? ['user-permissions', user.email] : null,
    
    // Fetcher function - return permissions from AuthContext
    async () => {
      // Permissions are already loaded and cached in AuthContext
      return userPermissions || [];
    },
    
    // SWR options following CLAUDE.md standards
    {
      revalidateOnFocus: false,
      dedupingInterval: 30000,           // 30 second deduplication
      staleTime: 5 * 60 * 1000,          // 5 minute stale time (CLAUDE.md standard)
      keepPreviousData: true,            // Performance optimization
      errorRetryCount: 3,                // Resilience
      errorRetryInterval: 1000           // 1 second retry delay
    }
  );

  // Permission checking functions
  const checkPermission = useCallback((permission: PermissionKey): boolean => {
    // Check if user is authorized and has the permission
    if (!authorized || !permissions) {
      return false;
    }

    return permissions.includes(permission) || permissions.includes('system.full_access');
  }, [authorized, permissions]);

  const checkMultiplePermissions = useCallback((
    permissionList: PermissionKey[]
  ): BulkPermissionCheckResult => {
    const results: BulkPermissionCheckResult = {};
    
    permissionList.forEach(perm => {
      results[perm] = checkPermission(perm);
    });

    return results;
  }, [checkPermission]);

  const refreshPermissions = useCallback(async (): Promise<void> => {
    try {
      // Refresh permissions from AuthContext and update SWR cache
      await mutate();
    } catch (err) {
      console.error('Failed to refresh permissions:', err);
      // SWR handles error state automatically
    }
  }, [mutate]);

  const hasFullAccess = checkPermission('system.full_access');

  return {
    permissions: permissions || [],
    loading: isLoading,
    error: error?.message || null,
    checkPermission,
    checkMultiplePermissions,
    hasFullAccess,
    refreshPermissions
  };
};