import { useState } from 'react'
import useSWR from 'swr'
import { fetchOrders, fetchOrdersEssential } from '../../services'
import type { Order } from '../../pages/Orders/types'
import { logger } from '../../utils/logger'
import { getOrdersKey } from './useOrdersBasic'

// Key generator functions for progressive loading
export const getOrdersEssentialKey = () => '/orders/essential'

/**
 * Progressive loading hook - fetches 25 orders immediately, then loads the rest in background
 * Simplified from the original complex implementation
 */
export function useOrdersProgressive() {
  // State to track if we have large filtered data
  const [hasLargeFilteredData, setHasLargeFilteredData] = useState(false)
  
  // Fetch essential orders immediately (first 25)
  const { 
    data: essentialOrders, 
    error: essentialError, 
    isLoading: isLoadingEssential,
    isValidating: isValidatingEssential,
    mutate: mutateEssential
  } = useSWR(
    hasLargeFilteredData ? null : getOrdersEssentialKey(),
    async () => {
      logger.debug('useOrdersProgressive: Fetching essential orders')
      return await fetchOrdersEssential({ limit: 25, offset: 0 })
    },
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      dedupingInterval: 60000,
      keepPreviousData: true
    }
  )

  // Fetch complete orders in background
  const {
    data: allOrders,
    error: allOrdersError,
    isLoading: isLoadingAll,
    isValidating: isValidatingAll,
    mutate: mutateAll
  } = useSWR(
    getOrdersKey(),
    async () => {
      logger.debug('useOrdersProgressive: Fetching all orders in background')
      return await fetchOrders()
    },
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      dedupingInterval: 10000,
      keepPreviousData: true
    }
  )

  // Determine which data to show
  const orders = allOrders && allOrders.length > 0 ? allOrders : essentialOrders || []
  
  const isLoading = isLoadingEssential && !essentialOrders && !allOrders
  const isBackgroundLoading = isLoadingAll && !!essentialOrders && !allOrders
  const error = allOrdersError || essentialError
  
  // Custom mutate function for filtered operations
  const customMutate = async (
    dataOrFn?: ((currentData: Order[] | undefined) => Promise<Order[]>) | Order[],
    options?: {
      dateRange?: { from?: Date; to?: Date }
      quarter?: string
    }
  ) => {
    if (options) {
      // For filtered data, update the all orders cache and prevent essential override
      const result = await mutateAll(async () => {
        const data = await fetchOrders(options)
        return data
      }, { revalidate: false })
      
      // Prevent essential orders from overriding large datasets
      const isLargeDataset = (result?.length || 0) > 100
      setHasLargeFilteredData(isLargeDataset)
      
      return result
    } else {
      // Reset filtered data flag and update both caches
      setHasLargeFilteredData(false)
      const allResult = await mutateAll(dataOrFn)
      const essentialResult = await mutateEssential(dataOrFn)
      return allResult || essentialResult
    }
  }

  return {
    orders,
    isLoading,
    isBackgroundLoading,
    isValidating: isValidatingEssential || isValidatingAll,
    isError: !!error,
    error,
    mutate: customMutate,
    // Additional metadata
    hasCompleteData: !!allOrders,
    essentialCount: essentialOrders?.length || 0,
    totalCount: allOrders?.length || 0
  }
}