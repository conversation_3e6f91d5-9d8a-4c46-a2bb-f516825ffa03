import { useState, useEffect, useMemo, useRef } from 'react'
import type { AuthorizedUser } from '../../../../../services/admin'
import { AdminService } from '../../../../../services/admin'
import { useAuth } from '../../../../../contexts/AuthContext'
import { useToast } from '../../../../../hooks/use-toast'
import { useEditUserFormValidation } from './useEditUserFormValidation'
import type { EditUserFormData } from '../types'

export function useEditUserForm(
  open: boolean, 
  user: AuthorizedUser,
  onSuccess: () => Promise<void>, 
  onClose: () => void
) {
  const { user: currentUser } = useAuth()
  const { toast } = useToast()
  const { validateForm } = useEditUserFormValidation()
  
  const [activeTab, setActiveTab] = useState('basic')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const isMountedRef = useRef(true)

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false
    }
  }, [])
  
  // Initialize form data with user's current values
  const [formData, setFormData] = useState<EditUserFormData>({
    first_name: user.first_name,
    last_name: user.last_name,
    department: user.department || '',
    permissions: [...user.permissions],
    role_template: user.role_template || '',
    notes: user.notes || '',
    is_active: user.is_active
  })

  const [originalData, setOriginalData] = useState<EditUserFormData>({
    first_name: user.first_name,
    last_name: user.last_name,
    department: user.department || '',
    permissions: [...user.permissions],
    role_template: user.role_template || '',
    notes: user.notes || '',
    is_active: user.is_active
  })

  // Update form data when sheet opens, but prevent updates during submission
  useEffect(() => {
    if (open && !isSubmitting) {
      const newData = {
        first_name: user.first_name,
        last_name: user.last_name,
        department: user.department || '',
        permissions: [...user.permissions],
        role_template: user.role_template || '',
        notes: user.notes || '',
        is_active: user.is_active
      }
      setFormData(newData)
      setOriginalData(newData)
      setActiveTab('basic')
    }
  }, [open]) // Only depend on open state to prevent updates during form interaction

  // Check if form has changes (memoized for performance)
  const hasChanges = useMemo(() => {
    return (
      formData.first_name !== originalData.first_name ||
      formData.last_name !== originalData.last_name ||
      formData.department !== originalData.department ||
      formData.role_template !== originalData.role_template ||
      formData.notes !== originalData.notes ||
      formData.is_active !== originalData.is_active ||
      JSON.stringify(formData.permissions.sort()) !== JSON.stringify(originalData.permissions.sort())
    )
  }, [formData, originalData])

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handlePermissionToggle = (permission: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      permissions: checked
        ? [...prev.permissions, permission]
        : prev.permissions.filter(p => p !== permission)
    }))
  }

  const handleReset = () => {
    setFormData({ ...originalData })
  }

  const handleSubmit = async () => {
    if (!formData.first_name || !formData.last_name) {
      toast({
        title: 'Error',
        description: 'First name and last name are required',
        variant: 'destructive'
      })
      return
    }

    try {
      setIsSubmitting(true)
      
      // Prepare update data - only include changed fields
      const updateData: any = {}
      if (formData.first_name !== originalData.first_name) updateData.first_name = formData.first_name
      if (formData.last_name !== originalData.last_name) updateData.last_name = formData.last_name
      if (formData.department !== originalData.department) updateData.department = formData.department
      if (formData.role_template !== originalData.role_template) updateData.role_template = formData.role_template
      if (formData.notes !== originalData.notes) updateData.notes = formData.notes
      if (formData.is_active !== originalData.is_active) updateData.is_active = formData.is_active
      
      // Check if permissions changed
      const permissionsChanged = JSON.stringify(formData.permissions.sort()) !== JSON.stringify(originalData.permissions.sort())
      if (permissionsChanged) updateData.permissions = formData.permissions

      // Only proceed if there are actual changes
      if (Object.keys(updateData).length === 0) {
        toast({
          title: 'No Changes',
          description: 'No changes were made to update',
          variant: 'default'
        })
        onClose()
        return
      }

      await AdminService.updateUser(user.id, updateData)
      
      // Follow the working CreateUser pattern exactly:
      // 1. Refresh data first (blocking operation)
      await onSuccess()
      
      // 2. Show success toast after refresh completes
      toast({
        title: 'User Updated',
        description: `${formData.first_name} ${formData.last_name}'s information has been updated successfully`,
      })
      
      // 3. Close dialog last
      onClose()
    } catch (error) {
      // Only show error if component is still mounted
      if (isMountedRef.current) {
        toast({
          title: 'Error',
          description: error instanceof Error ? error.message : 'Failed to update user',
          variant: 'destructive'
        })
      }
    } finally {
      // Only update state if component is still mounted
      if (isMountedRef.current) {
        setIsSubmitting(false)
      }
    }
  }

  return {
    activeTab,
    setActiveTab,
    formData,
    isSubmitting,
    hasChanges,
    handleInputChange,
    handlePermissionToggle,
    handleSubmit,
    handleReset
  }
}