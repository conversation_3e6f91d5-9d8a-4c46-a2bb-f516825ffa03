# Quick Migration Index

Fast reference for finding specific migration files and understanding the database evolution.

## 🚀 Recently Added (Latest First)

- `permissions/015_modernize_permissions_system_v2.sql` - Permission system modernization (Sep 2025)
- `permissions/014_permission_system_v2_migration.sql` - Permission system v2 migration (Sep 2025)
- `permissions/013_create_user_audit_logs.sql` - User audit logging system (Aug 2025)

## 📋 By Category

### 🏗️ Schema (Database Structure) - Execute FIRST
| File | Purpose | Dependencies |
|------|---------|--------------|
| `001_production_cost_schema.sql` | Foundation schema | None (start here) |
| `002_standardize_attribute_types.sql` | Standardize data types | 001 |
| `003_unified_product_data_architecture.sql` | Product data unification | 001, 002 |
| `004_add_computed_production_costs.sql` | Computed production costs | 001-003 |
| `005_order_production_cost_calculation_functions.sql` | Cost calculation functions | 001-004 |
| `006_enhanced_production_cost_with_calc_rules.sql` | Enhanced cost calculation | 001-005 |
| `007_production_cost_with_breakdown_storage.sql` | Cost breakdown storage | 001-006 |

### 📊 Data (Business Logic) - Execute SECOND
| File | Purpose | Dependencies |
|------|---------|--------------|
| `001_business_rule_enforcement.sql` | Core business rules | Schema complete |
| `002_simplify_calculation_methods.sql` | Simplify calculation logic | 001 |
| `003_cleanup_unused_fields.sql` | Remove unused fields | 001-002 |
| `004_editing_session_management.sql` | Session management | 001-003 |
| `005_add_order_notes_column.sql` | Order notes functionality | Schema complete |
| `006_hard_delete_products_function.sql` | Hard delete functionality | 001-005 |
| `007_system_announcements_calculation_rules.sql` | System announcements | 001-006 |
| `008_harmonize_manual_trigger_with_breakdown.sql` | Harmonize triggers | 001-007 |
| `009_update_item_notes_validation_for_simplified_types.sql` | Item notes validation | 001-008 |
| `010_add_enable_notes_column.sql` | Enable notes column | 001-009 |

### 🔒 Permissions (Security) - Execute THIRD
| File | Purpose | Dependencies |
|------|---------|--------------|
| `001_create_permissions_system.sql` | Initial permission system | Schema + Data |
| `002_seed_permissions_data.sql` | Permission data seeding | 001 |
| `003_add_delete_permissions.sql` | Delete permissions | 001-002 |
| `004_create_authorized_users_table.sql` | Authorized users table | 001-003 |
| `005_create_roles_table.sql` | User roles system | 001-004 |
| `006_secure_rls_basic.sql` | Basic RLS setup | 001-005 |
| `007_comprehensive_rls_granular_policies.sql` | Comprehensive RLS | 001-006 |
| `008_views_rls_security.sql` | RLS for views | 001-007 |
| `009_fix_authorized_users_rls.sql` | Fix authorized users RLS | 001-008 |
| `010_safe_profile_enhancements.sql` | Safe profile enhancements | 001-009 |
| `011_setup_automatic_profile_creation.sql` | Auto profile creation | 001-010 |
| `012_add_auth_user_id_field.sql` | Auth user ID field | 001-011 |
| `013_create_user_audit_logs.sql` | User audit logs | 001-012 |
| `014_permission_system_v2_migration.sql` | Permission v2 migration | 001-013 |
| `015_modernize_permissions_system_v2.sql` | Permission v2 modernization | 001-014 |

### ✨ Features (Enhancements) - Execute FOURTH
| File | Purpose | Dependencies |
|------|---------|--------------|
| `001_add_additional_cost_tier.sql` | Additional cost tier | Core system |
| `002_add_template_value_configuration.sql` | Template system | Core system |
| `003_add_tier_metadata.sql` | Tier metadata | 001-002 |
| `004_add_product_component_support.sql` | Product components | 001-003 |
| `005_analytics_general_overview_data.sql` | Analytics overview | Core system |
| `006_analytics_get_client_performance.sql` | Client performance analytics | 005 |
| `007_client_profit_analytics.sql` | Client profit analytics | 005-006 |
| `008_template_deletion_and_product_deprecation.sql` | Template lifecycle | 002-007 |
| `009_analytics_production_cost_breakdown_functions.sql` | Production cost analytics | 005-008 |

## 🎯 Common Migration Paths

### New Database Setup (Full Installation)
```
Schema Migrations (001-007):
1. schema/001_production_cost_schema.sql
2. schema/002_standardize_attribute_types.sql  
3. schema/003_unified_product_data_architecture.sql
4. schema/004_add_computed_production_costs.sql
5. schema/005_order_production_cost_calculation_functions.sql
6. schema/006_enhanced_production_cost_with_calc_rules.sql
7. schema/007_production_cost_with_breakdown_storage.sql

Data Migrations (001-010):
8. data/001_business_rule_enforcement.sql
9. data/002_simplify_calculation_methods.sql
... (continue through data/010)

Permissions (001-015):
... (continue through all permission migrations)

Features (001-009):
... (optional enhancements)
```

### Permission System V2 Update
```
1. permissions/014_permission_system_v2_migration.sql
2. permissions/015_modernize_permissions_system_v2.sql
3. Run: scripts/utilities/verify-permissions-migration.sql
```

### Analytics Suite Setup
```
1. features/005_analytics_general_overview_data.sql
2. features/006_analytics_get_client_performance.sql
3. features/007_client_profit_analytics.sql
4. features/009_analytics_production_cost_breakdown_functions.sql
```

## 🔍 Quick Reference

### Latest Migration in Each Category
- **Schema**: `007_production_cost_with_breakdown_storage.sql`
- **Data**: `010_add_enable_notes_column.sql`  
- **Permissions**: `015_modernize_permissions_system_v2.sql`
- **Features**: `009_analytics_production_cost_breakdown_functions.sql`

### Critical Migrations (Must Have)
- `schema/001_production_cost_schema.sql` - Database foundation
- `data/001_business_rule_enforcement.sql` - Core business logic
- `permissions/001_create_permissions_system.sql` - Security foundation
- `permissions/014_permission_system_v2_migration.sql` - Modern permissions
- `permissions/015_modernize_permissions_system_v2.sql` - Permission system completion

### By Feature Area
- **Production Costs**: schema/001, 004-007
- **Templates**: features/002, 008
- **Analytics**: features/005-007, 009
- **Permissions**: permissions/001-015 (all)
- **Order Management**: data/005, 009, 010

## ⚠️ Important Notes

### Sequential Execution Required
- **Schema migrations must be executed 001→007 in order**
- **Data migrations must be executed 001→010 in order**  
- **Permission migrations must be executed 001→015 in order**
- **Feature migrations can be selective but maintain 001→009 order**

### Prerequisites
- Always execute **schema** migrations first (001-007)
- Then **data** migrations (001-010)
- Then **permissions** migrations (001-015)
- Finally **features** migrations (001-009)

### Backup Strategy
- **Database backup** before each category
- Individual backups before major migrations (schema/007, permissions/014-015)
- Test in **development environment** first

### Rollback Information
- `data/rollback_003_calculation_methods.sql` available
- Most migrations do not have explicit rollback scripts
- **Database backup** is primary rollback strategy

---

**💡 Pro Tip**: The new numbering system makes it easy to see the latest migration in each category and understand execution order. Always check the highest number in each directory to see what's been applied.