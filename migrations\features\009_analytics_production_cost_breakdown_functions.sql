-- Migration: Analytics Production Cost Breakdown Functions
-- Description: Database functions to consolidate production cost breakdown data for analytics
-- Version: 018
-- Date: 2025-01-09

-- =====================================================
-- 1. Function to Get Complete Production Cost Breakdown for Order Item
-- =====================================================

CREATE OR REPLACE FUNCTION get_order_item_production_cost_breakdown(
  p_order_item_id UUID
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  v_order_item RECORD;
  v_mapping_result RECORD;
  v_templates_breakdown JSONB := '[]'::JSONB;
  v_template_record RECORD;
  v_components_breakdown JSONB;
  v_totals JSONB;
  v_base_cost NUMERIC := 0;
  v_additional_cost NUMERIC := 0;
  v_total_cost NUMERIC := 0;
BEGIN
  -- Step 1: Get order item details
  SELECT 
    item_id,
    product,
    product_type,
    size,
    qty,
    nos,
    production_cost,
    created_at
  INTO v_order_item
  FROM order_items
  WHERE item_id = p_order_item_id;

  IF NOT FOUND THEN
    RETURN jsonb_build_object(
      'error', 'Order item not found',
      'order_item_id', p_order_item_id
    );
  END IF;

  -- Step 2: Map order item attributes to IDs
  SELECT * INTO v_mapping_result
  FROM map_order_item_to_attributes(
    v_order_item.product,
    v_order_item.product_type,
    v_order_item.size
  );

  IF NOT v_mapping_result.mapping_success THEN
    RETURN jsonb_build_object(
      'error', 'Failed to map product attributes',
      'order_item_id', p_order_item_id,
      'product_info', jsonb_build_object(
        'product', v_order_item.product,
        'product_type', v_order_item.product_type,
        'size', v_order_item.size
      )
    );
  END IF;

  -- Step 3: Get applied templates breakdown
  FOR v_template_record IN
    SELECT 
      ta.template_id,
      ta.applied_at,
      ct.name as template_name,
      ct.category as template_category,
      ct.calculation_method,
      ct.selected_components
    FROM template_applications ta
    JOIN calculation_templates ct ON ct.id = ta.template_id
    WHERE ta.category_id = v_mapping_result.category_id
      AND ta.product_type_id = v_mapping_result.product_type_id
      AND ta.size_id = v_mapping_result.size_id
      AND ta.is_active = true
      AND ct.status = 'active'
    ORDER BY ta.applied_at
  LOOP
    -- Get components breakdown for this template
    SELECT get_template_components_breakdown(
      v_mapping_result.category_id,
      v_mapping_result.product_type_id,
      v_mapping_result.size_id,
      v_template_record.selected_components,
      COALESCE(v_order_item.qty, 1),
      v_order_item.nos
    ) INTO v_components_breakdown;

    -- Calculate template subtotal
    DECLARE
      v_template_subtotal NUMERIC := 0;
      v_component JSONB;
    BEGIN
      FOR v_component IN 
        SELECT * FROM jsonb_array_elements(v_components_breakdown->'components')
      LOOP
        v_template_subtotal := v_template_subtotal + (v_component->>'final_value')::NUMERIC;
      END LOOP;
    END;

    -- Add to templates breakdown
    v_templates_breakdown := v_templates_breakdown || jsonb_build_object(
      'template_id', v_template_record.template_id,
      'template_name', v_template_record.template_name,
      'template_category', v_template_record.template_category,
      'calculation_method', v_template_record.calculation_method,
      'components', v_components_breakdown->'components',
      'subtotal', v_template_subtotal
    );

    -- Add to totals
    IF v_template_record.template_category = 'basic_cost' THEN
      v_base_cost := v_base_cost + v_template_subtotal;
    ELSE
      v_additional_cost := v_additional_cost + v_template_subtotal;
    END IF;
  END LOOP;

  v_total_cost := v_base_cost + v_additional_cost;

  -- Build final result
  RETURN jsonb_build_object(
    'order_item_id', p_order_item_id,
    'product_combination', jsonb_build_object(
      'category', v_order_item.product,
      'product_type', v_order_item.product_type,
      'size', v_order_item.size
    ),
    'applied_templates', v_templates_breakdown,
    'totals', jsonb_build_object(
      'base_cost', v_base_cost,
      'additional_cost', v_additional_cost,
      'total_cost', v_total_cost
    ),
    'calculation_context', jsonb_build_object(
      'quantity', COALESCE(v_order_item.qty, 1),
      'nos', v_order_item.nos,
      'calculation_date', NOW()
    ),
    'data_completeness', jsonb_build_object(
      'has_templates', jsonb_array_length(v_templates_breakdown) > 0,
      'coverage_percentage', CASE 
        WHEN jsonb_array_length(v_templates_breakdown) > 0 THEN 100
        ELSE 0
      END
    )
  );
END;
$$;

-- =====================================================
-- 2. Function to Get Components Breakdown for Template
-- =====================================================

CREATE OR REPLACE FUNCTION get_template_components_breakdown(
  p_category_id UUID,
  p_product_type_id UUID,
  p_size_id UUID,
  p_component_codes TEXT[],
  p_quantity INTEGER DEFAULT 1,
  p_nos INTEGER DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  v_components JSONB := '[]'::JSONB;
  v_component_record RECORD;
  v_tier_result JSONB;
BEGIN
  -- Return empty if no component codes provided
  IF p_component_codes IS NULL OR array_length(p_component_codes, 1) = 0 THEN
    RETURN jsonb_build_object('components', v_components);
  END IF;

  -- Get component values for the specified codes
  FOR v_component_record IN
    SELECT 
      pcv.value,
      pcv.tier_metadata,
      pcc.code,
      pcc.name,
      pcc.category
    FROM production_cost_component_values pcv
    JOIN production_cost_components pcc ON pcc.id = pcv.component_id
    WHERE pcv.product_category_id = p_category_id
      AND pcv.product_type_id = p_product_type_id
      AND pcv.size_id = p_size_id
      AND pcv.is_current = true
      AND pcc.status = 'active'
      AND pcc.code = ANY(p_component_codes)
  LOOP
    -- Calculate tier transformation and final value
    SELECT calculate_tier_transformation(
      v_component_record.value,
      v_component_record.tier_metadata,
      p_quantity,
      p_nos
    ) INTO v_tier_result;

    -- Add component to breakdown
    v_components := v_components || jsonb_build_object(
      'component_code', v_component_record.code,
      'component_name', v_component_record.name,
      'component_category', v_component_record.category,
      'base_value', v_component_record.value,
      'tier_type', COALESCE(v_component_record.tier_metadata->>'tier', 'none'),
      'transformation', v_tier_result->>'transformation',
      'final_value', (v_tier_result->>'final_value')::NUMERIC,
      'template_source', 'production_cost_system'
    );
  END LOOP;

  RETURN jsonb_build_object('components', v_components);
END;
$$;

-- =====================================================
-- 3. Function to Calculate Tier Transformation
-- =====================================================

CREATE OR REPLACE FUNCTION calculate_tier_transformation(
  p_base_value NUMERIC,
  p_tier_metadata JSONB,
  p_quantity INTEGER,
  p_nos INTEGER DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  v_tier_type TEXT;
  v_transformation TEXT;
  v_final_value NUMERIC;
BEGIN
  -- Default for basic costs (no tier metadata)
  IF p_tier_metadata IS NULL OR NOT (p_tier_metadata ? 'tier') THEN
    RETURN jsonb_build_object(
      'transformation', 'multiply_qty',
      'final_value', p_base_value * p_quantity
    );
  END IF;

  v_tier_type := p_tier_metadata->>'tier';

  CASE v_tier_type
    WHEN 'per_unit' THEN
      v_transformation := 'multiply_qty';
      v_final_value := p_base_value * p_quantity;
    WHEN 'per_order' THEN
      v_transformation := 'add_to_final';
      v_final_value := p_base_value;
    WHEN 'distributed_by_nos' THEN
      v_transformation := 'divide_nos';
      v_final_value := CASE 
        WHEN p_nos IS NOT NULL AND p_nos > 0 THEN p_base_value
        ELSE p_base_value
      END;
    WHEN 'distributed_by_qty' THEN
      v_transformation := 'divide_qty';
      v_final_value := CASE 
        WHEN p_quantity > 0 THEN p_base_value
        ELSE p_base_value
      END;
    ELSE
      v_transformation := 'none';
      v_final_value := p_base_value;
  END CASE;

  RETURN jsonb_build_object(
    'transformation', v_transformation,
    'final_value', v_final_value
  );
END;
$$;

-- =====================================================
-- 4. Function to Get Batch Production Cost Breakdowns
-- =====================================================

CREATE OR REPLACE FUNCTION get_batch_production_cost_breakdowns(
  p_order_item_ids UUID[]
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  v_results JSONB := '[]'::JSONB;
  v_item_id UUID;
  v_breakdown JSONB;
BEGIN
  -- Process each order item
  FOREACH v_item_id IN ARRAY p_order_item_ids
  LOOP
    SELECT get_order_item_production_cost_breakdown(v_item_id) INTO v_breakdown;
    
    -- Add to results if successful
    IF v_breakdown IS NOT NULL AND NOT (v_breakdown ? 'error') THEN
      v_results := v_results || v_breakdown;
    END IF;
  END LOOP;

  RETURN jsonb_build_object(
    'breakdowns', v_results,
    'processed_count', array_length(p_order_item_ids, 1),
    'successful_count', jsonb_array_length(v_results)
  );
END;
$$;

-- =====================================================
-- 5. Helper Function: Get Breakdown Summary Stats
-- =====================================================

CREATE OR REPLACE FUNCTION get_production_cost_breakdown_stats()
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  v_total_order_items INTEGER;
  v_items_with_production_cost INTEGER;
  v_items_with_templates INTEGER;
  v_coverage_stats JSONB;
BEGIN
  -- Get total order items
  SELECT COUNT(*) INTO v_total_order_items
  FROM order_items
  WHERE product IS NOT NULL AND product != '';

  -- Get items with production cost
  SELECT COUNT(*) INTO v_items_with_production_cost
  FROM order_items
  WHERE product IS NOT NULL 
    AND product != ''
    AND production_cost > 0;

  -- Get items that could have template-based breakdown
  -- (This is an estimate based on product combinations that have templates)
  SELECT COUNT(DISTINCT oi.item_id) INTO v_items_with_templates
  FROM order_items oi
  WHERE EXISTS (
    SELECT 1 FROM map_order_item_to_attributes(oi.product, oi.product_type, oi.size) m
    WHERE m.mapping_success = true
      AND EXISTS (
        SELECT 1 FROM template_applications ta
        WHERE ta.category_id = m.category_id
          AND ta.product_type_id = m.product_type_id
          AND ta.size_id = m.size_id
          AND ta.is_active = true
      )
  );

  RETURN jsonb_build_object(
    'total_order_items', v_total_order_items,
    'items_with_production_cost', v_items_with_production_cost,
    'items_with_templates', v_items_with_templates,
    'production_cost_coverage_percentage', CASE 
      WHEN v_total_order_items > 0 THEN 
        ROUND((v_items_with_production_cost::NUMERIC / v_total_order_items) * 100, 2)
      ELSE 0
    END,
    'template_coverage_percentage', CASE 
      WHEN v_total_order_items > 0 THEN 
        ROUND((v_items_with_templates::NUMERIC / v_total_order_items) * 100, 2)
      ELSE 0
    END
  );
END;
$$;

-- Add helpful comments
COMMENT ON FUNCTION get_order_item_production_cost_breakdown IS 'Gets complete production cost breakdown for an order item including applied templates and component details';
COMMENT ON FUNCTION get_template_components_breakdown IS 'Gets component breakdown for specific template component codes with tier calculations';
COMMENT ON FUNCTION calculate_tier_transformation IS 'Calculates tier-based transformation and final value for cost components';
COMMENT ON FUNCTION get_batch_production_cost_breakdowns IS 'Gets production cost breakdowns for multiple order items in batch';
COMMENT ON FUNCTION get_production_cost_breakdown_stats IS 'Returns statistics about production cost breakdown data coverage';