import { useCallback, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import { useNavigationLoading } from './NavigationLoadingProvider'
import {
  Settings,
  LogOut,
  User,
  Palette,
  Shield,
  Crown,
  Edit3
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu'
import { ThemeToggle } from '../ui/theme-toggle'

interface ProfileDropdownProps {
  children: React.ReactNode
}

export function ProfileDropdown({ children }: ProfileDropdownProps) {
  const navigate = useNavigate()
  const { user, profile, authorizedUser, signOut } = useAuth()
  const { startNavigationTransition } = useNavigationLoading()
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false)

  // Get role-specific styling and icon
  const getRoleInfo = useCallback(() => {
    const role = (profile?.role || authorizedUser?.role || '').toLowerCase()
    
    switch (role) {
      case 'admin':
      case 'administrator':
        return {
          style: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
          icon: Shield,
          label: profile?.role || authorizedUser?.role || 'Admin'
        }
      case 'manager':
      case 'supervisor':
        return {
          style: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
          icon: Crown,
          label: profile?.role || authorizedUser?.role || 'Manager'
        }
      case 'editor':
      case 'moderator':
        return {
          style: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
          icon: Edit3,
          label: profile?.role || authorizedUser?.role || 'Editor'
        }
      case 'user':
      default:
        return {
          style: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
          icon: User,
          label: profile?.role || authorizedUser?.role || 'User'
        }
    }
  }, [profile?.role, authorizedUser?.role])

  // Profile menu handlers
  const handleSettingsClick = useCallback(() => {
    setIsProfileMenuOpen(false)
    startNavigationTransition(() => {
      navigate('/settings')
    })
  }, [navigate, startNavigationTransition])

  const handleSignoutClick = useCallback(async () => {
    try {
      await signOut()
      navigate('/login', { replace: true })
    } catch (error) {
      console.error('Signout error:', error)
    }
  }, [signOut, navigate])

  return (
    <DropdownMenu open={isProfileMenuOpen} onOpenChange={setIsProfileMenuOpen}>
      <DropdownMenuTrigger asChild>
        {children}
      </DropdownMenuTrigger>
      <DropdownMenuContent 
        side="right" 
        align="end" 
        className="w-56"
        sideOffset={8}
      >
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none mb-0">
              {profile?.full_name || 'User'}
            </p>
            <p className="text-xs leading-none text-muted-foreground mb-0">
              {user?.email}
            </p>
            {(profile?.role || authorizedUser?.role || profile?.department || authorizedUser?.department) && (
              <div className="flex items-center gap-1 mt-1">
                {(() => {
                  const roleInfo = getRoleInfo();
                  const RoleIcon = roleInfo.icon;
                  return (
                    <span className={`inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-medium ${roleInfo.style}`}>
                      <RoleIcon className="h-3 w-3" />
                      {roleInfo.label}
                    </span>
                  );
                })()}
                {(profile?.department || authorizedUser?.department) && (
                  <span className="text-xs text-muted-foreground">
                    • {profile?.department || authorizedUser?.department}
                  </span>
                )}
              </div>
            )}
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem onClick={handleSettingsClick} className="cursor-pointer">
            <Settings className="mr-2 h-4 w-4" />
            <span>Settings & Profile</span>
          </DropdownMenuItem>
          <DropdownMenuItem 
            className="cursor-pointer"
            onClick={(e) => e.preventDefault()}
          >
            <Palette className="mr-2 h-4 w-4" />
            <span>Theme</span>
            <div className="ml-auto" onClick={(e) => e.stopPropagation()}>
              <ThemeToggle compact />
            </div>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem 
          onClick={handleSignoutClick}
          className="cursor-pointer text-red-600 focus:text-red-600 focus:bg-red-50"
        >
          <LogOut className="mr-2 h-4 w-4" />
          <span>Sign out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}