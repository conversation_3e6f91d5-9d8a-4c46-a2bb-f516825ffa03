-- Fix client cache triggers to use case-insensitive matching
-- This addresses the issue where "Bantu photography" (lowercase 'p') in orders
-- doesn't match "Bantu Photography" (uppercase 'P') in clients table

-- Drop and recreate the update_client_order_cache function with case-insensitive matching
DROP FUNCTION IF EXISTS update_client_order_cache(TEXT);

CREATE OR REPLACE FUNCTION update_client_order_cache(client_name_param TEXT)
RETURNS VOID AS $$
DECLARE
    order_data RECORD;
    affected_rows INTEGER;
BEGIN
    -- Skip if client_name is null or empty
    IF client_name_param IS NULL OR TRIM(client_name_param) = '' THEN
        RETURN;
    END IF;
    
    -- Normalize client name
    client_name_param := TRIM(client_name_param);
    
    -- Aggregate all order data for this client using CASE-INSENSITIVE matching
    SELECT 
        ARRAY_AGG(order_id ORDER BY order_date DESC) as order_ids,
        COUNT(*) as total_orders,
        MIN(order_date) as first_date,
        MAX(order_date) as latest_date,
        COALESCE(SUM(total_amount), 0) as total_value
    INTO order_data
    FROM orders 
    WHERE LOWER(client_name) = LOWER(client_name_param);  -- FIXED: Case-insensitive matching
    
    -- Update the client record if it exists using CASE-INSENSITIVE matching
    UPDATE clients SET
        order_ids_cache = to_jsonb(COALESCE(order_data.order_ids, ARRAY[]::UUID[])),
        first_order_date = order_data.first_date,
        latest_order_date = order_data.latest_date,
        total_lifetime_orders = COALESCE(order_data.total_orders, 0),
        lifetime_value = order_data.total_value,
        cache_last_updated = NOW()
    WHERE LOWER(name) = LOWER(client_name_param);  -- FIXED: Case-insensitive matching
    
    -- Get number of rows affected
    GET DIAGNOSTICS affected_rows = ROW_COUNT;
    
    -- Log cache update for monitoring
    RAISE NOTICE 'Updated cache for client "%" - % orders, % total value, % rows affected', 
        client_name_param, 
        COALESCE(order_data.total_orders, 0), 
        order_data.total_value,
        affected_rows;
        
EXCEPTION
    WHEN OTHERS THEN
        -- Log error but don't fail the transaction
        RAISE WARNING 'Failed to update cache for client "%": %', client_name_param, SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- Test the function with a known case-sensitive mismatch
-- This should now properly match "Bantu photography" orders with "Bantu Photography" client
SELECT update_client_order_cache('Bantu photography');
SELECT update_client_order_cache('Bantu Photography');

-- Also update any other clients that might have case mismatches
-- Run cache update for all unique client names from orders
DO $$
DECLARE
    client_name_rec RECORD;
BEGIN
    FOR client_name_rec IN 
        SELECT DISTINCT client_name 
        FROM orders 
        WHERE client_name IS NOT NULL 
          AND TRIM(client_name) != ''
    LOOP
        PERFORM update_client_order_cache(client_name_rec.client_name);
    END LOOP;
END $$;

-- Verify the cache has been populated
SELECT 
    name,
    array_length(order_ids_cache, 1) as cached_orders_count,
    total_lifetime_orders,
    lifetime_value,
    cache_last_updated
FROM clients 
WHERE name ILIKE '%bantu%'
ORDER BY name;

-- Log the fix
RAISE NOTICE 'Client cache function updated to use case-insensitive matching. All client caches have been refreshed.';