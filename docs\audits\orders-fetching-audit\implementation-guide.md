# Orders Fetching Mechanism: Implementation Guide

**Document Type:** Step-by-Step Implementation Tasks  
**Audience:** Development Team, Project Managers  
**Priority:** Production Blocker Resolution  
**Estimated Timeline:** 4-6 weeks  

---

## 🎯 Overview

This guide provides actionable tasks to address critical issues identified in the orders fetching mechanism audit. Tasks are prioritized by severity and impact, with detailed implementation steps and acceptance criteria.

### Critical Issues Summary
1. **File Size Violations:** 2 files exceed limits (86-162% over)
2. **Memory Scaling Problems:** Linear growth, 300% above industry standard
3. **Performance Bottlenecks:** Queries take 500ms-2s for large datasets
4. **Complex State Management:** Over-engineered progressive loading

---

## 📅 Implementation Roadmap

### Phase 1: Critical Issues (Weeks 1-2) 🔴
**Goal:** Resolve production blockers  
**Impact:** Prevents application crashes, enables production deployment

### Phase 2: Performance Optimization (Weeks 3-4) 🟡  
**Goal:** Improve performance and scalability  
**Impact:** Better user experience, handles larger datasets

### Phase 3: Architecture Enhancement (Weeks 5-6) 🟢
**Goal:** Long-term maintainability improvements  
**Impact:** Easier maintenance, better developer experience

---

## Phase 1: Critical Issues Resolution

### Task 1.1: Refactor useOrdersSWR.ts (Priority: 🔴 CRITICAL)

**Current Problem:** 557 lines in single file (86% over limit)  
**Target:** Split into 4 focused modules (<150 lines each)

#### Step 1.1.1: Create New File Structure
```bash
# Create new directory structure
mkdir -p src/hooks/orders
mkdir -p src/hooks/orders/types
```

#### Step 1.1.2: Extract Core Data Fetching Hook
**File:** `src/hooks/orders/useOrdersCore.ts`

```typescript
import { useSWR } from 'swr';
import { fetchOrders } from '../../services/orders';
import type { Order, OrderFetchOptions } from './types';

export const useOrdersCore = (options?: OrderFetchOptions) => {
  const { data, error, isLoading, mutate } = useSWR(
    ['orders', options],
    () => fetchOrders(options),
    {
      revalidateOnFocus: true,
      revalidateOnReconnect: true,
      dedupingInterval: 5000,
      keepPreviousData: true
    }
  );

  return {
    orders: data?.orders || [],
    totalCount: data?.totalCount || 0,
    error,
    isLoading,
    mutate
  };
};
```

**Acceptance Criteria:**
- [ ] File under 150 lines
- [ ] Single responsibility: core data fetching only
- [ ] All existing functionality preserved
- [ ] Unit tests covering all branches
- [ ] TypeScript strict mode compliant

#### Step 1.1.3: Extract Progressive Loading Logic
**File:** `src/hooks/orders/useOrdersProgressive.ts`

```typescript
import { useState, useCallback } from 'react';
import { useOrdersCore } from './useOrdersCore';

export const useOrdersProgressive = () => {
  const [page, setPage] = useState(1);
  const [allOrders, setAllOrders] = useState<Order[]>([]);
  
  const { orders: pageOrders, isLoading, error } = useOrdersCore({
    page,
    limit: 50
  });
  
  const loadMore = useCallback(() => {
    setPage(prev => prev + 1);
    setAllOrders(prev => [...prev, ...pageOrders]);
  }, [pageOrders]);
  
  const reset = useCallback(() => {
    setPage(1);
    setAllOrders([]);
  }, []);
  
  return {
    orders: allOrders,
    isLoading,
    error,
    loadMore,
    reset,
    hasMore: pageOrders.length === 50 // Assuming full page means more data
  };
};
```

**Acceptance Criteria:**
- [ ] Simplified state management (max 3 state variables)
- [ ] Clear loading states
- [ ] Memory efficient (no large dataset caching)
- [ ] Predictable state transitions
- [ ] Performance tests pass

#### Step 1.1.4: Extract Optimistic Updates Logic  
**File:** `src/hooks/orders/useOrdersOptimistic.ts`

```typescript
import { useCallback } from 'react';
import { mutate } from 'swr';
import { addOrderSWR, updateOrderSWR, deleteOrderSWR } from '../../services/orders';
import type { Order } from './types';

export const useOrdersOptimistic = () => {
  const addOrder = useCallback(async (orderData: Omit<Order, 'id'>) => {
    // Optimistic update
    await mutate(
      ['orders'],
      (current: { orders: Order[] } = { orders: [] }) => ({
        ...current,
        orders: [
          { ...orderData, id: `temp-${Date.now()}` },
          ...current.orders
        ]
      }),
      { revalidate: false }
    );
    
    try {
      // Actual API call
      const newOrder = await addOrderSWR(orderData);
      
      // Update with real data
      await mutate(['orders']);
      
      return newOrder;
    } catch (error) {
      // Revert optimistic update
      await mutate(['orders']);
      throw error;
    }
  }, []);
  
  const updateOrder = useCallback(async (id: string, updates: Partial<Order>) => {
    // Similar optimistic update pattern
    // Implementation details...
  }, []);
  
  const deleteOrder = useCallback(async (id: string) => {
    // Similar optimistic update pattern
    // Implementation details...
  }, []);
  
  return {
    addOrder,
    updateOrder,
    deleteOrder
  };
};
```

**Acceptance Criteria:**
- [ ] Clean optimistic update patterns
- [ ] Proper error rollback
- [ ] Type-safe implementations
- [ ] Integration tests covering success/failure scenarios

#### Step 1.1.5: Create Main Orders Hook
**File:** `src/hooks/orders/useOrders.ts`

```typescript
import { useOrdersCore } from './useOrdersCore';
import { useOrdersProgressive } from './useOrdersProgressive';
import { useOrdersOptimistic } from './useOrdersOptimistic';
import { useOrdersFilters } from './useOrdersFilters';

export const useOrders = (options?: OrderFetchOptions) => {
  const core = useOrdersCore(options);
  const progressive = useOrdersProgressive();
  const optimistic = useOrdersOptimistic();
  const filters = useOrdersFilters(core.orders);
  
  return {
    // Core data
    orders: core.orders,
    isLoading: core.isLoading,
    error: core.error,
    
    // Progressive loading
    loadMore: progressive.loadMore,
    hasMore: progressive.hasMore,
    
    // Optimistic updates
    addOrder: optimistic.addOrder,
    updateOrder: optimistic.updateOrder,
    deleteOrder: optimistic.deleteOrder,
    
    // Filters
    filteredOrders: filters.filteredOrders,
    setFilters: filters.setFilters,
    
    // Actions
    refresh: core.mutate
  };
};
```

**Acceptance Criteria:**
- [ ] Clean composition of smaller hooks
- [ ] Backwards compatible API
- [ ] Clear separation of concerns
- [ ] Full test coverage

#### Step 1.1.6: Update Import Statements
Update all files importing from the old `useOrdersSWR.ts`:

```typescript
// ❌ Old import
import { useOrdersSWR } from '../hooks/useOrdersSWR';

// ✅ New import  
import { useOrders } from '../hooks/orders/useOrders';
```

**Implementation Checklist:**
- [ ] Update all component imports
- [ ] Update all test imports
- [ ] Verify no broken imports remain
- [ ] Run full test suite
- [ ] Verify application works end-to-end

### Task 1.2: Refactor order.service.ts (Priority: 🔴 CRITICAL)

**Current Problem:** 785 lines in single file (162% over limit)  
**Target:** Split into 6 focused service modules (<150 lines each)

#### Step 1.2.1: Create Service Directory Structure
```bash
mkdir -p src/services/orders
mkdir -p src/services/orders/types
mkdir -p src/services/orders/utils
```

#### Step 1.2.2: Extract Database Queries Module
**File:** `src/services/orders/orderQueries.ts`

```typescript
import { supabase } from '../../lib/supabase';
import type { OrderFetchOptions, DbOrder } from './types';

export const fetchOrdersPage = async (options: OrderFetchOptions = {}) => {
  const {
    page = 1,
    limit = 50,
    dateRange,
    filters = {}
  } = options;
  
  const offset = (page - 1) * limit;
  
  let query = supabase
    .from('orders')
    .select(`
      order_id,
      order_no,
      client_name,
      agent,
      sector,
      order_date,
      status,
      delivery_date,
      quarter,
      created_at,
      total_amount,
      cash_paid,
      balance,
      payment_status,
      delivery_by,
      order_notes
    `)
    .order('order_date', { ascending: false })
    .range(offset, offset + limit - 1);
    
  // Apply date range filter
  if (dateRange?.from) {
    query = query.gte('order_date', formatDate(dateRange.from));
  }
  if (dateRange?.to) {
    query = query.lte('order_date', formatDate(dateRange.to));
  }
  
  // Apply other filters
  if (filters.status) {
    query = query.eq('status', filters.status);
  }
  if (filters.sector) {
    query = query.eq('sector', filters.sector);
  }
  
  const { data, error, count } = await query;
  
  if (error) throw error;
  
  return {
    orders: data || [],
    totalCount: count || 0,
    hasMore: (count || 0) > offset + limit,
    page,
    limit
  };
};

export const fetchOrderById = async (orderId: string): Promise<DbOrder> => {
  const { data, error } = await supabase
    .from('orders')
    .select('*')
    .eq('order_id', orderId)
    .single();
    
  if (error) throw error;
  return data;
};

export const fetchOrderItems = async (orderId: string) => {
  const { data, error } = await supabase
    .from('order_items')
    .select('*')
    .eq('order_id', orderId)
    .order('created_at');
    
  if (error) throw error;
  return data || [];
};

export const fetchOrderPayments = async (orderId: string) => {
  const { data, error } = await supabase
    .from('order_payments')
    .select('*')
    .eq('order_id', orderId)
    .order('payment_sequence');
    
  if (error) throw error;
  return data || [];
};

const formatDate = (date: Date): string => {
  return date.toISOString().split('T')[0];
};
```

**Acceptance Criteria:**
- [ ] Under 150 lines
- [ ] Single responsibility: database queries only
- [ ] Proper error handling
- [ ] TypeScript strict compliance
- [ ] Unit tests for all query functions

#### Step 1.2.3: Extract Data Transformation Module
**File:** `src/services/orders/orderTransforms.ts`

```typescript
import type { DbOrder, Order } from './types';
import { mapStatusToOrderStatus, determinePaymentStatus } from '../utils/statusUtils';

export const transformDbOrderToOrder = (dbOrder: DbOrder): Order => {
  try {
    const totalAmount = dbOrder.total_amount || 0;
    const totalPaid = dbOrder.cash_paid || 0;
    const balance = dbOrder.balance || (totalAmount - totalPaid);
    
    return {
      id: dbOrder.order_id,
      orderNo: dbOrder.order_no,
      customer: dbOrder.client_name || '',
      orderDate: dbOrder.order_date || '',
      deliveryDate: dbOrder.delivery_date || '',
      amount: totalAmount,
      amountPaid: totalPaid,
      balance: balance,
      status: mapStatusToOrderStatus(dbOrder.status),
      paymentStatus: determinePaymentStatus(dbOrder.payment_status, totalPaid, totalAmount),
      deliveryBy: dbOrder.delivery_by,
      sector: dbOrder.sector,
      agent: dbOrder.agent,
      notes: transformOrderNotes(dbOrder.order_notes),
      items: [], // Loaded separately
      payments: [] // Loaded separately
    };
  } catch (error) {
    console.error(`Error transforming order ${dbOrder.order_id}:`, error);
    throw new Error(`Failed to transform order data: ${error.message}`);
  }
};

export const transformDbOrdersToOrders = (dbOrders: DbOrder[]): Order[] => {
  return dbOrders
    .map((dbOrder) => {
      try {
        return transformDbOrderToOrder(dbOrder);
      } catch (error) {
        console.error(`Skipping malformed order ${dbOrder.order_id}:`, error);
        return null;
      }
    })
    .filter(Boolean) as Order[];
};

const transformOrderNotes = (orderNotes: any): Note[] => {
  if (!orderNotes || !Array.isArray(orderNotes)) return [];
  
  return orderNotes.map((note, index) => ({
    id: `note-${index}`,
    content: note.content || '',
    createdAt: note.created_at || new Date().toISOString(),
    createdBy: note.created_by || null
  }));
};
```

**Acceptance Criteria:**
- [ ] Under 100 lines
- [ ] Robust error handling for malformed data
- [ ] Comprehensive data validation
- [ ] Unit tests covering edge cases

#### Step 1.2.4: Extract CRUD Operations Module
**File:** `src/services/orders/orderOperations.ts`

```typescript
import { supabase } from '../../lib/supabase';
import type { Order, CreateOrderData } from './types';
import { transformDbOrderToOrder } from './orderTransforms';

export const createOrder = async (orderData: CreateOrderData): Promise<Order> => {
  try {
    // Prepare order data
    const dbOrderData = {
      client_name: orderData.customer,
      order_date: orderData.orderDate || new Date().toISOString().split('T')[0],
      delivery_date: orderData.deliveryDate || null,
      delivery_by: orderData.deliveryBy || null,
      sector: orderData.sector || 'b2b',
      agent: orderData.agent,
      status: orderData.status,
      quarter: calculateQuarter(new Date(orderData.orderDate || Date.now())),
      order_notes: orderData.notes || []
    };
    
    // Insert order
    const { data: newOrder, error: orderError } = await supabase
      .from('orders')
      .insert(dbOrderData)
      .select()
      .single();
      
    if (orderError) throw orderError;
    
    // Insert order items
    if (orderData.items?.length > 0) {
      const itemsData = orderData.items.map(item => ({
        order_id: newOrder.order_id,
        product: item.productCategory || item.product,
        product_type: item.product,
        size: item.size,
        qty: item.quantity,
        cost_price: item.costPrice,
        original_amount: item.quantity * (item.costPrice || 0),
        discount_amount: item.totalCost || (item.quantity * (item.costPrice || 0))
      }));
      
      const { error: itemsError } = await supabase
        .from('order_items')
        .insert(itemsData);
        
      if (itemsError) throw itemsError;
    }
    
    // Insert payments if provided
    if (orderData.payments?.length > 0) {
      const paymentsData = orderData.payments.map((payment, index) => ({
        order_id: newOrder.order_id,
        payment_amount: payment.amount,
        payment_date: payment.date || new Date().toISOString().split('T')[0],
        payment_method: payment.method || 'Cash',
        payment_sequence: index + 1
      }));
      
      const { error: paymentsError } = await supabase
        .from('order_payments')
        .insert(paymentsData);
        
      if (paymentsError) throw paymentsError;
    }
    
    // Fetch complete order with relations
    const completeOrder = await fetchCompleteOrder(newOrder.order_id);
    return transformDbOrderToOrder(completeOrder);
    
  } catch (error) {
    console.error('Error creating order:', error);
    throw error;
  }
};

export const updateOrder = async (orderId: string, updates: Partial<Order>): Promise<Order> => {
  try {
    // Update order
    const { data: updatedOrder, error: updateError } = await supabase
      .from('orders')
      .update({
        client_name: updates.customer,
        order_date: updates.orderDate,
        delivery_date: updates.deliveryDate,
        status: updates.status,
        // ... other fields
      })
      .eq('order_id', orderId)
      .select()
      .single();
      
    if (updateError) throw updateError;
    
    // Fetch complete order
    const completeOrder = await fetchCompleteOrder(orderId);
    return transformDbOrderToOrder(completeOrder);
    
  } catch (error) {
    console.error('Error updating order:', error);
    throw error;
  }
};

export const deleteOrder = async (orderId: string): Promise<boolean> => {
  try {
    // Delete order items first
    await supabase
      .from('order_items')
      .delete()
      .eq('order_id', orderId);
      
    // Delete payments
    await supabase
      .from('order_payments')
      .delete()
      .eq('order_id', orderId);
      
    // Delete order
    const { error } = await supabase
      .from('orders')
      .delete()
      .eq('order_id', orderId);
      
    if (error) throw error;
    
    return true;
  } catch (error) {
    console.error('Error deleting order:', error);
    throw error;
  }
};

const calculateQuarter = (date: Date): string => {
  const quarter = Math.ceil((date.getMonth() + 1) / 3);
  return `Q${quarter} ${date.getFullYear()}`;
};

const fetchCompleteOrder = async (orderId: string) => {
  const { data, error } = await supabase
    .from('orders')
    .select(`
      *,
      items:order_items(*),
      payments:order_payments(*)
    `)
    .eq('order_id', orderId)
    .single();
    
  if (error) throw error;
  return data;
};
```

**Acceptance Criteria:**
- [ ] Under 150 lines
- [ ] Complete CRUD operations
- [ ] Proper transaction handling
- [ ] Error recovery mechanisms
- [ ] Integration tests covering all operations

#### Step 1.2.5: Create Main Orders Service
**File:** `src/services/orders/index.ts`

```typescript
// Barrel exports for clean imports
export * from './orderQueries';
export * from './orderTransforms';
export * from './orderOperations';
export * from './types';

// Main service interface
export class OrdersService {
  static async fetchOrders(options?: OrderFetchOptions) {
    const result = await fetchOrdersPage(options);
    return {
      ...result,
      orders: transformDbOrdersToOrders(result.orders)
    };
  }
  
  static async getOrder(orderId: string) {
    const dbOrder = await fetchOrderById(orderId);
    const items = await fetchOrderItems(orderId);
    const payments = await fetchOrderPayments(orderId);
    
    return transformDbOrderToOrder({
      ...dbOrder,
      items,
      payments
    });
  }
  
  static async createOrder(data: CreateOrderData) {
    return createOrder(data);
  }
  
  static async updateOrder(id: string, updates: Partial<Order>) {
    return updateOrder(id, updates);
  }
  
  static async deleteOrder(id: string) {
    return deleteOrder(id);
  }
}
```

**Acceptance Criteria:**
- [ ] Clean service interface
- [ ] Proper abstraction layer
- [ ] Easy to mock for testing
- [ ] Backwards compatible API

### Task 1.3: Memory Management Implementation (Priority: 🔴 CRITICAL)

**Current Problem:** Memory grows linearly (75MB for 5K orders)  
**Target:** Implement efficient caching with limits (15MB for 5K orders)

#### Step 1.3.1: Implement LRU Cache
**File:** `src/utils/cache/LRUCache.ts`

```typescript
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  accessCount: number;
}

export class LRUCache<T> {
  private cache = new Map<string, CacheEntry<T>>();
  private accessOrder = new Set<string>();
  private readonly maxSize: number;
  private readonly ttl: number;
  
  constructor(maxSize: number = 100, ttlMinutes: number = 5) {
    this.maxSize = maxSize;
    this.ttl = ttlMinutes * 60 * 1000;
  }
  
  set(key: string, data: T): void {
    // Remove expired entries first
    this.cleanup();
    
    // Evict oldest if at capacity
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      const oldestKey = this.accessOrder.values().next().value;
      if (oldestKey) {
        this.delete(oldestKey);
      }
    }
    
    // Update access order
    if (this.accessOrder.has(key)) {
      this.accessOrder.delete(key);
    }
    this.accessOrder.add(key);
    
    // Set cache entry
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      accessCount: 1
    });
  }
  
  get(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) return null;
    
    // Check TTL
    if (Date.now() - entry.timestamp > this.ttl) {
      this.delete(key);
      return null;
    }
    
    // Update access order and count
    this.accessOrder.delete(key);
    this.accessOrder.add(key);
    entry.accessCount++;
    
    return entry.data;
  }
  
  delete(key: string): boolean {
    this.accessOrder.delete(key);
    return this.cache.delete(key);
  }
  
  clear(): void {
    this.cache.clear();
    this.accessOrder.clear();
  }
  
  size(): number {
    return this.cache.size;
  }
  
  getStats() {
    let totalMemory = 0;
    let hitCount = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      totalMemory += this.estimateMemoryUsage(entry.data);
      hitCount += entry.accessCount;
    }
    
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      estimatedMemoryMB: Math.round(totalMemory / (1024 * 1024) * 100) / 100,
      totalHits: hitCount,
      oldestEntry: Math.min(...Array.from(this.cache.values()).map(e => e.timestamp)),
      newestEntry: Math.max(...Array.from(this.cache.values()).map(e => e.timestamp))
    };
  }
  
  private cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];
    
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.ttl) {
        expiredKeys.push(key);
      }
    }
    
    expiredKeys.forEach(key => this.delete(key));
  }
  
  private estimateMemoryUsage(data: any): number {
    // Rough estimation of memory usage
    try {
      return new Blob([JSON.stringify(data)]).size;
    } catch {
      return 1000; // Default estimate
    }
  }
}
```

**Acceptance Criteria:**
- [ ] LRU eviction works correctly
- [ ] TTL expiration implemented
- [ ] Memory usage estimation accurate within 20%
- [ ] Performance benchmarks meet targets
- [ ] Unit tests cover all edge cases

#### Step 1.3.2: Implement Orders Cache Manager
**File:** `src/services/cache/OrdersCacheManager.ts`

```typescript
import { LRUCache } from '../../utils/cache/LRUCache';
import type { Order, OrdersPageResult } from '../orders/types';

class OrdersCacheManager {
  private pagesCache = new LRUCache<OrdersPageResult>(50, 10); // 50 pages, 10min TTL
  private ordersCache = new LRUCache<Order>(200, 15); // 200 orders, 15min TTL
  private metricsCache = new LRUCache<any>(10, 30); // Metrics cache, 30min TTL
  
  // Page-level caching
  setOrdersPage(key: string, data: OrdersPageResult): void {
    this.pagesCache.set(key, data);
    
    // Also cache individual orders
    data.orders.forEach(order => {
      this.ordersCache.set(`order-${order.id}`, order);
    });
  }
  
  getOrdersPage(key: string): OrdersPageResult | null {
    return this.pagesCache.get(key);
  }
  
  // Individual order caching
  setOrder(orderId: string, order: Order): void {
    this.ordersCache.set(`order-${orderId}`, order);
  }
  
  getOrder(orderId: string): Order | null {
    return this.ordersCache.get(`order-${orderId}`);
  }
  
  // Cache invalidation
  invalidateOrder(orderId: string): void {
    this.ordersCache.delete(`order-${orderId}`);
    
    // Also invalidate pages that might contain this order
    this.invalidatePages();
  }
  
  invalidatePages(): void {
    this.pagesCache.clear();
  }
  
  invalidateAll(): void {
    this.pagesCache.clear();
    this.ordersCache.clear();
    this.metricsCache.clear();
  }
  
  // Memory management
  getMemoryStats() {
    return {
      pages: this.pagesCache.getStats(),
      orders: this.ordersCache.getStats(),
      metrics: this.metricsCache.getStats(),
      totalEstimatedMemoryMB: 
        this.pagesCache.getStats().estimatedMemoryMB +
        this.ordersCache.getStats().estimatedMemoryMB +
        this.metricsCache.getStats().estimatedMemoryMB
    };
  }
  
  // Cache warming
  warmCache(orders: Order[]): void {
    orders.forEach(order => {
      this.setOrder(order.id, order);
    });
  }
}

// Singleton instance
export const ordersCache = new OrdersCacheManager();
```

**Acceptance Criteria:**
- [ ] Memory usage stays under 20MB for 5000 orders
- [ ] Cache hit rate above 70% in normal usage
- [ ] Proper cache invalidation on updates
- [ ] Memory stats reporting works
- [ ] Performance tests show improvement

#### Step 1.3.3: Add Memory Monitoring
**File:** `src/utils/monitoring/memoryMonitor.ts`

```typescript
interface MemoryAlert {
  level: 'warning' | 'critical';
  message: string;
  memoryUsage: number;
  timestamp: number;
}

class MemoryMonitor {
  private alerts: MemoryAlert[] = [];
  private thresholds = {
    warning: 50 * 1024 * 1024,  // 50MB
    critical: 100 * 1024 * 1024 // 100MB
  };
  
  private intervalId: NodeJS.Timeout | null = null;
  
  startMonitoring(intervalMs: number = 30000): void {
    if (this.intervalId) return;
    
    this.intervalId = setInterval(() => {
      this.checkMemoryUsage();
    }, intervalMs);
  }
  
  stopMonitoring(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }
  
  private checkMemoryUsage(): void {
    const memoryUsage = this.getMemoryUsage();
    
    if (memoryUsage.heapUsed > this.thresholds.critical) {
      this.triggerAlert('critical', 
        `Critical memory usage: ${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
        memoryUsage.heapUsed
      );
    } else if (memoryUsage.heapUsed > this.thresholds.warning) {
      this.triggerAlert('warning',
        `High memory usage: ${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
        memoryUsage.heapUsed
      );
    }
  }
  
  private triggerAlert(level: 'warning' | 'critical', message: string, memoryUsage: number): void {
    const alert: MemoryAlert = {
      level,
      message,
      memoryUsage,
      timestamp: Date.now()
    };
    
    this.alerts.push(alert);
    
    // Keep only last 100 alerts
    if (this.alerts.length > 100) {
      this.alerts = this.alerts.slice(-100);
    }
    
    // Log alert
    console[level === 'critical' ? 'error' : 'warn'](`[MemoryMonitor] ${message}`);
    
    // Trigger cleanup if critical
    if (level === 'critical') {
      this.triggerGarbageCollection();
    }
  }
  
  private triggerGarbageCollection(): void {
    // Force garbage collection if available (Node.js with --expose-gc)
    if (global.gc) {
      global.gc();
      console.log('[MemoryMonitor] Forced garbage collection');
    }
    
    // Trigger cache cleanup
    window.dispatchEvent(new CustomEvent('memory-pressure', {
      detail: { memoryUsage: this.getMemoryUsage().heapUsed }
    }));
  }
  
  getMemoryUsage() {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      return process.memoryUsage();
    }
    
    // Browser fallback (approximate)
    return {
      heapUsed: (performance as any).memory?.usedJSHeapSize || 0,
      heapTotal: (performance as any).memory?.totalJSHeapSize || 0,
      external: 0,
      rss: 0
    };
  }
  
  getAlerts(level?: 'warning' | 'critical'): MemoryAlert[] {
    if (level) {
      return this.alerts.filter(alert => alert.level === level);
    }
    return [...this.alerts];
  }
  
  clearAlerts(): void {
    this.alerts = [];
  }
  
  getStats() {
    const memoryUsage = this.getMemoryUsage();
    const recentAlerts = this.alerts.filter(alert => 
      Date.now() - alert.timestamp < 60000 // Last minute
    );
    
    return {
      currentMemoryMB: Math.round(memoryUsage.heapUsed / 1024 / 1024),
      totalMemoryMB: Math.round(memoryUsage.heapTotal / 1024 / 1024),
      warningThresholdMB: Math.round(this.thresholds.warning / 1024 / 1024),
      criticalThresholdMB: Math.round(this.thresholds.critical / 1024 / 1024),
      alertsLastMinute: recentAlerts.length,
      totalAlerts: this.alerts.length,
      isMonitoring: this.intervalId !== null
    };
  }
}

export const memoryMonitor = new MemoryMonitor();

// Auto-start monitoring in development
if (process.env.NODE_ENV === 'development') {
  memoryMonitor.startMonitoring();
}
```

**Acceptance Criteria:**
- [ ] Monitoring detects memory issues accurately
- [ ] Alerts trigger at correct thresholds
- [ ] Cleanup mechanisms work effectively
- [ ] Performance impact is minimal (<1% CPU)
- [ ] Integration with cache management

---

## Phase 2: Performance Optimization (Weeks 3-4)

### Task 2.1: Database Query Optimization (Priority: 🟡 MAJOR)

**Current Problem:** Queries take 500ms-2s for large datasets  
**Target:** Reduce to <100ms with pagination and indexing

#### Step 2.1.1: Implement Query Builder
**File:** `src/services/database/QueryBuilder.ts`

```typescript
interface QueryOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filters?: Record<string, any>;
  search?: string;
}

export class QueryBuilder {
  private baseQuery: string;
  private conditions: string[] = [];
  private parameters: any[] = [];
  private joins: string[] = [];
  
  constructor(tableName: string, selectFields: string[] = ['*']) {
    this.baseQuery = `SELECT ${selectFields.join(', ')} FROM ${tableName}`;
  }
  
  where(field: string, operator: string, value: any): this {
    this.conditions.push(`${field} ${operator} ?`);
    this.parameters.push(value);
    return this;
  }
  
  whereIn(field: string, values: any[]): this {
    if (values.length === 0) return this;
    
    const placeholders = values.map(() => '?').join(', ');
    this.conditions.push(`${field} IN (${placeholders})`);
    this.parameters.push(...values);
    return this;
  }
  
  whereDateRange(field: string, from?: Date, to?: Date): this {
    if (from) {
      this.where(field, '>=', from.toISOString().split('T')[0]);
    }
    if (to) {
      this.where(field, '<=', to.toISOString().split('T')[0]);
    }
    return this;
  }
  
  search(fields: string[], searchTerm: string): this {
    if (!searchTerm.trim()) return this;
    
    const searchConditions = fields.map(field => `${field} ILIKE ?`);
    this.conditions.push(`(${searchConditions.join(' OR ')})`);
    
    const searchValue = `%${searchTerm.trim()}%`;
    this.parameters.push(...fields.map(() => searchValue));
    
    return this;
  }
  
  orderBy(field: string, direction: 'ASC' | 'DESC' = 'DESC'): this {
    this.baseQuery += ` ORDER BY ${field} ${direction}`;
    return this;
  }
  
  paginate(page: number, limit: number): this {
    const offset = (page - 1) * limit;
    this.baseQuery += ` LIMIT ${limit} OFFSET ${offset}`;
    return this;
  }
  
  build(): { query: string; parameters: any[] } {
    let query = this.baseQuery;
    
    if (this.joins.length > 0) {
      query += ' ' + this.joins.join(' ');
    }
    
    if (this.conditions.length > 0) {
      query += ' WHERE ' + this.conditions.join(' AND ');
    }
    
    return {
      query,
      parameters: this.parameters
    };
  }
}

// Helper function for common order queries
export const buildOrdersQuery = (options: QueryOptions) => {
  const {
    page = 1,
    limit = 50,
    sortBy = 'order_date',
    sortOrder = 'desc',
    filters = {},
    search
  } = options;
  
  const builder = new QueryBuilder('orders', [
    'order_id',
    'order_no', 
    'client_name',
    'agent',
    'sector',
    'order_date',
    'status',
    'delivery_date',
    'total_amount',
    'cash_paid',
    'balance',
    'payment_status'
  ]);
  
  // Apply filters
  if (filters.status) {
    builder.where('status', '=', filters.status);
  }
  
  if (filters.sector) {
    builder.where('sector', '=', filters.sector);
  }
  
  if (filters.agent) {
    builder.where('agent', '=', filters.agent);
  }
  
  if (filters.dateRange) {
    builder.whereDateRange('order_date', filters.dateRange.from, filters.dateRange.to);
  }
  
  // Apply search
  if (search) {
    builder.search(['order_no', 'client_name', 'agent'], search);
  }
  
  // Apply sorting and pagination
  builder
    .orderBy(sortBy, sortOrder.toUpperCase() as 'ASC' | 'DESC')
    .paginate(page, limit);
    
  return builder.build();
};
```

**Acceptance Criteria:**
- [ ] Query builder generates efficient SQL
- [ ] Proper parameter binding for security
- [ ] Support for complex filtering
- [ ] Performance tests show <100ms queries
- [ ] Unit tests cover all query patterns

#### Step 2.1.2: Add Database Indexes
**File:** `migrations/008_add_performance_indexes.sql`

```sql
-- Performance indexes for orders table
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_date_status 
ON orders(order_date DESC, status) WHERE order_date IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_client_date
ON orders(client_name, order_date DESC) WHERE client_name IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_sector_date
ON orders(sector, order_date DESC) WHERE sector IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_agent_date  
ON orders(agent, order_date DESC) WHERE agent IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_status_date
ON orders(status, order_date DESC) WHERE status IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_quarter
ON orders(quarter) WHERE quarter IS NOT NULL;

-- Full text search index for client names
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_client_search
ON orders USING gin(to_tsvector('english', client_name));

-- Composite index for common filter combinations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_sector_status_date
ON orders(sector, status, order_date DESC) 
WHERE sector IS NOT NULL AND status IS NOT NULL;

-- Index for order items joins
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_order_items_order_id
ON order_items(order_id);

-- Index for order payments joins  
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_order_payments_order_id
ON order_payments(order_id);

-- Analyze tables to update statistics
ANALYZE orders;
ANALYZE order_items;
ANALYZE order_payments;
```

**Acceptance Criteria:**
- [ ] Indexes created without blocking operations
- [ ] Query performance improves significantly
- [ ] Database size increase is acceptable (<20%)
- [ ] All common queries use indexes efficiently

### Task 2.2: Virtual Scrolling Implementation (Priority: 🟡 MAJOR)

**Current Problem:** Rendering 1000+ orders causes frame drops  
**Target:** Smooth scrolling regardless of dataset size

#### Step 2.2.1: Create Virtual List Component
**File:** `src/components/ui/VirtualList.tsx`

```typescript
import React, { useState, useEffect, useMemo, useCallback } from 'react';

interface VirtualListProps<T> {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  overscan?: number;
  onScrollEnd?: () => void;
  className?: string;
}

export function VirtualList<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
  onScrollEnd,
  className = ''
}: VirtualListProps<T>) {
  const [scrollTop, setScrollTop] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);
  
  // Calculate visible range
  const visibleRange = useMemo(() => {
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const endIndex = Math.min(
      items.length - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    );
    
    return { startIndex, endIndex };
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan]);
  
  // Generate visible items
  const visibleItems = useMemo(() => {
    const { startIndex, endIndex } = visibleRange;
    const visible = [];
    
    for (let i = startIndex; i <= endIndex; i++) {
      visible.push({
        index: i,
        item: items[i],
        style: {
          position: 'absolute' as const,
          top: i * itemHeight,
          left: 0,
          right: 0,
          height: itemHeight
        }
      });
    }
    
    return visible;
  }, [items, visibleRange, itemHeight]);
  
  // Handle scroll events
  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    const newScrollTop = event.currentTarget.scrollTop;
    setScrollTop(newScrollTop);
    
    if (!isScrolling) {
      setIsScrolling(true);
    }
    
    // Check if scrolled to bottom
    const scrollHeight = event.currentTarget.scrollHeight;
    const clientHeight = event.currentTarget.clientHeight;
    
    if (newScrollTop + clientHeight >= scrollHeight - 100) {
      onScrollEnd?.();
    }
  }, [isScrolling, onScrollEnd]);
  
  // Debounce scrolling state
  useEffect(() => {
    if (isScrolling) {
      const timeout = setTimeout(() => {
        setIsScrolling(false);
      }, 150);
      
      return () => clearTimeout(timeout);
    }
  }, [isScrolling, scrollTop]);
  
  const totalHeight = items.length * itemHeight;
  
  return (
    <div
      className={`virtual-list ${className}`}
      style={{
        height: containerHeight,
        overflow: 'auto',
        position: 'relative'
      }}
      onScroll={handleScroll}
    >
      <div
        style={{
          height: totalHeight,
          position: 'relative'
        }}
      >
        {visibleItems.map(({ index, item, style }) => (
          <div key={index} style={style}>
            {renderItem(item, index)}
          </div>
        ))}
      </div>
    </div>
  );
}

// Hook for easier virtual list usage
export function useVirtualList<T>(items: T[], itemHeight: number) {
  const [containerRef, setContainerRef] = useState<HTMLDivElement | null>(null);
  const [containerHeight, setContainerHeight] = useState(400);
  
  useEffect(() => {
    if (!containerRef) return;
    
    const observer = new ResizeObserver(entries => {
      for (const entry of entries) {
        setContainerHeight(entry.contentRect.height);
      }
    });
    
    observer.observe(containerRef);
    
    return () => observer.disconnect();
  }, [containerRef]);
  
  return {
    containerRef: setContainerRef,
    containerHeight,
    VirtualList: ({ renderItem, ...props }: Omit<VirtualListProps<T>, 'items' | 'itemHeight' | 'containerHeight'>) => (
      <VirtualList
        items={items}
        itemHeight={itemHeight}
        containerHeight={containerHeight}
        renderItem={renderItem}
        {...props}
      />
    )
  };
}
```

**Acceptance Criteria:**
- [ ] Smooth scrolling with 10,000+ items
- [ ] Memory usage stays constant regardless of list size
- [ ] Proper keyboard navigation support
- [ ] Accessible screen reader support
- [ ] Performance benchmarks meet targets

#### Step 2.2.2: Integrate Virtual Scrolling with Orders List
**File:** `src/components/orders/VirtualizedOrdersList.tsx`

```typescript
import React, { memo } from 'react';
import { VirtualList } from '../ui/VirtualList';
import { OrderCard } from './OrderCard';
import type { Order } from '../../types/order.types';

interface VirtualizedOrdersListProps {
  orders: Order[];
  onOrderClick?: (order: Order) => void;
  onOrderUpdate?: (order: Order) => void;
  onLoadMore?: () => void;
  isLoading?: boolean;
  hasMore?: boolean;
}

const ITEM_HEIGHT = 120; // Height of each order card in pixels

export const VirtualizedOrdersList = memo(({
  orders,
  onOrderClick,
  onOrderUpdate,
  onLoadMore,
  isLoading,
  hasMore
}: VirtualizedOrdersListProps) => {
  const renderOrderItem = (order: Order, index: number) => (
    <div className="px-4 pb-2">
      <OrderCard
        key={order.id}
        order={order}
        onClick={() => onOrderClick?.(order)}
        onUpdate={onOrderUpdate}
      />
    </div>
  );
  
  const handleScrollEnd = () => {
    if (hasMore && !isLoading) {
      onLoadMore?.();
    }
  };
  
  return (
    <div className="h-full w-full">
      <VirtualList
        items={orders}
        itemHeight={ITEM_HEIGHT}
        containerHeight={600} // Will be overridden by parent container
        renderItem={renderOrderItem}
        onScrollEnd={handleScrollEnd}
        className="orders-virtual-list"
      />
      
      {isLoading && (
        <div className="flex justify-center p-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      )}
    </div>
  );
});

VirtualizedOrdersList.displayName = 'VirtualizedOrdersList';
```

**Acceptance Criteria:**
- [ ] Replace existing orders list without breaking functionality
- [ ] Maintain all existing features (filtering, sorting, etc.)
- [ ] Smooth infinite scrolling
- [ ] Loading states work correctly
- [ ] No performance regression for small lists

---

## Phase 3: Architecture Enhancement (Weeks 5-6)

### Task 3.1: Error Boundary Implementation (Priority: 🟢 NICE TO HAVE)

#### Step 3.1.1: Create Orders Error Boundary
**File:** `src/components/errors/OrdersErrorBoundary.tsx`

```typescript
import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { Button } from '../ui/button';
import { Alert, AlertDescription, AlertTitle } from '../ui/alert';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  retryCount: number;
}

export class OrdersErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0
    };
  }
  
  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error
    };
  }
  
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo
    });
    
    // Log error
    console.error('OrdersErrorBoundary caught an error:', error, errorInfo);
    
    // Call optional error handler
    this.props.onError?.(error, errorInfo);
    
    // Send to error tracking service
    this.reportError(error, errorInfo);
  }
  
  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    // In a real app, send to error tracking service like Sentry
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      retryCount: this.state.retryCount
    };
    
    // For now, just log to console
    console.error('Error Report:', errorReport);
  };
  
  private handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: prevState.retryCount + 1
    }));
  };
  
  private getErrorMessage = (error: Error): string => {
    // Provide user-friendly error messages
    if (error.message.includes('Network')) {
      return 'Unable to connect to the server. Please check your internet connection and try again.';
    }
    
    if (error.message.includes('Permission')) {
      return 'You don\'t have permission to view orders. Please contact your administrator.';
    }
    
    if (error.message.includes('timeout')) {
      return 'The request took too long to complete. Please try again.';
    }
    
    return 'Something went wrong while loading orders. Our team has been notified.';
  };
  
  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }
      
      // Default error UI
      return (
        <div className="p-6 max-w-lg mx-auto">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Unable to Load Orders</AlertTitle>
            <AlertDescription className="mt-2">
              {this.getErrorMessage(this.state.error!)}
            </AlertDescription>
          </Alert>
          
          <div className="mt-4 space-y-2">
            <Button onClick={this.handleRetry} className="w-full">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
            
            {process.env.NODE_ENV === 'development' && (
              <details className="mt-4 p-4 bg-gray-100 rounded text-sm">
                <summary className="cursor-pointer font-medium">
                  Error Details (Development Only)
                </summary>
                <pre className="mt-2 whitespace-pre-wrap text-xs">
                  {this.state.error?.stack}
                </pre>
                <pre className="mt-2 whitespace-pre-wrap text-xs">
                  {this.state.errorInfo?.componentStack}
                </pre>
              </details>
            )}
          </div>
        </div>
      );
    }
    
    return this.props.children;
  }
}

// Higher-order component for easier usage
export function withOrdersErrorBoundary<P extends object>(
  Component: React.ComponentType<P>
) {
  const WrappedComponent = (props: P) => (
    <OrdersErrorBoundary>
      <Component {...props} />
    </OrdersErrorBoundary>
  );
  
  WrappedComponent.displayName = `withOrdersErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}
```

### Task 3.2: Performance Monitoring Dashboard (Priority: 🟢 NICE TO HAVE)

#### Step 3.2.1: Create Performance Monitor Component
**File:** `src/components/dev/PerformanceMonitor.tsx`

```typescript
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { memoryMonitor } from '../../utils/monitoring/memoryMonitor';
import { ordersCache } from '../../services/cache/OrdersCacheManager';

export const PerformanceMonitor = () => {
  const [memoryStats, setMemoryStats] = useState(memoryMonitor.getStats());
  const [cacheStats, setCacheStats] = useState(ordersCache.getMemoryStats());
  const [isVisible, setIsVisible] = useState(false);
  
  useEffect(() => {
    // Only show in development
    if (process.env.NODE_ENV !== 'development') return;
    
    const interval = setInterval(() => {
      setMemoryStats(memoryMonitor.getStats());
      setCacheStats(ordersCache.getMemoryStats());
    }, 5000);
    
    // Show/hide with keyboard shortcut
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'P') {
        setIsVisible(prev => !prev);
      }
    };
    
    document.addEventListener('keydown', handleKeyPress);
    
    return () => {
      clearInterval(interval);
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, []);
  
  if (process.env.NODE_ENV !== 'development' || !isVisible) {
    return null;
  }
  
  const getMemoryBadgeVariant = (currentMB: number, warningMB: number, criticalMB: number) => {
    if (currentMB > criticalMB) return 'destructive';
    if (currentMB > warningMB) return 'secondary';
    return 'default';
  };
  
  return (
    <div className="fixed bottom-4 right-4 w-80 z-50">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm">Performance Monitor</CardTitle>
          <p className="text-xs text-muted-foreground">
            Press Ctrl+Shift+P to toggle
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Memory Usage */}
          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium">Memory Usage</span>
              <Badge variant={getMemoryBadgeVariant(
                memoryStats.currentMemoryMB,
                memoryStats.warningThresholdMB,
                memoryStats.criticalThresholdMB
              )}>
                {memoryStats.currentMemoryMB}MB
              </Badge>
            </div>
            
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full ${
                  memoryStats.currentMemoryMB > memoryStats.criticalThresholdMB
                    ? 'bg-red-600'
                    : memoryStats.currentMemoryMB > memoryStats.warningThresholdMB
                    ? 'bg-yellow-600'
                    : 'bg-green-600'
                }`}
                style={{
                  width: `${Math.min(100, (memoryStats.currentMemoryMB / memoryStats.totalMemoryMB) * 100)}%`
                }}
              />
            </div>
            
            <div className="text-xs text-muted-foreground mt-1">
              {memoryStats.alertsLastMinute > 0 && (
                <span className="text-red-600">
                  {memoryStats.alertsLastMinute} alerts in last minute
                </span>
              )}
            </div>
          </div>
          
          {/* Cache Statistics */}
          <div>
            <h4 className="text-sm font-medium mb-2">Cache Performance</h4>
            
            <div className="space-y-1 text-xs">
              <div className="flex justify-between">
                <span>Pages Cache:</span>
                <span>{cacheStats.pages.size} items ({cacheStats.pages.estimatedMemoryMB}MB)</span>
              </div>
              
              <div className="flex justify-between">
                <span>Orders Cache:</span>
                <span>{cacheStats.orders.size} items ({cacheStats.orders.estimatedMemoryMB}MB)</span>
              </div>
              
              <div className="flex justify-between">
                <span>Total Cache:</span>
                <Badge variant="outline">
                  {cacheStats.totalEstimatedMemoryMB}MB
                </Badge>
              </div>
            </div>
          </div>
          
          {/* Performance Metrics */}
          <div>
            <h4 className="text-sm font-medium mb-2">Performance</h4>
            <div className="space-y-1 text-xs">
              <div className="flex justify-between">
                <span>Monitoring:</span>
                <Badge variant={memoryStats.isMonitoring ? 'default' : 'secondary'}>
                  {memoryStats.isMonitoring ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
```

---

## 🧪 Testing Strategy

### Unit Testing Checklist

```bash
# Run tests for each refactored module
npm test src/hooks/orders/useOrdersCore.test.ts
npm test src/hooks/orders/useOrdersProgressive.test.ts
npm test src/services/orders/orderQueries.test.ts
npm test src/services/orders/orderTransforms.test.ts
npm test src/utils/cache/LRUCache.test.ts
```

### Integration Testing Checklist

```bash
# Test complete order flow
npm test src/components/orders/OrdersList.integration.test.ts
npm test src/hooks/orders/useOrders.integration.test.ts
```

### Performance Testing Checklist

```bash
# Run performance benchmarks
npm run test:performance
npm run test:memory-usage
npm run test:query-performance
```

---

## 🚀 Deployment Checklist

### Phase 1 Deployment (Critical Issues)
- [ ] All files under 300 lines
- [ ] Memory monitoring active in production
- [ ] Cache limits properly configured
- [ ] Error boundaries implemented
- [ ] Performance alerts configured
- [ ] Database indexes created
- [ ] Full regression testing completed
- [ ] Load testing with 5000+ orders passed

### Phase 2 Deployment (Performance)
- [ ] Virtual scrolling working smoothly
- [ ] Query performance <100ms consistently
- [ ] Memory usage <20MB for 5K orders
- [ ] Cache hit rate >70%
- [ ] All performance tests passing

### Phase 3 Deployment (Enhancements)
- [ ] Error tracking integrated
- [ ] Performance monitoring dashboard available
- [ ] Documentation updated
- [ ] Team training completed

---

## 📞 Support & Resources

### Getting Help
- **Implementation Questions:** Check code examples in this guide
- **Architecture Decisions:** Review full audit report
- **Performance Issues:** Use performance monitoring tools
- **Testing Problems:** Follow testing strategy section

### Key Resources
- [Full Audit Report](./audit-report.md)
- [SWR Documentation](https://swr.vercel.app/)
- [React Performance Guide](https://react.dev/learn/render-and-commit)
- [Database Performance Best Practices](https://supabase.com/docs/guides/database/performance)

---

**Document Version:** 1.0  
**Last Updated:** January 8, 2025  
**Estimated Completion:** 4-6 weeks with 2-3 developers  
**Success Metrics:** Grade improvement from C+ to A-, memory usage <20MB, query time <100ms