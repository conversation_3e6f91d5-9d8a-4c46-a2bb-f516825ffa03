-- Migration: Unified Product Data Architecture
-- Ensures data consistency between production_cost_component_values and product_line tables
-- Adds triggers to auto-maintain sync going forward

-- Step 1: Backfill missing product_line entries for products with cost data
INSERT INTO product_line (
  category_id,
  product_type_id,
  size_id,
  is_active,
  is_featured,
  sort_order,
  minimum_quantity,
  production_time_days,
  attributes
)
SELECT DISTINCT
  pcv.product_category_id,
  pcv.product_type_id,
  pcv.size_id,
  true as is_active,
  false as is_featured,
  0 as sort_order,
  1 as minimum_quantity,
  3 as production_time_days,
  jsonb_build_object(
    'backfilled', 'unified_architecture_migration',
    'backfilled_at', NOW(),
    'reason', 'Product had cost data but no product_line entry'
  ) as attributes
FROM production_cost_component_values pcv
WHERE pcv.is_current = true
  AND pcv.tier_metadata IS NULL  -- Only basic costs
  AND NOT EXISTS (
    SELECT 1 FROM product_line pl
    WHERE pl.category_id = pcv.product_category_id
      AND pl.product_type_id = pcv.product_type_id
      AND pl.size_id = pcv.size_id
  );

-- Step 2: Create function to auto-maintain product_line sync
CREATE OR REPLACE FUNCTION auto_maintain_product_line_sync()
RETURNS TRIGGER AS $$
BEGIN
  -- Only process basic costs (tier_metadata IS NULL)
  IF NEW.tier_metadata IS NULL AND NEW.is_current = true THEN
    -- Insert into product_line if combination doesn't exist
    INSERT INTO product_line (
      category_id,
      product_type_id, 
      size_id,
      is_active,
      is_featured,
      sort_order,
      minimum_quantity,
      production_time_days,
      attributes
    ) VALUES (
      NEW.product_category_id,
      NEW.product_type_id,
      NEW.size_id,
      true,
      false,
      0,
      1,
      3,
      jsonb_build_object('auto_created', 'cost_component_added', 'created_at', NOW())
    ) ON CONFLICT (category_id, product_type_id, size_id) 
    DO UPDATE SET
      -- If product was deprecated due to cost removal, reactivate it
      is_active = CASE 
        WHEN product_line.deprecation_status = 'deprecated_cost_removed' THEN true
        ELSE product_line.is_active
      END,
      deprecation_status = CASE 
        WHEN product_line.deprecation_status = 'deprecated_cost_removed' THEN 'active'
        ELSE product_line.deprecation_status
      END,
      deprecation_reason = CASE 
        WHEN product_line.deprecation_status = 'deprecated_cost_removed' THEN NULL
        ELSE product_line.deprecation_reason
      END,
      deprecated_at = CASE 
        WHEN product_line.deprecation_status = 'deprecated_cost_removed' THEN NULL
        ELSE product_line.deprecated_at
      END,
      deprecated_by = CASE 
        WHEN product_line.deprecation_status = 'deprecated_cost_removed' THEN NULL
        ELSE product_line.deprecated_by
      END,
      attributes = product_line.attributes || jsonb_build_object('cost_restored_at', NOW()),
      updated_at = NOW();
      
    RAISE INFO 'Auto-maintained product_line entry for product combination %-%-%', 
      NEW.product_category_id, NEW.product_type_id, NEW.size_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 3: Create trigger to auto-maintain product_line sync
DROP TRIGGER IF EXISTS trigger_auto_maintain_product_line_sync ON production_cost_component_values;

CREATE TRIGGER trigger_auto_maintain_product_line_sync
  AFTER INSERT OR UPDATE ON production_cost_component_values
  FOR EACH ROW
  EXECUTE FUNCTION auto_maintain_product_line_sync();

-- Step 4: Add comments for documentation
COMMENT ON FUNCTION auto_maintain_product_line_sync() IS 
'Automatically maintains sync between production_cost_component_values and product_line tables. 
When basic cost components are added, ensures corresponding product_line entry exists.
Reactivates deprecated products when costs are restored.';

COMMENT ON TRIGGER trigger_auto_maintain_product_line_sync ON production_cost_component_values IS
'Ensures product_line table stays in sync with cost data. Products are auto-created when costs are added.';

-- Step 5: Create view for unified product data access (optional, for reporting)
CREATE OR REPLACE VIEW unified_product_data_view AS
SELECT 
  COALESCE(pl.id::text, 'cost-' || pcv_summary.combination_key) as id,
  pcv_summary.category_id,
  pcv_summary.product_type_id,
  pcv_summary.size_id,
  cat_attr.value as category_name,
  type_attr.value as product_type_name,
  size_attr.value as size_name,
  
  -- Product line status
  pl.id as product_line_id,
  COALESCE(pl.is_active, true) as is_active,
  COALESCE(pl.is_featured, false) as is_featured,
  pl.deprecation_status,
  pl.deprecation_reason,
  COALESCE(pl.minimum_quantity, 1) as minimum_quantity,
  COALESCE(pl.production_time_days, 3) as production_time_days,
  COALESCE(pl.attributes, '{}'::jsonb) as attributes,
  
  -- Cost data status
  pcv_summary.combination_key,
  CASE WHEN pcv_summary.combination_key IS NOT NULL THEN true ELSE false END as has_cost_data,
  COALESCE(pcv_summary.basic_cost_components, 0) as basic_cost_components_count,
  COALESCE(pcv_summary.additional_cost_components, 0) as additional_cost_components_count,
  COALESCE(pcv_summary.total_basic_cost, 0) as total_basic_cost,
  pcv_summary.last_cost_update,
  
  -- Display names
  type_attr.value || ' (' || size_attr.value || ')' as display_name,
  cat_attr.value || ' - ' || type_attr.value || ' - ' || size_attr.value as full_name,
  
  -- Metadata
  COALESCE(pl.created_at, pcv_summary.last_cost_update) as created_at,
  COALESCE(pl.updated_at, pcv_summary.last_cost_update) as updated_at,
  CASE 
    WHEN pl.id IS NOT NULL AND pcv_summary.combination_key IS NOT NULL THEN 'merged'
    WHEN pl.id IS NOT NULL THEN 'product_line'
    ELSE 'cost_data'
  END as data_source
FROM (
  -- Aggregate cost data by product combination
  SELECT 
    product_category_id as category_id,
    product_type_id,
    size_id,
    product_category_id || '_' || product_type_id || '_' || size_id as combination_key,
    COUNT(CASE WHEN tier_metadata IS NULL THEN 1 END) as basic_cost_components,
    COUNT(CASE WHEN tier_metadata IS NOT NULL THEN 1 END) as additional_cost_components,
    SUM(CASE WHEN tier_metadata IS NULL THEN value ELSE 0 END) as total_basic_cost,
    MAX(updated_at) as last_cost_update
  FROM production_cost_component_values
  WHERE is_current = true
  GROUP BY product_category_id, product_type_id, size_id
) pcv_summary
FULL OUTER JOIN product_line pl ON (
  pl.category_id = pcv_summary.category_id AND
  pl.product_type_id = pcv_summary.product_type_id AND
  pl.size_id = pcv_summary.size_id
)
LEFT JOIN product_attributes cat_attr ON cat_attr.id = COALESCE(pcv_summary.category_id, pl.category_id)
LEFT JOIN product_attributes type_attr ON type_attr.id = COALESCE(pcv_summary.product_type_id, pl.product_type_id)
LEFT JOIN product_attributes size_attr ON size_attr.id = COALESCE(pcv_summary.size_id, pl.size_id);

COMMENT ON VIEW unified_product_data_view IS
'Unified view of all product data combining cost components and product line entries. 
Use this view for reporting and analytics that need complete product information.
For application logic, use the unifiedProductData.service.ts instead.';