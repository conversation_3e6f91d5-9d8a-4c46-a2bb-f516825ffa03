-- ============================================================================
-- PERMISSIONS SYSTEM DATABASE SCHEMA - FOCUSED
-- Migration: 010_create_permissions_system.sql
-- 
-- Creates ONLY the permissions system tables - no auth or other systems mixed
-- Pure permissions implementation that can be extended by other systems later
-- 
-- Reference: docs/auth/implementation/permissions-implementation-roadmap.md
-- Standards: NIST RBAC model focused on permissions only
-- ============================================================================

-- ============================================================================
-- PERMISSIONS REGISTRY TABLE
-- ============================================================================
-- Central registry of all permissions in the system
-- Follows the 19-permission page-level + strategic features model

CREATE TABLE permissions (
    id SERIAL PRIMARY KEY,
    key VARCHAR(255) UNIQUE NOT NULL,           -- e.g., 'pages.orders.access'
    name VARCHAR(255) NOT NULL,                 -- Human-readable name
    description TEXT,                           -- Detailed description
    category VARCHAR(100) NOT NULL,             -- 'Page Access', 'Financial Controls', etc.
    is_active BOOLEAN DEFAULT TRUE,             -- Can be disabled without deletion
    requires_permissions TEXT[],                -- Array of prerequisite permissions
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints for data integrity
    CONSTRAINT permissions_key_format CHECK (
        key ~ '^[a-z_]+\.[a-z_]+(\.)?[a-z_]*$'  -- Enforce naming convention
    ),
    CONSTRAINT permissions_category_valid CHECK (
        category IN (
            'Page Access',
            'Order Component Controls', 
            'Financial Controls',
            'Operational Controls',
            'Data Security',
            'Administrative Controls',
            'System Administration',
            'Analytics Access'
        )
    )
);

-- Indexes for performance (<100ms permission queries)
CREATE INDEX permissions_key_idx ON permissions(key);
CREATE INDEX permissions_category_idx ON permissions(category);
CREATE INDEX permissions_is_active_idx ON permissions(is_active) WHERE is_active = TRUE;

-- ============================================================================
-- ROLES TABLE
-- ============================================================================
-- Business function-aligned role templates that group permissions

CREATE TABLE roles (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,          -- Role identifier (e.g., 'order_manager')
    display_name VARCHAR(255) NOT NULL,         -- Human-readable name
    description TEXT,                           -- Role purpose and scope
    permissions TEXT[] NOT NULL,                -- Array of permission keys
    is_active BOOLEAN DEFAULT TRUE,             -- Can be disabled
    is_system_role BOOLEAN DEFAULT FALSE,       -- Protected system roles
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT roles_name_format CHECK (
        name ~ '^[a-z_]+$'  -- Lowercase with underscores only
    ),
    CONSTRAINT roles_permissions_not_empty CHECK (
        array_length(permissions, 1) > 0
    )
);

-- Indexes for performance
CREATE INDEX roles_name_idx ON roles(name);
CREATE INDEX roles_is_active_idx ON roles(is_active) WHERE is_active = TRUE;
CREATE INDEX roles_permissions_idx ON roles USING GIN(permissions);

-- ============================================================================
-- PERMISSION VALIDATION FUNCTIONS
-- ============================================================================
-- Functions to validate permissions and dependencies

-- Function to validate a permission key exists and is active
CREATE OR REPLACE FUNCTION validate_permission_key(permission_key VARCHAR)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM permissions 
        WHERE key = permission_key AND is_active = TRUE
    );
END;
$$ LANGUAGE plpgsql;

-- Function to get all available permissions (for UI components)
CREATE OR REPLACE FUNCTION get_all_permissions()
RETURNS TABLE (
    permission_key VARCHAR,
    permission_name VARCHAR,
    category VARCHAR,
    description TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT p.key, p.name, p.category, p.description
    FROM permissions p
    WHERE p.is_active = TRUE
    ORDER BY p.category, p.name;
END;
$$ LANGUAGE plpgsql;

-- Function to validate role permissions (all permissions must exist)
CREATE OR REPLACE FUNCTION validate_role_permissions(role_permissions TEXT[])
RETURNS BOOLEAN AS $$
DECLARE
    perm VARCHAR;
BEGIN
    FOREACH perm IN ARRAY role_permissions
    LOOP
        IF NOT validate_permission_key(perm) THEN
            RETURN FALSE;
        END IF;
    END LOOP;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- TRIGGERS
-- ============================================================================
-- Automated triggers for data integrity and timestamp management

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply updated_at triggers
CREATE TRIGGER permissions_updated_at 
    BEFORE UPDATE ON permissions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER roles_updated_at 
    BEFORE UPDATE ON roles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Trigger to validate role permissions before insert/update
CREATE OR REPLACE FUNCTION validate_role_permissions_trigger()
RETURNS TRIGGER AS $$
BEGIN
    IF NOT validate_role_permissions(NEW.permissions) THEN
        RAISE EXCEPTION 'Role contains invalid or inactive permissions';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER roles_validate_permissions
    BEFORE INSERT OR UPDATE ON roles
    FOR EACH ROW EXECUTE FUNCTION validate_role_permissions_trigger();

-- ============================================================================
-- VIEWS
-- ============================================================================
-- Convenient views for common queries

-- View to see permission distribution by category
CREATE VIEW permission_categories_summary AS
SELECT 
    category,
    COUNT(*) as total_permissions,
    COUNT(CASE WHEN is_active THEN 1 END) as active_permissions
FROM permissions
GROUP BY category
ORDER BY category;

-- View to see role complexity (number of permissions per role)
CREATE VIEW role_complexity_summary AS
SELECT 
    name,
    display_name,
    array_length(permissions, 1) as permission_count,
    is_active,
    is_system_role
FROM roles
WHERE is_active = TRUE
ORDER BY permission_count DESC;

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================
-- Add comments for database documentation

COMMENT ON TABLE permissions IS 'Registry of all system permissions - pure permissions data only';
COMMENT ON TABLE roles IS 'Business function-aligned role templates containing permission arrays';

COMMENT ON FUNCTION validate_permission_key IS 'Validates that a permission key exists and is active';
COMMENT ON FUNCTION get_all_permissions IS 'Returns all active permissions for UI components';
COMMENT ON FUNCTION validate_role_permissions IS 'Validates that all permissions in a role are valid';

COMMENT ON VIEW permission_categories_summary IS 'Summary of permissions grouped by category';
COMMENT ON VIEW role_complexity_summary IS 'Analysis of roles by number of permissions';

-- ============================================================================
-- MIGRATION VERIFICATION
-- ============================================================================
-- Verification queries to ensure schema was created correctly

DO $$
BEGIN
    -- Verify permissions table exists
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'permissions') THEN
        RAISE EXCEPTION 'permissions table not created';
    END IF;
    
    -- Verify roles table exists
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'roles') THEN
        RAISE EXCEPTION 'roles table not created';
    END IF;
    
    -- Verify functions exist
    IF NOT EXISTS (SELECT FROM pg_proc WHERE proname = 'validate_permission_key') THEN
        RAISE EXCEPTION 'validate_permission_key function not created';
    END IF;
    
    IF NOT EXISTS (SELECT FROM pg_proc WHERE proname = 'get_all_permissions') THEN
        RAISE EXCEPTION 'get_all_permissions function not created';
    END IF;
    
    -- Verify views exist
    IF NOT EXISTS (SELECT FROM information_schema.views WHERE table_name = 'permission_categories_summary') THEN
        RAISE EXCEPTION 'permission_categories_summary view not created';
    END IF;
    
    RAISE NOTICE 'Clean permissions system schema created successfully - ready for data seeding';
END $$;

-- ============================================================================
-- END OF MIGRATION
-- ============================================================================