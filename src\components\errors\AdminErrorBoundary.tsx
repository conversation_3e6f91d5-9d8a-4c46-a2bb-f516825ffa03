import React from 'react'
import { FeatureErrorBoundary } from '../ui/FeatureErrorBoundary'
import { Settings } from 'lucide-react'

interface AdminErrorBoundaryProps {
  children: React.ReactNode
}

/**
 * Error boundary specifically for the Admin/Settings features
 */
export function AdminErrorBoundary({ children }: AdminErrorBoundaryProps) {
  return (
    <FeatureErrorBoundary
      featureName="Admin Settings"
      fallback={
        <div className="flex items-center justify-center min-h-[400px] p-4">
          <div className="text-center space-y-4">
            <div className="mx-auto p-3 bg-purple-100 rounded-full w-fit">
              <Settings className="h-8 w-8 text-purple-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold mb-2">Admin Panel Error</h2>
              <p className="text-muted-foreground mb-4">
                The admin settings panel encountered an error and cannot be displayed.
              </p>
              <button 
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
              >
                Reload Settings
              </button>
            </div>
          </div>
        </div>
      }
    >
      {children}
    </FeatureErrorBoundary>
  )
}