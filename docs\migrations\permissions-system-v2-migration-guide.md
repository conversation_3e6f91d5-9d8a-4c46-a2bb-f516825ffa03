# Permissions System V2 Migration Guide

## Overview

This migration updates the database permissions system to align with the superior frontend architecture, replacing the fragmented page-based permissions with a clean resource-action model.

## Migration Files

- **Migration SQL**: `migrations/043_modernize_permissions_system_v2.sql`
- **Verification Script**: `scripts/verify-permissions-migration.sql`

## What This Migration Does

### 1. **Creates Backup Tables**
- `permissions_backup` - Backup of original permissions
- `roles_backup` - Backup of original roles  
- `authorized_users_backup` - Backup of user data

### 2. **Replaces Fragmented Permissions**

**Old System (Database):**
```
pages.orders_access, orders.general_info_edit, orders.items_edit,
pages.analytics_overview_access, pages.analytics_general_access,
admin.permissions_assign, admin.users_create, system.full_access
```

**New System (Aligned with Frontend):**
```
orders.view, orders.create, orders.edit, orders.delete,
products.view, products.create, products.edit, products.delete,
clients.view, clients.create, clients.edit, clients.delete,
analytics.view, analytics.export,
settings.view, settings.edit,
admin.users, admin.permissions, system.admin
```

### 3. **Simplifies Role Structure**

**Before:**
- `admin`, `order_manager`, `product_manager`, `supervisor`, `viewer`
- Complex permission arrays with redundant and fragmented permissions

**After:**
- `viewer` → Read-only access
- `operator` → Daily operations (create/edit)  
- `manager` → Full business operations
- `admin` → System administration

### 4. **Migrates User Data**

- Maps old role templates to new roles
- Converts old permission arrays to new format
- Ensures all users maintain appropriate access levels

## Running the Migration

### Option 1: Via Supabase Dashboard
1. Copy contents of `migrations/043_modernize_permissions_system_v2.sql`
2. Run in Supabase SQL Editor
3. Review results with verification queries

### Option 2: Via CLI (if available)
```bash
supabase db push --include=043_modernize_permissions_system_v2.sql
```

### Option 3: Via MCP (if connection available)
The migration was prepared for MCP execution but connection issues may require manual execution.

## Verification

Run the verification script to ensure migration success:

```sql
-- Execute: scripts/verify-permissions-migration.sql
```

### Expected Results:
- ✅ 17 permissions created (4 each for orders/products/clients, 2 analytics, 2 settings, 3 admin)
- ✅ 4 roles created (viewer, operator, manager, admin)
- ✅ All users migrated to new role structure
- ✅ Backup tables created for rollback capability

## Post-Migration Testing

### 1. **Frontend Permission Checks**
Test these components after migration:
- `usePermissions` hook should return proper permissions
- `PermissionGuard` components should work correctly
- User management UI should function properly

### 2. **Sample Test Cases**
```typescript
// These should now work correctly:
hasPermission('orders.view') // Instead of 'pages.orders_access'  
hasPermission('products.edit') // Instead of 'products.pricing_edit'
hasPermission('system.admin') // Instead of 'system.full_access'
```

### 3. **Role Assignment Testing**
- Verify users can be assigned to viewer/operator/manager/admin roles
- Test role-based permission inheritance
- Confirm permission overrides still work

## Rollback Plan

If issues occur, rollback using backup tables:

```sql
-- EMERGENCY ROLLBACK (use with caution)
DELETE FROM permissions;
DELETE FROM roles; 
DELETE FROM authorized_users;

INSERT INTO permissions SELECT * FROM permissions_backup;
INSERT INTO roles SELECT * FROM roles_backup;
INSERT INTO authorized_users SELECT * FROM authorized_users_backup;
```

## Benefits of New System

1. **Frontend-Database Alignment**: Eliminates permission key mismatches
2. **Semantic Clarity**: `orders.view` is clearer than `pages.orders_access`
3. **Maintainability**: Adding new resources follows predictable pattern
4. **Type Safety**: Frontend TypeScript validation works correctly
5. **Industry Standard**: Follows RBAC best practices
6. **Performance**: Simpler permission checks, better caching

## Migration Mapping

| Old Permission | New Permission | Notes |
|---|---|---|
| `pages.orders_access` | `orders.view` | Page access → Resource access |
| `orders.general_info_edit` + `orders.items_edit` | `orders.edit` | Consolidated granular permissions |
| `pages.analytics_*_access` | `analytics.view` | Unified analytics access |
| `admin.permissions_assign` | `admin.permissions` | Semantic naming |
| `system.full_access` | `system.admin` | Standard admin naming |

## Support

If you encounter issues:
1. Check verification script results
2. Review backup tables for data integrity  
3. Test frontend permission components
4. Verify user role assignments in UI

The migration preserves all user access levels while modernizing the underlying permission structure to match the well-designed frontend architecture.