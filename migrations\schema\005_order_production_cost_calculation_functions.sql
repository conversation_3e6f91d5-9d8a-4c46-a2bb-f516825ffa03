-- Migration: Order Production Cost Calculation Functions
-- Description: Database-driven production cost calculation system for order items
-- Version: 017
-- Date: 2025-01-09

-- =====================================================
-- 1. Map Order Item Attributes to IDs Function
-- =====================================================

CREATE OR REPLACE FUNCTION map_order_item_to_attributes(
  p_product TEXT,
  p_product_type TEXT, 
  p_size TEXT
)
RETURNS TABLE(
  category_id UUID, 
  product_type_id UUID, 
  size_id UUID,
  mapping_success BOOLEAN
) 
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    cat.id as category_id,
    pt.id as product_type_id,
    sz.id as size_id,
    (cat.id IS NOT NULL AND pt.id IS NOT NULL AND sz.id IS NOT NULL) as mapping_success
  FROM 
    (SELECT id FROM product_attributes WHERE UPPER(attribute_type) = 'CATEGORY' AND value = p_product AND status = 'active' LIMIT 1) cat
  CROSS JOIN 
    (SELECT id FROM product_attributes WHERE UPPER(attribute_type) = 'PRODUCT_TYPE' AND value = p_product_type AND status = 'active' LIMIT 1) pt
  CROSS JOIN 
    (SELECT id FROM product_attributes WHERE UPPER(attribute_type) = 'SIZE' AND value = p_size AND status = 'active' LIMIT 1) sz;
END;
$$;

-- =====================================================
-- 2. Calculate Production Cost for Single Item Function
-- =====================================================

CREATE OR REPLACE FUNCTION calculate_item_production_cost(
  p_category_id UUID,
  p_product_type_id UUID,
  p_size_id UUID,
  p_quantity INTEGER DEFAULT 1,
  p_nos INTEGER DEFAULT 1
)
RETURNS TABLE(
  base_cost NUMERIC,
  additional_cost NUMERIC,
  total_cost NUMERIC,
  calculation_success BOOLEAN
)
LANGUAGE plpgsql
AS $$
DECLARE
  v_base_cost NUMERIC := 0;
  v_additional_cost NUMERIC := 0;
  v_total_cost NUMERIC := 0;
  v_component_record RECORD;
  v_tier_type TEXT;
  v_component_value NUMERIC;
BEGIN
  -- Initialize costs
  v_base_cost := 0;
  v_additional_cost := 0;

  -- Get all component values for this product combination
  FOR v_component_record IN
    SELECT 
      pcv.value,
      pcv.tier_metadata,
      pcc.name as component_name,
      pcc.category as component_category
    FROM production_cost_component_values pcv
    INNER JOIN production_cost_components pcc ON pcv.component_id = pcc.id
    WHERE pcv.product_category_id = p_category_id
      AND pcv.product_type_id = p_product_type_id
      AND pcv.size_id = p_size_id
      AND pcv.is_current = true
      AND pcc.status = 'active'
  LOOP
    v_component_value := v_component_record.value;
    
    -- Check if this component has tier metadata (additional cost)
    IF v_component_record.tier_metadata IS NOT NULL AND 
       jsonb_typeof(v_component_record.tier_metadata) = 'object' AND
       v_component_record.tier_metadata ? 'tier' THEN
      
      -- This is an additional cost with tier logic
      v_tier_type := v_component_record.tier_metadata->>'tier';
      
      CASE v_tier_type
        WHEN 'per_unit' THEN
          v_additional_cost := v_additional_cost + (v_component_value * p_quantity);
        WHEN 'per_order' THEN
          v_additional_cost := v_additional_cost + v_component_value;
        WHEN 'distributed_by_nos' THEN
          -- Distribute across number of sheets
          IF p_nos > 0 THEN
            v_additional_cost := v_additional_cost + v_component_value;
          ELSE
            v_additional_cost := v_additional_cost + v_component_value;
          END IF;
        WHEN 'distributed_by_qty' THEN
          -- Distribute across quantity
          IF p_quantity > 0 THEN
            v_additional_cost := v_additional_cost + v_component_value;
          ELSE
            v_additional_cost := v_additional_cost + v_component_value;
          END IF;
        ELSE
          -- Default: treat as fixed additional cost
          v_additional_cost := v_additional_cost + v_component_value;
      END CASE;
    ELSE
      -- This is a base cost (no tier metadata)
      v_base_cost := v_base_cost + v_component_value;
    END IF;
  END LOOP;

  -- Calculate total cost
  v_total_cost := (v_base_cost * p_quantity) + v_additional_cost;

  RETURN QUERY
  SELECT 
    v_base_cost,
    v_additional_cost,
    v_total_cost,
    true as calculation_success;
END;
$$;

-- =====================================================
-- 3. Main Order Production Cost Calculation Function
-- =====================================================

CREATE OR REPLACE FUNCTION calculate_order_production_costs(
  p_order_ids UUID[] DEFAULT NULL,
  p_date_from DATE DEFAULT NULL,
  p_date_to DATE DEFAULT NULL,
  p_force_recalculate BOOLEAN DEFAULT FALSE,
  p_dry_run BOOLEAN DEFAULT FALSE
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  v_processed_count INTEGER := 0;
  v_updated_count INTEGER := 0;
  v_error_count INTEGER := 0;
  v_total_production_cost NUMERIC := 0;
  v_errors JSONB := '[]'::JSONB;
  v_item_record RECORD;
  v_mapping_result RECORD;
  v_cost_result RECORD;
  v_calculated_cost NUMERIC;
  v_new_profit_amount NUMERIC;
  v_new_profit_margin NUMERIC;
  v_error_message TEXT;
BEGIN
  -- Build the query to get order items based on filters
  FOR v_item_record IN
    SELECT 
      oi.item_id,
      oi.order_id,
      oi.product,
      oi.product_type,
      oi.size,
      oi.qty,
      oi.nos,
      oi.discount_amount,
      oi.production_cost as current_production_cost,
      o.created_at
    FROM order_items oi
    INNER JOIN orders o ON oi.order_id = o.order_id
    WHERE 
      -- Filter by order IDs if specified
      (p_order_ids IS NULL OR oi.order_id = ANY(p_order_ids))
      -- Filter by date range if specified
      AND (p_date_from IS NULL OR o.created_at::DATE >= p_date_from)
      AND (p_date_to IS NULL OR o.created_at::DATE <= p_date_to)
      -- Only process items with valid data
      AND oi.product IS NOT NULL 
      AND oi.product != ''
      AND oi.product_type IS NOT NULL
      AND oi.size IS NOT NULL
      -- Skip if already calculated (unless force recalculate)
      AND (p_force_recalculate = TRUE OR oi.production_cost = 0 OR oi.production_cost IS NULL)
  LOOP
    v_processed_count := v_processed_count + 1;
    v_error_message := NULL;
    v_calculated_cost := 0;

    BEGIN
      -- Step 1: Map order item attributes to IDs
      SELECT * INTO v_mapping_result
      FROM map_order_item_to_attributes(
        v_item_record.product,
        v_item_record.product_type,
        v_item_record.size
      );

      IF NOT v_mapping_result.mapping_success THEN
        v_error_message := 'Failed to map attributes to IDs';
        RAISE EXCEPTION '%', v_error_message;
      END IF;

      -- Step 2: Calculate production cost
      SELECT * INTO v_cost_result
      FROM calculate_item_production_cost(
        v_mapping_result.category_id,
        v_mapping_result.product_type_id,
        v_mapping_result.size_id,
        COALESCE(v_item_record.qty, 1),
        COALESCE(v_item_record.nos, 1)
      );

      IF NOT v_cost_result.calculation_success THEN
        v_error_message := 'Production cost calculation failed';
        RAISE EXCEPTION '%', v_error_message;
      END IF;

      v_calculated_cost := v_cost_result.total_cost;

      -- Step 3: Calculate new profit metrics
      v_new_profit_amount := COALESCE(v_item_record.discount_amount, 0) - v_calculated_cost;
      
      IF COALESCE(v_item_record.discount_amount, 0) > 0 THEN
        v_new_profit_margin := (v_new_profit_amount / v_item_record.discount_amount) * 100;
      ELSE
        v_new_profit_margin := 0;
      END IF;

      -- Step 4: Update the order item (if not dry run)
      IF NOT p_dry_run THEN
        UPDATE order_items
        SET 
          production_cost = v_calculated_cost,
          profit_amount = v_new_profit_amount,
          profit_margin_percentage = v_new_profit_margin
        WHERE item_id = v_item_record.item_id;
      END IF;

      v_updated_count := v_updated_count + 1;
      v_total_production_cost := v_total_production_cost + v_calculated_cost;

    EXCEPTION WHEN OTHERS THEN
      v_error_count := v_error_count + 1;
      v_error_message := COALESCE(v_error_message, SQLERRM);
      
      -- Add error to error list
      v_errors := v_errors || jsonb_build_object(
        'item_id', v_item_record.item_id,
        'order_id', v_item_record.order_id,
        'product', v_item_record.product,
        'product_type', v_item_record.product_type,
        'size', v_item_record.size,
        'error', v_error_message
      );
    END;
  END LOOP;

  -- Return summary results
  RETURN jsonb_build_object(
    'processed_count', v_processed_count,
    'updated_count', v_updated_count,
    'error_count', v_error_count,
    'total_production_cost', v_total_production_cost,
    'average_production_cost', CASE 
      WHEN v_updated_count > 0 THEN v_total_production_cost / v_updated_count 
      ELSE 0 
    END,
    'dry_run', p_dry_run,
    'errors', v_errors,
    'summary', jsonb_build_object(
      'success_rate', CASE 
        WHEN v_processed_count > 0 THEN (v_updated_count::NUMERIC / v_processed_count) * 100
        ELSE 0
      END,
      'filters_applied', jsonb_build_object(
        'order_ids', p_order_ids,
        'date_from', p_date_from,
        'date_to', p_date_to,
        'force_recalculate', p_force_recalculate
      )
    )
  );
END;
$$;

-- =====================================================
-- 4. Helper Function: Get Production Cost Coverage Stats
-- =====================================================

CREATE OR REPLACE FUNCTION get_production_cost_coverage_stats()
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  v_total_items INTEGER;
  v_items_with_costs INTEGER;
  v_items_without_costs INTEGER;
  v_coverage_by_category JSONB;
BEGIN
  -- Get total order items count
  SELECT COUNT(*) INTO v_total_items
  FROM order_items
  WHERE product IS NOT NULL AND product != '';

  -- Get items with production costs
  SELECT COUNT(*) INTO v_items_with_costs
  FROM order_items
  WHERE product IS NOT NULL 
    AND product != ''
    AND production_cost > 0;

  -- Calculate items without costs
  v_items_without_costs := v_total_items - v_items_with_costs;

  -- Get coverage by category
  SELECT jsonb_object_agg(product, stats) INTO v_coverage_by_category
  FROM (
    SELECT 
      product,
      jsonb_build_object(
        'total_items', COUNT(*),
        'items_with_costs', COUNT(*) FILTER (WHERE production_cost > 0),
        'coverage_percentage', ROUND(
          (COUNT(*) FILTER (WHERE production_cost > 0)::NUMERIC / COUNT(*)) * 100, 2
        )
      ) as stats
    FROM order_items
    WHERE product IS NOT NULL AND product != ''
    GROUP BY product
  ) coverage_stats;

  RETURN jsonb_build_object(
    'total_items', v_total_items,
    'items_with_costs', v_items_with_costs,
    'items_without_costs', v_items_without_costs,
    'overall_coverage_percentage', CASE 
      WHEN v_total_items > 0 THEN ROUND((v_items_with_costs::NUMERIC / v_total_items) * 100, 2)
      ELSE 0
    END,
    'coverage_by_category', v_coverage_by_category
  );
END;
$$;

-- =====================================================
-- 5. Test Function for Photo Boards
-- =====================================================

CREATE OR REPLACE FUNCTION test_photo_boards_production_cost_calculation()
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  v_result JSONB;
BEGIN
  -- Test calculation for Photo Boards only with dry run
  SELECT calculate_order_production_costs(
    p_order_ids := NULL,
    p_date_from := NULL,
    p_date_to := NULL,
    p_force_recalculate := TRUE,
    p_dry_run := TRUE
  ) INTO v_result;

  -- Filter to only Photo Boards for testing
  RETURN v_result;
END;
$$;

-- Add helpful comments
COMMENT ON FUNCTION map_order_item_to_attributes IS 'Maps order item string attributes to product_attributes table IDs';
COMMENT ON FUNCTION calculate_item_production_cost IS 'Calculates production cost for a single item using tier-aware logic';
COMMENT ON FUNCTION calculate_order_production_costs IS 'Main function to calculate production costs for order items with filtering options';
COMMENT ON FUNCTION get_production_cost_coverage_stats IS 'Returns statistics about production cost coverage across orders';
COMMENT ON FUNCTION test_photo_boards_production_cost_calculation IS 'Test function for Photo Boards production cost calculation';