-- Add notes column to orders table to store general order notes
-- This allows us to use inline note storage instead of the separate notes table

ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS order_notes JSONB DEFAULT '[]'::jsonb;

-- Add comment to explain the column
COMMENT ON COLUMN orders.order_notes IS 'General notes about the order stored as JSONB array';

-- Example structure:
-- [
--   {
--     "content": "Customer requested express delivery",
--     "created_at": "2025-01-06T10:30:00Z",
--     "created_by": "<PERSON>"
--   }
-- ]