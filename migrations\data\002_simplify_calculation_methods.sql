-- Migration: Simplify Calculation Methods
-- Date: 2025-05-30
-- Purpose: Convert 'weighted' and 'custom_formula' templates to 'simple_sum'
-- Impact: 2 templates affected, 0 active applications

-- =============================================================================
-- BACKUP AND ROLLBACK PREPARATION
-- =============================================================================

-- Create backup table for rollback
CREATE TABLE IF NOT EXISTS calculation_templates_backup_20250530 AS 
SELECT * FROM calculation_templates 
WHERE calculation_method IN ('weighted', 'custom_formula');

-- Verify backup creation
DO $$
DECLARE
    backup_count INTEGER;
    original_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO backup_count FROM calculation_templates_backup_20250530;
    SELECT COUNT(*) INTO original_count FROM calculation_templates 
    WHERE calculation_method IN ('weighted', 'custom_formula');
    
    IF backup_count != original_count THEN
        RAISE EXCEPTION 'Backup verification failed: expected %, got %', original_count, backup_count;
    END IF;
    
    RAISE NOTICE 'Backup created successfully: % templates backed up', backup_count;
END $$;

-- =============================================================================
-- MIGRATION SCRIPT
-- =============================================================================

-- Update weighted templates to simple_sum
UPDATE calculation_templates 
SET 
    calculation_method = 'simple_sum',
    description = REPLACE(
        description, 
        'Applies weighted multipliers to selected components for precise cost calculation', 
        'Calculates cost by adding selected components together'
    ),
    updated_at = NOW()
WHERE calculation_method = 'weighted' 
AND status = 'active';

-- Update custom_formula templates to simple_sum  
UPDATE calculation_templates 
SET 
    calculation_method = 'simple_sum',
    description = REPLACE(
        description,
        'Uses a custom mathematical formula for advanced cost calculations',
        'Calculates cost by adding selected components together'
    ),
    updated_at = NOW()
WHERE calculation_method = 'custom_formula' 
AND status = 'active';

-- =============================================================================
-- VERIFICATION QUERIES
-- =============================================================================

-- Verify migration results
SELECT 
    'AFTER MIGRATION' as status,
    calculation_method,
    COUNT(*) as count
FROM calculation_templates 
GROUP BY calculation_method
ORDER BY calculation_method;

-- Show updated templates
SELECT 
    id,
    name,
    calculation_method,
    description,
    updated_at
FROM calculation_templates 
WHERE id IN (
    'e2ae6f0c-7dfa-45f6-a397-6c48e429fed9',  -- Basic rule test 2
    'e63b0a5d-7869-42ed-a699-cf9f3a24ad26'   -- Basic rule test 3
);

-- =============================================================================
-- ROLLBACK SCRIPT (if needed)
-- =============================================================================

/*
-- ROLLBACK: Restore from backup
UPDATE calculation_templates 
SET 
    calculation_method = backup.calculation_method,
    description = backup.description,
    updated_at = NOW()
FROM calculation_templates_backup_20250530 backup
WHERE calculation_templates.id = backup.id;

-- Verify rollback
SELECT 
    'AFTER ROLLBACK' as status,
    calculation_method,
    COUNT(*) as count
FROM calculation_templates 
GROUP BY calculation_method
ORDER BY calculation_method;
*/

-- =============================================================================
-- CLEANUP PHASE (run separately after validation)
-- =============================================================================

/*
-- Phase 2: Clean up unused fields (run this after validating migration)
-- Note: Keep these fields for now as they might be referenced in application code

ALTER TABLE calculation_templates 
DROP COLUMN IF EXISTS component_weights,
DROP COLUMN IF EXISTS formula,
DROP COLUMN IF EXISTS formula_builder;

-- Drop backup table after successful migration and validation
DROP TABLE IF EXISTS calculation_templates_backup_20250530;
*/