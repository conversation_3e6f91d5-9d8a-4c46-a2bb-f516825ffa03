import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { usePermissions } from '../hooks/permissions';
import { updateProfile } from '../services/profileService';
import type { ProfileData } from '../types/profiles.types';
import { PERMISSIONS } from '../types/permissions.types';
import { logger } from '../utils/logger';
import { useToast } from '../hooks/use-toast';
import AdminSettings from './Settings/AdminSettings';
import {
  UserIcon,
  ShieldIcon,
  BellIcon,
  KeyIcon,
  PaletteIcon,
  DatabaseIcon,
  CalendarIcon,
  MailIcon,
  BuildingIcon,
} from 'lucide-react';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Ava<PERSON>, AvatarFallback, AvatarImage } from '../components/ui/avatar';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Separator } from '../components/ui/separator';
import { Badge } from '../components/ui/badge';
import { StatusBadge } from '../components/ui/status-badge';
import { MetricBadge } from '../components/ui/metric-badge';

const settingsLogger = logger.withPrefix('SettingsPage');

const Settings: React.FC = () => {
  const { user, profile, authorizedUser, profileLoading, refreshProfile } = useAuth();
  const { checkPermission } = usePermissions();
  const { toast } = useToast();

  // Profile form state
  const [formData, setFormData] = useState<Pick<ProfileData, 'full_name' | 'avatar_url'>>({
    full_name: '',
    avatar_url: '',
  });

  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');

  // Update form data when profile changes
  useEffect(() => {
    if (profile) {
      setFormData({
        full_name: profile.full_name || '',
        avatar_url: profile.avatar_url || '',
      });
    }
  }, [profile]);

  // Generate initials for avatar fallback
  const getInitials = () => {
    if (profile?.full_name) {
      return profile.full_name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
    }
    if (user?.email) {
      return user.email.charAt(0).toUpperCase()
    }
    return 'U'
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleProfileSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to update your profile",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsSaving(true);
      settingsLogger.debug('Updating profile', formData);

      const updatedProfile = await updateProfile(user.id, {
        full_name: formData.full_name,
        avatar_url: formData.avatar_url,
      });

      if (updatedProfile) {
        settingsLogger.debug('Profile updated successfully');
        toast({
          title: "Profile Updated",
          description: "Your profile has been updated successfully",
          duration: 3000
        });
        refreshProfile();
      } else {
        throw new Error('Failed to update profile');
      }
    } catch (error) {
      settingsLogger.error('Error updating profile:', error);
      toast({
        title: "Update Failed",
        description: "An error occurred while updating your profile",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (profileLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading settings...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">
          <p className="text-xl text-gray-700">You must be logged in to access settings</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight mb-0">Settings</h1>
        <p className="text-muted-foreground">
          Manage your account settings and preferences.
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className={`grid w-full ${checkPermission('admin.users') ? 'grid-cols-4' : 'grid-cols-3'}`}>
          <TabsTrigger value="profile">Profile & Account</TabsTrigger>
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          {checkPermission('admin.users') && (
            <TabsTrigger value="admin">Admin</TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="profile" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UserIcon className="h-5 w-5" />
                Profile Information
              </CardTitle>
              <CardDescription>
                Update your profile information, avatar, and view account details.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Avatar Section */}
              <div className="flex items-center gap-6">
                <Avatar className="h-24 w-24">
                  <AvatarImage src={formData.avatar_url || profile?.avatar_url} alt={profile?.full_name || 'User'} />
                  <AvatarFallback className="text-lg">{getInitials()}</AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="text-lg font-semibold mb-0">{profile?.full_name || 'No name set'}</h3>
                  <p className="text-muted-foreground mb-0">{user.email}</p>
                  <div className="flex items-center gap-2 mt-2">
                    <span className="text-sm text-muted-foreground">Role:</span>
                    <StatusBadge 
                      status={(profile?.role || authorizedUser?.role || 'default').toLowerCase() as any}
                      className="capitalize"
                    >
                      {profile?.role || authorizedUser?.role || 'User'}
                    </StatusBadge>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Profile Form */}
              <form onSubmit={handleProfileSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="full_name">Full Name</Label>
                    <Input
                      id="full_name"
                      name="full_name"
                      value={formData.full_name}
                      onChange={handleInputChange}
                      placeholder="Enter your full name"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="avatar_url">Avatar URL</Label>
                    <Input
                      id="avatar_url"
                      name="avatar_url"
                      type="url"
                      value={formData.avatar_url}
                      onChange={handleInputChange}
                      placeholder="https://example.com/avatar.jpg"
                    />
                  </div>
                </div>

                {formData.avatar_url && (
                  <div className="space-y-2">
                    <Label>Avatar Preview</Label>
                    <Avatar className="h-16 w-16">
                      <AvatarImage src={formData.avatar_url} alt="Avatar preview" />
                      <AvatarFallback>Preview</AvatarFallback>
                    </Avatar>
                  </div>
                )}

                <Button type="submit" disabled={isSaving} variant="default" className="bg-gray-800 hover:bg-gray-900 text-white">
                  {isSaving ? 'Saving...' : 'Save Changes'}
                </Button>
              </form>

              <Separator />

              {/* Account Information Section */}
              <div>
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                  <ShieldIcon className="h-5 w-5" />
                  Account Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <MailIcon className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium mb-0">Email Address</p>
                        <p className="text-sm text-muted-foreground mb-0">{user.email}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      <ShieldIcon className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium mb-0">Role</p>
                        <StatusBadge 
                          status={(profile?.role || authorizedUser?.role || 'default').toLowerCase() as any}
                          className="mt-1 capitalize"
                        >
                          {profile?.role || authorizedUser?.role || 'User'}
                        </StatusBadge>
                      </div>
                    </div>
                    
                    {(profile?.department || authorizedUser?.department) && (
                      <div className="flex items-center gap-3">
                        <BuildingIcon className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium mb-0">Department</p>
                          <MetricBadge 
                            value={profile?.department || authorizedUser?.department || ''}
                            variant="info"
                            className="mt-1"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div className="space-y-4">
                    {profile?.created_at && (
                      <div className="flex items-center gap-3">
                        <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium mb-0">Member Since</p>
                          <MetricBadge 
                            value={new Date(profile.created_at).toLocaleDateString()}
                            variant="success"
                            className="mt-1"
                          />
                        </div>
                      </div>
                    )}
                    
                    <div className="flex items-center gap-3">
                      <DatabaseIcon className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium mb-0">Account ID</p>
                        <MetricBadge 
                          value={`${user.id.slice(0, 8)}...`}
                          variant="neutral"
                          className="mt-1 font-mono text-xs"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>


        <TabsContent value="preferences" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PaletteIcon className="h-5 w-5" />
                Preferences
              </CardTitle>
              <CardDescription>
                Customize your application preferences and settings.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Theme</Label>
                    <p className="text-sm text-muted-foreground">
                      Choose your preferred color scheme
                    </p>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Coming soon
                  </div>
                </div>
                
                <Separator />
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Language</Label>
                    <p className="text-sm text-muted-foreground">
                      Select your preferred language
                    </p>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    English
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <KeyIcon className="h-5 w-5" />
                Security & Privacy
              </CardTitle>
              <CardDescription>
                Manage your security settings and privacy preferences.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Password</Label>
                    <p className="text-sm text-muted-foreground">
                      Change your account password
                    </p>
                  </div>
                  <Button variant="outline" size="sm" disabled className="border-gray-400 text-gray-600 hover:bg-gray-100">
                    Change Password
                  </Button>
                </div>
                
                <Separator />
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Two-Factor Authentication</Label>
                    <p className="text-sm text-muted-foreground">
                      Add an extra layer of security to your account
                    </p>
                  </div>
                  <Button variant="outline" size="sm" disabled className="border-gray-400 text-gray-600 hover:bg-gray-100">
                    Enable 2FA
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {checkPermission('admin.users') && (
          <TabsContent value="admin" className="space-y-6">
            <AdminSettings />
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
};

export default Settings;