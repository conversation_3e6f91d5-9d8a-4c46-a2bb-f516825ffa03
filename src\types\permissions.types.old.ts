/**
 * TypeScript type definitions for the permissions system
 * 
 * These types correspond to the database schema created in:
 * - migrations/010_create_permissions_system.sql
 * - migrations/011_seed_permissions_data_fixed.sql
 * 
 * Following industry standards from our code quality analysis
 */

// ============================================================================
// CORE PERMISSION TYPES
// ============================================================================

export interface Permission {
  readonly id: number;
  readonly key: string;
  readonly name: string;
  readonly description: string | null;
  readonly category: string;
  readonly isActive: boolean;
  readonly requiresPermissions: readonly string[];
  readonly createdAt: string;
  readonly updatedAt: string;
}

export interface Role {
  readonly id: number;
  readonly name: string;
  readonly displayName: string;
  readonly description: string | null;
  readonly permissions: readonly string[];
  readonly isActive: boolean;
  readonly isSystemRole: boolean;
  readonly createdAt: string;
  readonly updatedAt: string;
}

// ============================================================================
// PERMISSION CHECKING TYPES
// ============================================================================

export type PermissionCheckResult = {
  readonly hasPermission: boolean;
  readonly reason?: string;
};

export type BulkPermissionCheckResult = {
  [permissionKey: string]: boolean;
};

// ============================================================================
// PERMISSION CATEGORIES (From Database Constraints)
// ============================================================================

export type PermissionCategory = 
  | 'Page Access'
  | 'Order Component Controls'
  | 'Financial Controls'
  | 'Operational Controls'
  | 'Data Security'
  | 'Administrative Controls'
  | 'System Administration'
  | 'Analytics Access';

// ============================================================================
// PERMISSION REGISTRY (23 Total Permissions)
// ============================================================================

// Page Access Permissions (10 permissions)
export const PAGE_PERMISSIONS = {
  ORDERS_ACCESS: 'pages.orders_access',
  PRODUCTS_ACCESS: 'pages.products_access',
  CLIENTS_ACCESS: 'pages.clients_access',
  PRODUCTION_COST_ACCESS: 'pages.production_cost_access',
  ANALYTICS_OVERVIEW_ACCESS: 'pages.analytics_overview_access',
  ANALYTICS_SALES_ACCESS: 'pages.analytics_sales_access',
  ANALYTICS_PRODUCTION_ACCESS: 'pages.analytics_production_access',
  ANALYTICS_GENERAL_ACCESS: 'pages.analytics_general_access',
  SETTINGS_ACCESS: 'pages.settings_access',
  USER_MANAGEMENT_ACCESS: 'pages.user_management_access',
} as const;

// Strategic Feature Permissions (13 permissions)
export const FEATURE_PERMISSIONS = {
  ORDERS_CREATE: 'orders.create',
  ORDERS_GENERAL_INFO_EDIT: 'orders.general_info_edit',
  ORDERS_ITEMS_EDIT: 'orders.items_edit',
  ORDERS_PAYMENTS_MANAGE: 'orders.payments_manage',
  ORDERS_DELETE: 'orders.delete',
  PRODUCTS_PRICING_EDIT: 'products.pricing_edit',
  PRODUCTS_DELETE: 'products.delete',
  CLIENTS_CREATE: 'clients.create',
  CLIENTS_DELETE: 'clients.delete',
  ANALYTICS_EXPORT: 'analytics.export',
  ADMIN_USERS_CREATE: 'admin.users_create',
  ADMIN_PERMISSIONS_ASSIGN: 'admin.permissions_assign',
  SYSTEM_FULL_ACCESS: 'system.full_access',
} as const;

// All Permissions Combined
export const ALL_PERMISSIONS = {
  ...PAGE_PERMISSIONS,
  ...FEATURE_PERMISSIONS,
} as const;

// Permission key type (all possible permission keys)
export type PermissionKey = typeof ALL_PERMISSIONS[keyof typeof ALL_PERMISSIONS];

// ============================================================================
// ROLE REGISTRY (5 Total Roles)
// ============================================================================

export const ROLES = {
  VIEWER: 'viewer',
  ORDER_MANAGER: 'order_manager',
  PRODUCT_MANAGER: 'product_manager',
  SUPERVISOR: 'supervisor',
  ADMIN: 'admin',
} as const;

export type RoleName = typeof ROLES[keyof typeof ROLES];

// ============================================================================
// SERVICE INTERFACES
// ============================================================================

export interface PermissionsServiceInterface {
  checkPermission(permission: PermissionKey): Promise<PermissionCheckResult>;
  checkMultiplePermissions(permissions: PermissionKey[]): Promise<BulkPermissionCheckResult>;
  getAllPermissions(): Promise<Permission[]>;
  validatePermissionKey(permission: string): Promise<boolean>;
}

// ============================================================================
// HOOK INTERFACES
// ============================================================================

export interface UsePermissionsResult {
  readonly permissions: readonly string[];
  readonly loading: boolean;
  readonly error: string | null;
  readonly checkPermission: (permission: PermissionKey) => boolean;
  readonly checkMultiplePermissions: (permissions: PermissionKey[]) => BulkPermissionCheckResult;
  readonly hasFullAccess: boolean;
  readonly refreshPermissions: () => Promise<void>;
}

// ============================================================================
// COMPONENT PROPS TYPES
// ============================================================================

export interface PermissionGuardProps {
  readonly permission: PermissionKey;
  readonly children: React.ReactNode;
  readonly fallback?: React.ReactNode;
  readonly loading?: React.ReactNode;
}

export interface PermissionWrapperProps {
  readonly permissions: PermissionKey | readonly PermissionKey[];
  readonly requireAll?: boolean;
  readonly children: React.ReactNode;
  readonly fallback?: React.ReactNode;
}

// ============================================================================
// ERROR TYPES
// ============================================================================

export enum PermissionErrorCode {
  PERMISSION_DENIED = 'permission-denied',
  INVALID_PERMISSION = 'invalid-permission',
  SYSTEM_ERROR = 'system-error',
  USER_NOT_FOUND = 'user-not-found'
}

export class PermissionError extends Error {
  readonly code: PermissionErrorCode;
  readonly details?: Record<string, unknown>;

  constructor(
    code: PermissionErrorCode,
    message?: string,
    details?: Record<string, unknown>
  ) {
    super(message || `Permission error: ${code}`);
    this.name = 'PermissionError';
    this.code = code;
    this.details = details;
  }
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

// Type guard for permission keys
export const isValidPermissionKey = (key: string): key is PermissionKey => {
  return Object.values(ALL_PERMISSIONS).includes(key as PermissionKey);
};

// Type for permission validation
export type PermissionValidationResult = {
  readonly isValid: boolean;
  readonly permission?: PermissionKey;
  readonly error?: string;
};

// Development mode type (for open access mode)
export type DevelopmentModeConfig = {
  readonly openAccessMode: boolean;
  readonly bypassPermissions: boolean;
  readonly logPermissionChecks: boolean;
};