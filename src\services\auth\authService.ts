import { supabase } from '../../lib/supabase';
import type { Session, User, AuthError } from '@supabase/supabase-js';

export interface AuthResult {
  user: User | null;
  session: Session | null;
  error?: AuthError | null;
}

/**
 * Core Authentication Service
 * Handles Supabase authentication operations
 * Following CLAUDE.md guidelines - focused, under 250 lines
 */
export class AuthService {
  /**
   * Sign up new user with email and password
   */
  static async signUp(email: string, password: string, metadata?: Record<string, any>): Promise<AuthResult> {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata || {}
      }
    });

    if (error) {
      throw new Error(`Sign up failed: ${error.message}`);
    }

    return {
      user: data.user,
      session: data.session,
      error: null
    };
  }

  /**
   * Sign in with email and password (for admin users)
   */
  static async signInWithPassword(email: string, password: string): Promise<AuthResult> {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) {
      throw new Error(`Password sign-in failed: ${error.message}`);
    }

    return {
      user: data.user,
      session: data.session,
      error: null
    };
  }

  /**
   * Sign in with email (for email verification flow)
   */
  static async signInWithEmail(email: string): Promise<void> {
    const { error } = await supabase.auth.signInWithOtp({
      email,
      options: {
        shouldCreateUser: true,
      }
    });

    if (error) {
      throw new Error(`Email sign-in failed: ${error.message}`);
    }
  }

  /**
   * Verify email OTP code (for magic link flows)
   */
  static async verifyEmailOTP(email: string, token: string): Promise<AuthResult> {
    const { data, error } = await supabase.auth.verifyOtp({
      email,
      token,
      type: 'email'
    });

    if (error) {
      throw new Error(`OTP verification failed: ${error.message}`);
    }

    return {
      user: data.user,
      session: data.session,
      error: null
    };
  }

  /**
   * Verify signup confirmation code (for password-based signups)
   */
  static async verifySignupOTP(email: string, token: string): Promise<AuthResult> {
    const { data, error } = await supabase.auth.verifyOtp({
      email,
      token,
      type: 'signup'
    });

    if (error) {
      throw new Error(`Signup verification failed: ${error.message}`);
    }

    return {
      user: data.user,
      session: data.session,
      error: null
    };
  }

  /**
   * Sign out current user
   */
  static async signOut(): Promise<void> {
    const { error } = await supabase.auth.signOut();
    if (error) {
      throw new Error(`Sign out failed: ${error.message}`);
    }
  }

  /**
   * Get current session
   */
  static async getCurrentSession(): Promise<Session | null> {
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('Error getting session:', error);
      return null;
    }
    
    return session;
  }

  /**
   * Refresh current session
   */
  static async refreshSession(): Promise<Session | null> {
    const { data: { session }, error } = await supabase.auth.refreshSession();
    
    if (error) {
      console.error('Error refreshing session:', error);
      return null;
    }
    
    return session;
  }

  /**
   * Get current user
   */
  static async getCurrentUser(): Promise<User | null> {
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.error('Error getting user:', error);
      return null;
    }
    
    return user;
  }

  /**
   * Listen to auth state changes
   */
  static onAuthStateChange(callback: (event: string, session: Session | null) => void) {
    return supabase.auth.onAuthStateChange(callback);
  }
}