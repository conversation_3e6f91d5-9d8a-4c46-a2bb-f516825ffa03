/**
 * AuthPerformanceDebugger Component
 * Development tool to monitor auth performance improvements
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { authPerformanceMonitor } from '../../utils/authPerformanceMonitor';

export const AuthPerformanceDebugger: React.FC = () => {
  const [summary, setSummary] = useState<any>(null);
  const [isVisible, setIsVisible] = useState(false);

  const refreshSummary = () => {
    setSummary(authPerformanceMonitor.getSummary());
  };

  const clearMetrics = () => {
    authPerformanceMonitor.clear();
    setSummary(null);
  };

  useEffect(() => {
    // Auto-refresh summary every 10 seconds when visible
    if (isVisible) {
      const interval = setInterval(refreshSummary, 10000);
      return () => clearInterval(interval);
    }
  }, [isVisible]);

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            setIsVisible(true);
            refreshSummary();
          }}
          className="bg-background/80 backdrop-blur"
        >
          📊 Auth Performance
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96">
      <Card className="bg-background/95 backdrop-blur border-2">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium">Auth Performance Monitor</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsVisible(false)}
              className="h-6 w-6 p-0"
            >
              ✕
            </Button>
          </div>
        </CardHeader>
        <CardContent className="pt-0 space-y-3">
          {summary ? (
            <>
              {/* Overall Performance */}
              <div className="flex justify-between items-center">
                <span className="text-xs text-muted-foreground">Cache Hit Rate:</span>
                <Badge variant={summary.cacheHitRate > 70 ? 'default' : 'secondary'}>
                  {summary.cacheHitRate.toFixed(1)}%
                </Badge>
              </div>
              
              {summary.performanceGain.percentImprovement > 0 && (
                <div className="flex justify-between items-center">
                  <span className="text-xs text-muted-foreground">Performance Gain:</span>
                  <Badge variant="default" className="bg-green-600">
                    {summary.performanceGain.percentImprovement.toFixed(1)}% faster
                  </Badge>
                </div>
              )}

              {/* Operation Details */}
              <div className="space-y-2">
                <span className="text-xs font-medium">Operations ({summary.totalOperations}):</span>
                <div className="space-y-1 max-h-32 overflow-y-auto">
                  {summary.summary.map((op: any, idx: number) => (
                    <div key={idx} className="flex justify-between items-center text-xs">
                      <span className={op.operation.includes('_cached') ? 'text-green-600' : 'text-blue-600'}>
                        {op.operation.replace('_cached', ' (cached)')}
                      </span>
                      <div className="flex gap-1">
                        <Badge variant="outline" className="text-xs px-1 py-0">
                          {op.avgTime}ms
                        </Badge>
                        <Badge variant="secondary" className="text-xs px-1 py-0">
                          {op.count}x
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={refreshSummary} className="text-xs h-7">
                  Refresh
                </Button>
                <Button variant="outline" size="sm" onClick={clearMetrics} className="text-xs h-7">
                  Clear
                </Button>
              </div>
            </>
          ) : (
            <div className="text-center py-4">
              <p className="text-xs text-muted-foreground mb-2">No performance data yet</p>
              <Button variant="outline" size="sm" onClick={refreshSummary} className="text-xs">
                Start Monitoring
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};