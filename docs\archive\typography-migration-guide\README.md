# Typography System Migration Guide

This guide helps you migrate from the legacy typography system to the new 2024 Typography Design System.

## Migration Overview

The new typography system provides:
- ✅ Enhanced semantic structure
- ✅ Responsive and fluid typography
- ✅ Better accessibility compliance
- ✅ Performance optimizations
- ✅ Modern design patterns

## Breaking Changes

### 1. Import Paths
```tsx
// Old
import { typography } from '../lib/typography';

// New - Enhanced system
import { typography } from '../lib/typography-enhanced';
// Or specific components
import { Typography, Heading, Text } from '../components/ui/typography-enhanced';
```

### 2. Nested Object Structure
```tsx
// Old
<h1 className={typography.h1}>Title</h1>

// New - Nested structure for better organization
<h1 className={typography.heading.h1}>Title</h1>
<h1 className={typography.display.hero}>Hero Title</h1>
<p className={typography.body.default}>Body text</p>
```

### 3. Component Props
```tsx
// Old
<Typography variant="h1">Title</Typography>

// New - More specific variants
<Typography variant="heading.h1">Title</Typography>
<Heading level={1}>Title</Heading>
<Display variant="hero">Hero Title</Display>
```

## Migration Strategies

### Strategy 1: Gradual Migration (Recommended)

**Phase 1: Install New System**
```bash
# The new system is already included
# Import both systems during transition
```

**Phase 2: Update Critical Pages**
```tsx
// Update your most important pages first
import { typography as newTypography } from '../lib/typography-enhanced';
import { Typography } from '../components/ui/typography-enhanced';

function LandingPage() {
  return (
    <div>
      {/* Use new system for new content */}
      <Typography variant="display.hero">Welcome</Typography>
      
      {/* Keep old system for existing content temporarily */}
      <h2 className={typography.h2}>Existing Content</h2>
    </div>
  );
}
```

**Phase 3: Replace Common Patterns**
```tsx
// Create migration helpers
const migrationMap = {
  'h1': 'heading.h1',
  'h2': 'heading.h2',
  'h3': 'heading.h3',
  'body': 'body.default',
  'bodyLarge': 'body.large',
  'bodySmall': 'body.small',
};

// Use search and replace for bulk updates
```

**Phase 4: Remove Legacy System**
```tsx
// After all components are migrated
// Remove old imports and update all references
```

### Strategy 2: Component-by-Component

**Step 1: Create Wrapper Components**
```tsx
// Create transitional components
export const LegacyHeading = ({ level, children, ...props }) => {
  return (
    <Heading level={level} {...props}>
      {children}
    </Heading>
  );
};

export const LegacyText = ({ variant = 'default', children, ...props }) => {
  const newVariant = variant === 'body' ? 'default' : variant;
  return (
    <Text variant={newVariant} {...props}>
      {children}
    </Text>
  );
};
```

**Step 2: Update Components Gradually**
```tsx
// Update one component at a time
function ProductCard() {
  return (
    <div>
      {/* Updated */}
      <Heading level={3} className="mb-2">
        Product Title
      </Heading>
      
      {/* Updated */}
      <Text variant="default" className="mb-4">
        Product description with better typography
      </Text>
      
      {/* Legacy - to be updated */}
      <span className={typography.small}>
        Legacy price text
      </span>
    </div>
  );
}
```

## Common Migration Patterns

### Headings
```tsx
// Before
<h1 className={typography.h1}>Page Title</h1>
<h2 className={typography.h2}>Section Title</h2>

// After
<Heading level={1}>Page Title</Heading>
<Heading level={2}>Section Title</Heading>

// Or with className
<h1 className={typography.heading.h1}>Page Title</h1>
<h2 className={typography.heading.h2}>Section Title</h2>
```

### Body Text
```tsx
// Before
<p className={typography.body}>Regular text</p>
<p className={typography.bodyLarge}>Large text</p>
<p className={typography.bodySmall}>Small text</p>

// After
<Text variant="default">Regular text</Text>
<Text variant="large">Large text</Text>
<Text variant="small">Small text</Text>

// Or with className
<p className={typography.body.default}>Regular text</p>
<p className={typography.body.large}>Large text</p>
<p className={typography.body.small}>Small text</p>
```

### Display Text
```tsx
// Before
<h1 className={typography.display}>Hero Title</h1>

// After
<Display variant="hero">Hero Title</Display>

// Or with className
<h1 className={typography.display.hero}>Hero Title</h1>
```

### UI Elements
```tsx
// Before
<label className={typography.label}>Form Label</label>
<span className={typography.caption}>Caption text</span>
<button className={typography.button}>Button text</button>

// After
<Label>Form Label</Label>
<UI variant="caption">Caption text</UI>
<UI variant="button">Button text</UI>

// Or with className
<label className={typography.ui.label.default}>Form Label</label>
<span className={typography.ui.caption.default}>Caption text</span>
<button className={typography.ui.button.default}>Button text</button>
```

### Interactive Elements
```tsx
// Before
<a className={typography.link}>Regular Link</a>
<a className={typography.linkSubtle}>Subtle Link</a>

// After
<Interactive variant="link" style="default">Regular Link</Interactive>
<Interactive variant="link" style="subtle">Subtle Link</Interactive>

// Or with className
<a className={typography.interactive.link.default}>Regular Link</a>
<a className={typography.interactive.link.subtle}>Subtle Link</a>
```

## Font-Specific Migrations

### Inter (Default UI)
```tsx
// Before
<span className={typography.inter.body}>Interface text</span>

// After
<Text font="inter">Interface text</Text>

// Or with className
<span className={typography.fonts.inter.body}>Interface text</span>
```

### Geist (Modern)
```tsx
// Before
<h1 className={typography.geist.heading}>Modern heading</h1>

// After
<Heading level={1} font="geist">Modern heading</Heading>

// Or with className
<h1 className={typography.fonts.geist.heading}>Modern heading</h1>
```

### Poppins (Friendly)
```tsx
// Before
<h2 className={typography.poppins.heading}>Friendly heading</h2>

// After
<Heading level={2} font="poppins">Friendly heading</Heading>

// Or with className
<h2 className={typography.fonts.poppins.heading}>Friendly heading</h2>
```

## Advanced Features Migration

### Responsive Typography
```tsx
// Before - Manual responsive classes
<h1 className="text-2xl sm:text-3xl lg:text-4xl">Responsive Title</h1>

// After - Built-in responsive system
<Typography 
  variant="heading.h1"
  responsive={{
    mobile: 'text-2xl',
    tablet: 'text-3xl',
    desktop: 'text-4xl'
  }}
>
  Responsive Title
</Typography>

// Or use fluid typography
<Typography variant="heading.h1" fluid>
  Fluid Responsive Title
</Typography>
```

### Theme-Aware Typography
```tsx
// Before - Manual dark mode classes
<p className="text-gray-900 dark:text-white">Theme text</p>

// After - Built-in theme support
<Text variant="default">Theme text</Text>

// Colors are automatically theme-aware
```

## Utility Migrations

### Typography Variants
```tsx
// Before
<h1 className={typographyVariants.pageTitle}>Page Title</h1>
<span className={typographyVariants.badge}>Badge</span>

// After - Enhanced variants
<h1 className={typographyVariants.pageTitle}>Page Title</h1>
<Status variant="badge">Badge</Status>

// Most variants are compatible, some have been enhanced
```

### Custom Combinations
```tsx
// Before
<span className={cn(typography.body, 'text-red-500')}>Custom text</span>

// After
<Text variant="default" className="text-red-500">Custom text</Text>

// Or use utility
<span className={typographyUtils.combine(typography.body.default, 'text-red-500')}>
  Custom text
</span>
```

## Testing Your Migration

### Visual Regression Testing
```tsx
// Create comparison pages
function TypographyComparison() {
  return (
    <div className="grid grid-cols-2 gap-8">
      <div>
        <h2>Legacy System</h2>
        <h1 className={legacyTypography.h1}>Title</h1>
        <p className={legacyTypography.body}>Body text</p>
      </div>
      
      <div>
        <h2>New System</h2>
        <h1 className={typography.heading.h1}>Title</h1>
        <p className={typography.body.default}>Body text</p>
      </div>
    </div>
  );
}
```

### Accessibility Testing
```tsx
// Ensure WCAG compliance is maintained
import { accessibilityHelpers } from '../lib/typography-enhanced';

function AccessibleComponent() {
  return (
    <button className={cn(
      typography.ui.button.default,
      accessibilityHelpers.focusVisible
    )}>
      Accessible Button
    </button>
  );
}
```

### Performance Testing
```tsx
// Monitor font loading performance
import { performanceHelpers } from '../lib/typography-enhanced';

// Apply font display optimizations
<style>{performanceHelpers.fontDisplay.swap}</style>
```

## Troubleshooting

### Common Issues

**1. Missing Variants**
```tsx
// Error: variant not found
<Typography variant="oldVariant">Text</Typography>

// Solution: Check migration map or use new variant
<Typography variant="body.default">Text</Typography>
```

**2. Nested Object Access**
```tsx
// Error: Cannot read property
className={typography.body}

// Solution: Use new nested structure
className={typography.body.default}
```

**3. Component Props**
```tsx
// Error: Invalid prop
<Typography size="large">Text</Typography>

// Solution: Use variant or size correctly
<Typography variant="body.large">Text</Typography>
<UI variant="button" size="large">Button</UI>
```

### Debugging Tools

**Typography Inspector**
```tsx
// Debug component for checking typography
function TypographyDebugger({ children, variant }) {
  console.log('Typography variant:', variant);
  console.log('Resolved classes:', typography[variant]);
  
  return children;
}
```

**Migration Checker**
```tsx
// Utility to check for legacy usage
function checkLegacyTypography() {
  const legacyPatterns = [
    'typography.h1',
    'typography.body',
    'typography.button'
  ];
  
  // Check for patterns in your codebase
  legacyPatterns.forEach(pattern => {
    console.warn(`Legacy pattern found: ${pattern}`);
  });
}
```

## Performance Considerations

### Font Loading
```tsx
// Optimize font loading in HTML head
<link 
  rel="preload" 
  href="/fonts/Inter-Variable.woff2" 
  as="font" 
  type="font/woff2" 
  crossOrigin="anonymous"
/>

// Use font-display for better loading
<style>{performanceHelpers.fontDisplay.swap}</style>
```

### Bundle Size
```tsx
// Import only what you need
import { Typography, Heading } from '../components/ui/typography-enhanced';

// Instead of importing everything
import * from '../lib/typography-enhanced';
```

## Rollback Plan

If you need to rollback:

1. **Keep legacy imports available**
2. **Use feature flags for gradual rollout**
3. **Maintain both systems during transition**
4. **Document which components are migrated**

```tsx
// Feature flag approach
const USE_NEW_TYPOGRAPHY = process.env.REACT_APP_NEW_TYPOGRAPHY === 'true';

function MyComponent() {
  const typographySystem = USE_NEW_TYPOGRAPHY ? newTypography : legacyTypography;
  
  return (
    <h1 className={typographySystem.h1}>Title</h1>
  );
}
```

## Next Steps

After migration:

1. **Update documentation**
2. **Train team on new system**
3. **Set up linting rules**
4. **Create design system guidelines**
5. **Monitor performance metrics**

## Support

For migration support:
- Check the [Typography Demo 2024](../pages/TypographyDemo/TypographyDemo2024.tsx)
- Review [Typography System Documentation](./typography-system-2024.md)
- Create issues for specific migration problems
- Join design system discussions