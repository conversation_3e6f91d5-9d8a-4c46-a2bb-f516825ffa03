import React from 'react'
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  SheetTitle,
} from '../../../../components/ui/sheet'
import { Button } from '../../../../components/ui/button'
import { ScrollArea } from '../../../../components/ui/scroll-area'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '../../../../components/ui/tabs'
import { User, Shield, FileText, UserPlus } from 'lucide-react'
import { useCreateUserForm } from './hooks/useCreateUserForm'
import { BasicInfoTab } from './components/BasicInfoTab'
import { RoleTemplateTab } from './components/RoleTemplateTab'
import { NotesTab } from './components/NotesTab'
import type { CreateUserSheetProps } from './types'

export function CreateUserSheet({ open, onOpenChange, onSuccess }: CreateUserSheetProps) {
  const {
    activeTab,
    setActiveTab,
    formData,
    isSubmitting,
    handleInputChange,
    handleRoleTemplateChange,
    handlePermissionToggle,
    handleSubmit
  } = useCreateUserForm(open, onSuccess, () => onOpenChange(false))

  return (
    <Sheet open={open} onOpenChange={onOpenChange} key={open ? 'open' : 'closed'}>
      <SheetContent className="sm:max-w-2xl flex flex-col">
        <SheetHeader className="space-y-3">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
              <UserPlus className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <SheetTitle className="text-xl">Add New User</SheetTitle>
              <SheetDescription>
                Create a new user account with appropriate permissions and access levels.
              </SheetDescription>
            </div>
          </div>
        </SheetHeader>

        <div className="flex-1 min-h-0 mt-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-3 flex-shrink-0">
              <TabsTrigger value="basic" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Basic Info
              </TabsTrigger>
              <TabsTrigger value="role" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Permissions
              </TabsTrigger>
              <TabsTrigger value="notes" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Additional Info
              </TabsTrigger>
            </TabsList>

            <div className="flex-1 min-h-0 mt-4">
              <ScrollArea className="h-full">
                <div className="pr-4 pb-4">
                  <TabsContent value="basic" className="space-y-6 mt-0">
                    <BasicInfoTab 
                      formData={formData}
                      onInputChange={handleInputChange}
                    />
                  </TabsContent>

                  <TabsContent value="role" className="space-y-6 mt-0">
                    <RoleTemplateTab
                      formData={formData}
                      onRoleTemplateChange={handleRoleTemplateChange}
                      onPermissionToggle={handlePermissionToggle}
                    />
                  </TabsContent>

                  <TabsContent value="notes" className="space-y-6 mt-0">
                    <NotesTab
                      formData={formData}
                      onInputChange={handleInputChange}
                    />
                  </TabsContent>
                </div>
              </ScrollArea>
            </div>
          </Tabs>
        </div>

        <SheetFooter className="flex-shrink-0 pt-6 border-t">
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
            className="border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                Creating User...
              </>
            ) : (
              <>
                <UserPlus className="h-4 w-4 mr-2" />
                Create User
              </>
            )}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  )
}