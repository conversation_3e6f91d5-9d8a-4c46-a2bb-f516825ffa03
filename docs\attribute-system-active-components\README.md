# Active Components Analysis - Attribute System

## Order Creation Flow (AddOrderFormTwoPanel)
- **Main Component**: `AddOrderFormTwoPanel.tsx`
- **Left Panel**: `FormLeftPanel.tsx`
  - Uses `ItemsSection.tsx` - ✅ MIGRATED to SWR
  - Uses `PaymentsSection.tsx` - (doesn't use attributes)
- **Right Panel**: `FormRightPanel.tsx` 
  - Preview only, no attribute usage

## Order Viewing/Editing Flow (ViewOrderSheetTwoPanel)
- **Main Component**: `ViewOrderSheetTwoPanel.tsx`
- **Edit Manager**: `EditDialogManager.tsx`
  - **Item Edit**: `OrderItemEditForm.tsx`
    - Uses `useOrderItemFormSimplified.ts` (exported as useOrderItemForm)
    - Which uses `useAttributeLoading.ts`
    - Which uses `useUnifiedAttributes` → SWR
  - **Details Edit**: `OrderDetailsEditForm.tsx` 
    - Uses Zustand directly ❌
  - **Payment Edit**: `PaymentEditForm.tsx` - (doesn't use attributes)
  - **Note Edit**: `NoteEditForm.tsx` - (doesn't use attributes)

## DEPRECATED Components (Still in codebase but not used)
- `OrderItemEditDialog.tsx` - Marked as @deprecated
- `OrderItemForm.tsx` - Likely deprecated (old form)
- `useOrderItemForm.ts` - Marked as @deprecated in comments

## Key Findings

### Currently Active Order Components:
1. **For Creating Orders**:
   - `ItemsSection.tsx` - ✅ Already migrated to SWR
   - `BasicInfoSection.tsx` - ✅ Already migrated to SWR

2. **For Editing Orders**:
   - `OrderItemEditForm.tsx` → Uses unified system (which uses SWR)
   - `OrderDetailsEditForm.tsx` → Still uses Zustand ❌

3. **Shared Components**:
   - `ProductCombinationSelector.tsx` - Used in both flows, still uses Zustand ❌

## Migration Priority (Revised)

### IMMEDIATE (Affects active order flows):
1. `ProductCombinationSelector.tsx` - Used everywhere for product selection
2. `OrderDetailsEditForm.tsx` - Active in order editing
3. `CategorySelectorDialog/index.tsx` - Used by ProductCombinationSelector

### HIGH (Production Cost - affects pricing):
1. `IntelligentProductMatrix.tsx`
2. `ProductMatrix.tsx` 
3. `ProductionCostTemplateSheet.tsx`

### MEDIUM (Product Management):
1. Products AttributesTab components
2. Filter dialogs

### LOW (Can be deleted/ignored):
1. Deprecated components
2. Test pages
3. Badge components

## Important Note
The "unified" system (`useUnifiedAttributes`) is actually helping here - components using it are already getting SWR data, even though it adds unnecessary abstraction. We should:
1. First migrate Zustand components to SWR
2. Then simplify by removing the unified layer