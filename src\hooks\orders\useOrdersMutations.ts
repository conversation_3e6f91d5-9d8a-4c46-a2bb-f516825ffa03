import { mutate } from 'swr'
import {
  addOrder as addOrderTo<PERSON><PERSON>,
  updateOrder as updateOrderIn<PERSON><PERSON>,
  deleteOrder as deleteOrder<PERSON>rom<PERSON><PERSON>
} from '../../services'
import type { Order } from '../../pages/Orders/types'
import { logger } from '../../utils/logger'
import { getOrdersKey } from './useOrdersBasic'

/**
 * Hook for order mutation operations with optimistic updates
 */
export function useOrdersMutations() {

  /**
   * Add a new order and update the SWR cache with optimistic updates
   */
  const addOrder = async (orderData: Omit<Order, 'id'>): Promise<Order> => {
    try {
      logger.debug('addOrder: Starting to add order', { customer: orderData.customer })

      // Create optimistic version with temporary ID
      const tempId = `temp-${Date.now()}`
      const optimisticOrder: Order = {
        ...orderData,
        id: tempId,
        orderNo: 'Pending...'
      }

      // Perform optimistic update
      await mutate(
        getOrdersKey(),
        async (currentData: Order[] = []) => {
          return [optimisticOrder, ...currentData]
        },
        { revalidate: false }
      )

      // Create the order in the database
      const newOrder = await addOrderToApi(orderData)

      // Update cache with real order data
      await mutate(
        getOrdersKey(),
        async (currentData: Order[] = []) => {
          const filteredData = currentData.filter(
            order => order.id !== tempId && order.id !== newOrder.id
          )
          return [newOrder, ...filteredData]
        },
        { revalidate: false }
      )

      return newOrder
    } catch (error: any) {
      logger.error('Error adding order:', error)
      // Revalidate cache to remove optimistic update
      mutate(getOrdersKey())
      throw error
    }
  }

  /**
   * Update an order and update the SWR cache with optimistic updates
   */
  const updateOrder = async (id: string, orderData: Partial<Order>): Promise<Order> => {
    try {
      if (!id) {
        throw new Error('Cannot update order: Missing order ID')
      }

      // Get current data for optimistic update
      const currentData = await mutate(getOrdersKey())
      const currentOrder = currentData?.find(order => order.id === id)

      if (!currentOrder) {
        throw new Error(`Order with ID ${id} not found in cache`)
      }

      // Create optimistic data
      const optimisticOrder = {
        ...currentOrder,
        ...orderData,
        id,
        updatedAt: new Date().toISOString()
      }

      // Perform optimistic update
      await mutate(
        getOrdersKey(),
        async (currentData: Order[] = []) => {
          return currentData.map(order =>
            order.id === id ? optimisticOrder : order
          )
        },
        { revalidate: false }
      )

      // Prepare full order data
      const fullOrderData = {
        ...currentOrder,
        ...orderData,
        id
      }

      // Update in database
      const updatedOrder = await updateOrderInApi(fullOrderData)

      // Update cache with server response
      await mutate(
        getOrdersKey(),
        async (currentData: Order[] = []) => {
          return currentData.map(order =>
            order.id === id ? { ...updatedOrder, id } : order
          )
        },
        { revalidate: false }
      )

      return { ...updatedOrder, id }
    } catch (error) {
      logger.error('Error updating order:', error)
      // Revalidate cache to revert optimistic update
      mutate(getOrdersKey())
      throw error
    }
  }

  /**
   * Delete an order and update the SWR cache with optimistic updates
   */
  const deleteOrder = async (id: string): Promise<boolean> => {
    try {
      if (!id) {
        throw new Error('Cannot delete order: Missing order ID')
      }

      // Get current data for validation
      const currentData = await mutate(getOrdersKey())
      const orderToDelete = currentData?.find(order => order.id === id)

      if (!orderToDelete) {
        throw new Error(`Order with ID ${id} not found in cache`)
      }

      // Perform optimistic update - remove from cache
      await mutate(
        getOrdersKey(),
        async (currentData: Order[] = []) => {
          return currentData.filter(order => order.id !== id)
        },
        { revalidate: false }
      )

      // Delete from database
      await deleteOrderFromApi(id)

      logger.debug('Order deleted successfully')
      return true
    } catch (error) {
      logger.error('Error deleting order:', error)
      // Revalidate cache to revert optimistic update
      mutate(getOrdersKey())
      throw error
    }
  }

  return {
    addOrder,
    updateOrder,
    deleteOrder
  }
}