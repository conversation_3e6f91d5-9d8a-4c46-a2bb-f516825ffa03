import React, { useState, useCallback } from 'react';
import { Navigate, useNavigate } from 'react-router-dom';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { useAuth } from '../../contexts/AuthContext';
import { UserStateDetectionService } from '../../services/auth/userStateDetection.service';
import type { UserStateResult } from '../../services/auth/userStateDetection.service';

/**
 * PreAuthPage component for email validation before account creation
 * Validates email against authorized_users table for pre-authorization
 */
const PreAuthPage: React.FC = () => {
  const { user, loading } = useAuth();
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [userState, setUserState] = useState<UserStateResult | null>(null);
  
  // Redirect if already logged in
  if (user) {
    return <Navigate to="/" replace />;
  }

  // Detect comprehensive user authentication state
  const detectUserState = useCallback(async (email: string): Promise<UserStateResult | null> => {
    try {
      const stateResult = await UserStateDetectionService.detectUserState(email);
      console.log('User state detected:', stateResult);
      return stateResult;
    } catch (error) {
      console.error('Error detecting user state:', error);
      return null;
    }
  }, []);

  const validateEmail = useCallback(async () => {
    if (!email.trim()) {
      setValidationError('Please enter your email address');
      return;
    }

    // Basic email format validation
    const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
    if (!emailRegex.test(email)) {
      setValidationError('Please enter a valid email address');
      return;
    }

    setIsValidating(true);
    setValidationError(null);

    try {
      // Detect comprehensive user state
      const stateResult = await detectUserState(email);
      
      if (!stateResult) {
        setValidationError('Failed to validate email. Please try again.');
        return;
      }

      setUserState(stateResult);

      // Handle different user states
      switch (stateResult.state) {
        case 'unauthorized':
          setValidationError(stateResult.message);
          break;

        case 'account_complete':
          setValidationError('Account already exists. Please use the login page instead.');
          // Optional: Auto-redirect to login after delay
          setTimeout(() => {
            navigate('/login', { 
              state: { email, message: 'Account already exists - please login' } 
            });
          }, 2000);
          break;

        case 'authorized_only':
        case 'signup_started':
        case 'signup_incomplete':
          // All these states should proceed to account creation
          navigate('/account-creation', { 
            state: { 
              email, 
              authorizedUser: stateResult.authorizedUser,
              userState: stateResult,
              fromEmailVerification: true
            } 
          });
          break;

        default:
          setValidationError('Unknown account state. Please contact support.');
      }
    } catch (error) {
      setValidationError('Failed to validate email. Please try again.');
      setUserState(null);
      console.error('Email validation error:', error);
    } finally {
      setIsValidating(false);
    }
  }, [email, detectUserState]);

  const proceedToAccountCreation = () => {
    if (userState?.authorizedUser && email) {
      navigate('/account-creation', { 
        state: { 
          email, 
          authorizedUser: userState.authorizedUser,
          userState
        } 
      });
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isValidating) {
      validateEmail();
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="w-full max-w-sm mx-4">
          <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-gray-200 mx-auto mb-4"></div>
            <p className="text-gray-600 font-medium">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="w-full max-w-sm mx-4">
        <div className="bg-white rounded-2xl shadow-lg p-8">
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <div className="w-14 h-14 rounded-xl bg-black flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <h1 className="text-2xl font-bold text-black mb-0">Aming Trading</h1>
            <p className="text-gray-600 text-base mb-6">Sales & Order Management System</p>
            <h2 className="text-xl font-extrabold text-black mb-0">Access Verification</h2>
            <p className="text-gray-500 text-sm mb-0">Enter your email to verify access</p>
          </div>

        <div className="space-y-6 mb-8">
          {!userState?.canLogin && (
            <div className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-black text-sm font-medium">
                  Email Address
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Email"
                  className="bg-gray-100 border-0 rounded-lg px-4 py-3 text-gray-700 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-black transition-colors"
                  disabled={isValidating}
                />
              </div>

              <Button
                onClick={validateEmail}
                disabled={isValidating || !email.trim()}
                className="w-full bg-black hover:bg-gray-800 text-white py-3 rounded-lg font-medium transition-all duration-200"
              >
                {isValidating ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                    Validating...
                  </div>
                ) : (
                  'Verify Access'
                )}
              </Button>

              {validationError && (
                <div className="p-4 rounded-lg bg-red-50 border border-red-200">
                  <p className="text-red-600 text-sm mb-0">{validationError}</p>
                  {userState?.state === 'unauthorized' && (
                    <p className="text-red-600 text-xs mt-1 mb-0">
                      Contact your administrator to request access.
                    </p>
                  )}
                  {userState?.state === 'account_complete' && (
                    <p className="text-green-600 text-xs mt-1 mb-0">
                      Redirecting to login page...
                    </p>
                  )}
                </div>
              )}
            </div>
          )}

          {userState?.canSignup && userState?.state !== 'account_complete' && (
            <div className="space-y-6">
              <div className="p-4 rounded-lg bg-green-50 border border-green-200">
                <p className="text-green-600 text-sm mb-0">{userState.message}</p>
                <p className="text-green-600 text-xs mt-1 mb-0">Next: {userState.nextAction}</p>
              </div>

              <Button
                onClick={proceedToAccountCreation}
                className="w-full bg-black hover:bg-gray-800 text-white py-3 rounded-lg font-medium transition-all duration-200"
              >
                {userState.state === 'authorized_only' ? 'Create Account' : 'Continue Setup'}
              </Button>

              <div className="text-center">
                <button
                  onClick={() => {
                    setUserState(null);
                    setEmail('');
                    setValidationError(null);
                  }}
                  className="text-sm text-gray-500 hover:text-gray-700"
                >
                  Use different email
                </button>
              </div>
            </div>
          )}
        </div>

        <div className="text-center pt-4">
          <p className="text-sm text-gray-500">
            Already have an account? <a href="/login" className="text-black font-semibold hover:underline transition-colors">Sign in</a>
          </p>
        </div>
        </div>
      </div>
    </div>
  );
};

export default PreAuthPage;