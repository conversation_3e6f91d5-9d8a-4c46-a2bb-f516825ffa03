/**
 * Password Match Indicator Component
 * Smart debounced matching feedback to avoid aggressive re-renders
 * Following CLAUDE.md guidelines - focused component under 250 lines
 */

import React, { useState, useEffect, useRef } from 'react';
import { Check, X, AlertCircle } from 'lucide-react';

interface PasswordMatchIndicatorProps {
  password: string;
  confirmPassword: string;
  className?: string;
  debounceMs?: number;
}

export const PasswordMatchIndicator: React.FC<PasswordMatchIndicatorProps> = ({
  password,
  confirmPassword,
  className = '',
  debounceMs = 800 // Wait 800ms after user stops typing
}) => {
  const [shouldShowIndicator, setShouldShowIndicator] = useState(false);
  const [isMatching, setIsMatching] = useState<boolean | null>(null);
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  
  useEffect(() => {
    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    // Don't show anything if confirm password is empty
    if (!confirmPassword.trim()) {
      setShouldShowIndicator(false);
      setIsMatching(null);
      return;
    }
    
    // Don't show anything if original password is empty
    if (!password.trim()) {
      setShouldShowIndicator(false);
      setIsMatching(null);
      return;
    }
    
    // Set up debounced check
    timeoutRef.current = setTimeout(() => {
      const matches = password === confirmPassword;
      setIsMatching(matches);
      setShouldShowIndicator(true);
    }, debounceMs);
    
    // Cleanup function
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [password, confirmPassword, debounceMs]);
  
  // Don't render anything if we shouldn't show the indicator
  if (!shouldShowIndicator || isMatching === null) {
    return null;
  }
  
  return (
    <div className={`flex items-center space-x-2 mt-2 ${className}`}>
      {isMatching ? (
        <>
          <Check className="h-4 w-4 text-green-600 flex-shrink-0" />
          <span className="text-sm text-green-700 font-medium">Passwords match</span>
        </>
      ) : (
        <>
          <X className="h-4 w-4 text-red-500 flex-shrink-0" />
          <span className="text-sm text-red-600">Passwords don't match</span>
        </>
      )}
    </div>
  );
};

export default PasswordMatchIndicator;