# Schema Migrations

Database structure changes, table creation, column modifications, and core architectural updates.

## Files in this directory:

### Production Cost System
- `001_production_cost_schema.sql` - Initial production cost system setup
- `002_standardize_attribute_types.sql` - Standardize data types across tables
- `003_unified_product_data_architecture.sql` - Unified product data structure
- `004_add_computed_production_costs.sql` - Add computed production cost fields
- `005_order_production_cost_calculation_functions.sql` - Production cost calculation functions
- `006_enhanced_production_cost_with_calc_rules.sql` - Enhanced cost calculation with business rules
- `007_production_cost_with_breakdown_storage.sql` - Production cost breakdown storage system

## Execution Order

Execute schema migrations **first** before other categories:

1. `001_production_cost_schema.sql` (foundational)
2. `002_*` and `003_*` files (architectural improvements)
3. `004_add_computed_production_costs.sql`
4. `005_*` through `007_*` (advanced features)

## Dependencies

- These migrations create the foundational database structure
- Must be executed before data, permission, or feature migrations
- Some files depend on previous schema migrations

## Notes

- Always backup database before executing schema changes
- Test in development environment first
- Schema changes may require application code updates