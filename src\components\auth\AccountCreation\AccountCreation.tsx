import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../../contexts/AuthContext';
import { AuthLoading } from '../../ui/loading-indicator';
import { SetupForm } from './components/SetupForm';
import { VerificationForm } from './components/VerificationForm';
import { StatusMessages } from './components/StatusMessages';
import { useAccountCreation } from './hooks/useAccountCreation';
import type { AccountCreationState } from './types';

/**
 * AccountCreation component for first-time user signup
 * Following CLAUDE.md: <250 lines, single responsibility, focused functionality
 */
const AccountCreation: React.FC = () => {
  const { user, loading } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  
  // Get email and user data from PreAuthPage navigation
  const state = location.state as AccountCreationState;
  const userState = state?.userState;

  // Track if we're redirecting to prevent hooks errors
  const [isRedirecting, setIsRedirecting] = useState(false);

  // Handle redirects after hooks are called
  useEffect(() => {
    // Redirect if already logged in
    if (user && !isRedirecting) {
      setIsRedirecting(true);
      navigate('/', { replace: true });
      return;
    }

    // Redirect if no state (direct access without pre-auth)
    if ((!state?.email || !state?.authorizedUser) && !isRedirecting) {
      setIsRedirecting(true);
      navigate('/pre-auth', { replace: true });
      return;
    }
  }, [user, state, navigate, isRedirecting]);

  // Early return with loading state if redirecting
  if (isRedirecting || user || !state?.email || !state?.authorizedUser) {
    return <AuthLoading title="Redirecting..." />;
  }

  const { email, authorizedUser } = state;

  const {
    // State
    password,
    confirmPassword,
    pin,
    confirmPin,
    firstName,
    lastName,
    step,
    verificationCode,
    isCreating,
    error,

    // Actions
    setPassword,
    setConfirmPassword,
    setPin,
    setConfirmPin,
    setFirstName,
    setLastName,
    setStep,
    setVerificationCode,
    handleCreateAccount,
    handleVerifyCode
  } = useAccountCreation({ email, authorizedUser });

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isCreating) {
      if (step === 'setup') {
        handleCreateAccount();
      } else {
        handleVerifyCode();
      }
    }
  };

  if (loading) {
    return <AuthLoading />;
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 p-4">
      <div className="w-full max-w-lg mx-auto">
        <div className="bg-white rounded-2xl shadow-lg p-8 md:p-10">
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <div className="w-14 h-14 rounded-xl bg-black flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
            </div>
            <h2 className="text-2xl font-extrabold text-black mb-0">
              {step === 'setup' ? 'Create Account' : 'Verify Email'}
            </h2>
            <p className="text-gray-500 text-sm mb-0">
              {step === 'setup' 
                ? (authorizedUser?.role === 'admin' || authorizedUser?.role === 'administrator'
                    ? 'Set up your admin password'
                    : 'Set up your account credentials')
                : 'Enter the verification code sent to your email'
              }
            </p>
          </div>

          <StatusMessages userState={userState} state={state} step={step} />
        
          <div className="space-y-6 mb-8">
            {step === 'setup' ? (
              <SetupForm
                email={email}
                authorizedUser={authorizedUser}
                password={password}
                confirmPassword={confirmPassword}
                firstName={firstName}
                lastName={lastName}
                pin={pin}
                confirmPin={confirmPin}
                onPasswordChange={setPassword}
                onConfirmPasswordChange={setConfirmPassword}
                onFirstNameChange={setFirstName}
                onLastNameChange={setLastName}
                onPinChange={setPin}
                onConfirmPinChange={setConfirmPin}
                onKeyPress={handleKeyPress}
                onSubmit={handleCreateAccount}
                isCreating={isCreating}
                error={error}
              />
            ) : (
              <VerificationForm
                email={email}
                verificationCode={verificationCode}
                onVerificationCodeChange={setVerificationCode}
                onKeyPress={handleKeyPress}
                onSubmit={handleVerifyCode}
                onBackToSetup={() => setStep('setup')}
                isCreating={isCreating}
                error={error}
              />
            )}
          </div>

          <div className="text-center pt-4">
            <p className="text-sm text-gray-500">
              Already have an account? <a href="/login" className="text-black font-semibold hover:underline transition-colors">Sign in</a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccountCreation;