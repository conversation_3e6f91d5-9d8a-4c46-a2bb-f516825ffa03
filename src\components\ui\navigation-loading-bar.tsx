import { motion, AnimatePresence } from 'framer-motion'
import { useNavigationLoading } from '../layout/NavigationLoadingProvider'

export function NavigationLoadingBar() {
  const { isNavigating, isPending } = useNavigationLoading()
  const isLoading = isNavigating || isPending

  return (
    <AnimatePresence>
      {isLoading && (
        <motion.div
          className="fixed top-0 left-0 right-0 z-50 h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-blue-500"
          initial={{ scaleX: 0, opacity: 0.8 }}
          animate={{ 
            scaleX: 1, 
            opacity: 1,
            background: [
              'linear-gradient(90deg, #3b82f6, #8b5cf6, #3b82f6)',
              'linear-gradient(90deg, #8b5cf6, #3b82f6, #8b5cf6)',
              'linear-gradient(90deg, #3b82f6, #8b5cf6, #3b82f6)'
            ]
          }}
          exit={{ scaleX: 1.1, opacity: 0 }}
          transition={{ 
            scaleX: { duration: 0.3, ease: 'easeInOut' },
            background: { duration: 1.5, repeat: Infinity, ease: 'linear' },
            exit: { duration: 0.2 }
          }}
          style={{ transformOrigin: 'left' }}
        />
      )}
    </AnimatePresence>
  )
}