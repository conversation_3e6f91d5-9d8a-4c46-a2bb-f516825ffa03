import useSWRMutation from 'swr/mutation'
import { AdminService, type AuthorizedUser } from '../../services/admin'
import { ADMIN_KEYS } from './useAdminData'
import type { AdminData } from './useAdminData'

// Fetcher functions for useSWRMutation
const createUserFetcher = async (url: string, { arg }: { arg: {
  email: string
  first_name: string
  last_name: string
  department?: string
  permissions: string[]
  role_template?: string
  notes?: string
} }) => {
  const newUser = await AdminService.createUser(arg)
  return newUser
}

const updateUserFetcher = async (url: string, { arg }: { arg: {
  id: string
  data: Partial<AuthorizedUser>
} }) => {
  const { id, data } = arg
  const updatedUser = await AdminService.updateUser(id, data)
  return updatedUser
}

/**
 * Modern SWR mutation hooks for admin operations with optimistic updates
 */
export function useAdminMutations(
  filters: { limit?: number; offset?: number; search?: string; active?: boolean } = { limit: 100 }
) {
  
  // Create user mutation
  const { 
    trigger: createUser, 
    isMutating: isCreating,
    error: createError 
  } = useSWRMutation(
    ADMIN_KEYS.combined(filters),
    createUserFetcher,
    {
      // Optimistic update: add new user immediately
      optimisticData: (currentData: AdminData | undefined, newUserData: any) => {
        if (!currentData) return currentData
        
        const tempUser: AuthorizedUser = {
          ...newUserData,
          id: `temp-${Date.now()}`,
          auth_user_id: `temp-auth-${Date.now()}`,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
        
        return {
          ...currentData,
          users: [tempUser, ...currentData.users],
          totalUsers: currentData.totalUsers + 1,
          activeUsers: tempUser.is_active ? currentData.activeUsers + 1 : currentData.activeUsers
        }
      },
      
      rollbackOnError: true,
      
      // Update cache with server response
      populateCache: (result: AuthorizedUser, currentData: AdminData | undefined) => {
        if (!currentData) return currentData
        
        // Remove any temporary users and add the real one
        const filteredUsers = currentData.users.filter(
          user => !user.id.startsWith('temp-') && user.id !== result.id
        )
        
        return {
          ...currentData,
          users: [result, ...filteredUsers],
          totalUsers: currentData.totalUsers, // This was already optimistically updated
          activeUsers: result.is_active ? currentData.activeUsers : currentData.activeUsers
        }
      },
      
      revalidate: false, // Using optimistic updates
      
      onError: (error) => {
        console.error('Failed to create user:', error)
      }
    }
  )

  // Update user mutation - DISABLED
  // Edit functionality has been removed
  const isUpdating = false
  const updateError = null
  
  const updateUser = (id: string, data: Partial<AuthorizedUser>) => {
    console.log('updateUser: Edit functionality has been disabled')
    return Promise.reject(new Error('Edit functionality has been disabled'))
  }

  return {
    // Mutation functions
    createUser: (userData: {
      email: string
      first_name: string
      last_name: string
      department?: string
      permissions: string[]
      role_template?: string
      notes?: string
    }) => createUser(userData),
    
    updateUser, // Disabled function that rejects
    
    // Loading states
    isCreating,
    isUpdating,
    isMutating: isCreating || isUpdating,
    
    // Error states
    createError,
    updateError,
    error: createError || updateError
  }
}