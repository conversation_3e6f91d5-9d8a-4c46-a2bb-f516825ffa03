/**
 * PermissionGuard - Route-level permission protection component
 * 
 * Following CLAUDE.md guidelines:
 * - Single responsibility: page-level protection  
 * - <250 lines
 * - Composition over inheritance
 */

import React from 'react';
import { usePermissionsContext } from '../../../contexts/PermissionsContext';
import type { PermissionGuardProps } from '../../../types/permissions.types';

// ============================================================================
// PERMISSION GUARD COMPONENT
// ============================================================================

/**
 * Guards entire pages/routes based on permissions
 * Used for page-level access control
 */
export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  permission,
  children,
  fallback = <NotAuthorizedPage />,
  loading = <LoadingSpinner />
}) => {
  const { checkPermission, loading: permissionsLoading } = usePermissionsContext();

  // Show loading state while permissions are being loaded
  if (permissionsLoading) {
    return <>{loading}</>;
  }

  const hasPermission = checkPermission(permission);

  if (!hasPermission) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

// ============================================================================
// DEFAULT FALLBACK COMPONENTS
// ============================================================================

/**
 * Default loading spinner for permission checks
 */
const LoadingSpinner: React.FC = () => (
  <div className="flex justify-center items-center p-4">
    <div className="animate-spin h-6 w-6 border-2 border-blue-600 rounded-full border-t-transparent" />
  </div>
);

/**
 * Default unauthorized access page
 */
const NotAuthorizedPage: React.FC = () => (
  <div className="flex flex-col items-center justify-center min-h-[400px] p-8">
    <div className="text-6xl mb-4">🔒</div>
    <h2 className="text-xl font-semibold text-gray-700 mb-2">Access Denied</h2>
    <p className="text-gray-500 text-center max-w-md mb-0">
      You don't have permission to access this page. Please contact your administrator if you believe this is an error.
    </p>
  </div>
);