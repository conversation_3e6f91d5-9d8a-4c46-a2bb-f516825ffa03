import type { AuthorizedUser, UpdateUserRequest } from '../../../../services/admin'

export interface EditUserSheetProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  user: AuthorizedUser
  onSuccess: () => Promise<void>
}

export interface EditUserFormData extends UpdateUserRequest {
  // All fields from UpdateUserRequest are optional, but we need them as strings for form handling
  first_name: string
  last_name: string
  department: string
  permissions: string[]
  role_template: string
  notes: string
  is_active: boolean
}