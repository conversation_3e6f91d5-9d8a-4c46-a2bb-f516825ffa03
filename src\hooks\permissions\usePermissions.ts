/**
 * usePermissions Hook - Unified permission management hook
 * 
 * Following CLAUDE.md guidelines:
 * - Uses AuthContext directly (no redundant SWR layer)
 * - Single responsibility: permission access convenience methods
 * - <100 lines (simplified from original)
 */

import { useCallback } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import type {
  PermissionKey,
  UsePermissionsResult
} from '../../types/permissions.types';

// ============================================================================
// SIMPLIFIED PERMISSIONS HOOK
// ============================================================================

export interface BulkPermissionCheckResult {
  readonly [key: string]: boolean;
}

export const usePermissions = (): UsePermissionsResult => {
  // Get unified state from AuthContext (no redundant SWR layer)
  const {
    permissions,
    role,
    loading,
    error,
    authorized,
    hasPermission,
    hasAllPermissions,
    isAdmin,
    refreshPermissions
  } = useAuth();

  // Additional convenience methods if needed
  const checkPermission = useCallback((permission: PermissionKey): boolean => {
    return hasPermission(permission);
  }, [hasPermission]);

  const checkMultiplePermissions = useCallback((
    permissionList: readonly PermissionKey[]
  ): BulkPermissionCheckResult => {
    const results: Record<string, boolean> = {};
    
    permissionList.forEach(perm => {
      results[perm] = hasPermission(perm);
    });

    return results;
  }, [hasPermission]);

  return {
    permissions,
    role,
    loading,
    error: error || null,
    isAdmin,
    hasPermission,
    hasAllPermissions,
    refreshPermissions,
    // Legacy compatibility methods
    checkPermission,
    checkMultiplePermissions
  };
};

// ============================================================================
// UTILITY HOOKS (Simplified)
// ============================================================================

/**
 * Hook for permission-based conditional rendering
 */
export const usePermissionGuard = () => {
  const { hasPermission } = usePermissions();
  
  return useCallback((
    permission: PermissionKey | readonly PermissionKey[],
    children: React.ReactNode,
    fallback?: React.ReactNode
  ): React.ReactNode => {
    return hasPermission(permission) ? children : (fallback || null);
  }, [hasPermission]);
};