#!/bin/bash
# =============================================================================
# DATABASE BACKUP SCRIPT - Supabase PostgreSQL
# =============================================================================
# Creates a complete backup of your Supabase database including:
# - Full schema (tables, functions, triggers, indexes)
# - All data in all tables  
# - Row Level Security (RLS) policies
# - User permissions and roles
#
# Usage: ./scripts/backup-database.sh
# =============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# =============================================================================
# CONFIGURATION
# =============================================================================

# Load environment variables
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
else
    print_error ".env file not found. Please create it with DATABASE_URL."
    exit 1
fi

# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
    print_error "DATABASE_URL not found in .env file."
    print_error "Add this line to your .env file:"
    print_error "DATABASE_URL=postgresql://postgres:[password]@db.your-project-id.supabase.co:5432/postgres"
    exit 1
fi

# Create backup directory with timestamp
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="database-backups/$TIMESTAMP"
mkdir -p "$BACKUP_DIR"

print_status "Starting database backup..."
print_status "Backup directory: $BACKUP_DIR"

# =============================================================================
# BACKUP EXECUTION
# =============================================================================

print_status "Creating comprehensive database backup..."

# 1. Full database dump (schema + data)
print_status "1. Creating full database backup (schema + data)..."
pg_dump "$DATABASE_URL" \
    --verbose \
    --no-owner \
    --no-privileges \
    --format=custom \
    --file="$BACKUP_DIR/full_database_backup.dump" \
    2>&1 | tee "$BACKUP_DIR/backup.log"

if [ ${PIPESTATUS[0]} -eq 0 ]; then
    print_success "Full database backup completed"
else
    print_error "Full database backup failed"
    exit 1
fi

# 2. Schema-only backup (for reference)
print_status "2. Creating schema-only backup..."
pg_dump "$DATABASE_URL" \
    --schema-only \
    --verbose \
    --no-owner \
    --no-privileges \
    --file="$BACKUP_DIR/schema_only.sql" \
    2>&1 | tee -a "$BACKUP_DIR/backup.log"

# 3. Data-only backup (for reference)  
print_status "3. Creating data-only backup..."
pg_dump "$DATABASE_URL" \
    --data-only \
    --verbose \
    --no-owner \
    --no-privileges \
    --file="$BACKUP_DIR/data_only.sql" \
    2>&1 | tee -a "$BACKUP_DIR/backup.log"

# 4. Permissions-specific backup
print_status "4. Creating permissions system backup..."
pg_dump "$DATABASE_URL" \
    --verbose \
    --no-owner \
    --no-privileges \
    --table=permissions \
    --table=roles \
    --table=authorized_users \
    --file="$BACKUP_DIR/permissions_system_backup.sql" \
    2>&1 | tee -a "$BACKUP_DIR/backup.log"

# 5. Create backup metadata
print_status "5. Creating backup metadata..."
cat > "$BACKUP_DIR/backup_info.txt" << EOF
=============================================================================
DATABASE BACKUP INFORMATION
=============================================================================
Backup Date: $(date)
Database: Supabase PostgreSQL
Project: Aming-app Permission System
Backup Type: Full (Schema + Data)

Files Created:
- full_database_backup.dump  : Complete database (use pg_restore)
- schema_only.sql           : Database structure only  
- data_only.sql            : Data only
- permissions_system_backup.sql : Permissions tables only
- backup.log               : Backup execution log
- backup_info.txt          : This information file

Restore Instructions:
===================
To restore the full database:
pg_restore --clean --if-exists --verbose --dbname=\$DATABASE_URL full_database_backup.dump

To restore specific tables:
psql \$DATABASE_URL -f permissions_system_backup.sql

Size Information:
================
EOF

# Add file sizes to metadata
ls -lh "$BACKUP_DIR" >> "$BACKUP_DIR/backup_info.txt"

# 6. Test backup integrity
print_status "6. Testing backup integrity..."
pg_restore --list "$BACKUP_DIR/full_database_backup.dump" > "$BACKUP_DIR/backup_contents.txt" 2>&1
if [ $? -eq 0 ]; then
    print_success "Backup integrity verified"
else
    print_warning "Could not verify backup integrity (but backup may still be valid)"
fi

# =============================================================================
# BACKUP SUMMARY
# =============================================================================

BACKUP_SIZE=$(du -sh "$BACKUP_DIR" | cut -f1)
FILE_COUNT=$(ls -1 "$BACKUP_DIR" | wc -l)

print_success "Database backup completed successfully!"
echo ""
echo "📊 Backup Summary:"
echo "   Directory: $BACKUP_DIR"  
echo "   Total Size: $BACKUP_SIZE"
echo "   Files Created: $FILE_COUNT"
echo ""
echo "📋 Files:"
echo "   • full_database_backup.dump (primary backup - use pg_restore)"
echo "   • schema_only.sql (structure reference)"
echo "   • data_only.sql (data reference)"  
echo "   • permissions_system_backup.sql (permissions tables)"
echo "   • backup.log (execution log)"
echo "   • backup_info.txt (metadata)"
echo "   • backup_contents.txt (backup contents list)"
echo ""
echo "🔄 To restore:"
echo "   pg_restore --clean --if-exists --verbose --dbname=\"\$DATABASE_URL\" \"$BACKUP_DIR/full_database_backup.dump\""
echo ""
echo "⚠️  IMPORTANT: Store this backup in a secure location before running migrations!"

# Create a quick restore script for convenience
cat > "$BACKUP_DIR/restore.sh" << EOF
#!/bin/bash
# Quick restore script for this backup
# Usage: ./restore.sh

echo "🚨 WARNING: This will OVERWRITE your current database!"
echo "Current backup: $(basename $(dirname $(readlink -f \$0)))"
echo ""
read -p "Are you sure you want to restore? (yes/no): " -r
if [[ \$REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
    echo "Restoring database..."
    pg_restore --clean --if-exists --verbose --dbname="\$DATABASE_URL" full_database_backup.dump
    echo "Restore completed!"
else
    echo "Restore cancelled."
fi
EOF

chmod +x "$BACKUP_DIR/restore.sh"
print_success "Quick restore script created: $BACKUP_DIR/restore.sh"

print_success "🎉 Backup process completed successfully!"
print_warning "📁 Backup saved to: $BACKUP_DIR"
print_warning "💾 Please store this backup in a secure location before proceeding with migrations!"