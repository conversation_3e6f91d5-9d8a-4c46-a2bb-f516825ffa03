-- Migration: Create analytics_get_client_performance database function
-- Purpose: Provide comprehensive client performance analytics with growth metrics and segmentation

CREATE OR REPLACE FUNCTION analytics_get_client_performance(
  p_period_months INTEGER DEFAULT 6
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_start_date DATE;
  v_comparison_start_date DATE;
  v_comparison_end_date DATE;
  v_result JSON;
BEGIN
  -- Calculate date ranges
  v_start_date := CURRENT_DATE - INTERVAL '1 month' * p_period_months;
  v_comparison_end_date := v_start_date - INTERVAL '1 day';
  v_comparison_start_date := v_comparison_end_date - INTERVAL '1 month' * p_period_months;

  -- Main query to get comprehensive client analytics
  WITH client_metrics AS (
    SELECT 
      o.client_name,
      COALESCE(o.sector, 'Unknown') as sector,
      COALESCE(o.sales_agent, 'Unknown') as agent,
      
      -- Revenue and order metrics
      SUM(o.total_amount) as total_revenue,
      COUNT(DISTINCT o.order_id) as total_orders,
      AVG(o.total_amount) as avg_order_value,
      
      -- Payment metrics
      SUM(COALESCE(o.cash_paid, 0)) as total_paid,
      SUM(o.total_amount) - SUM(COALESCE(o.cash_paid, 0)) as outstanding_balance,
      CASE 
        WHEN SUM(o.total_amount) > 0 
        THEN (SUM(COALESCE(o.cash_paid, 0)) / SUM(o.total_amount)) * 100
        ELSE 0 
      END as payment_rate,
      
      -- Date metrics
      MAX(o.created_at) as last_order_date,
      MIN(o.created_at) as first_order_date,
      EXTRACT(DAYS FROM CURRENT_DATE - MAX(o.created_at::date)) as days_since_last_order,
      
      -- Calculate order frequency (orders per month)
      CASE 
        WHEN EXTRACT(DAYS FROM MAX(o.created_at) - MIN(o.created_at)) > 0
        THEN COUNT(DISTINCT o.order_id) * 30.0 / EXTRACT(DAYS FROM MAX(o.created_at) - MIN(o.created_at))
        ELSE COUNT(DISTINCT o.order_id)
      END as order_frequency,
      
      -- Profit calculation (assuming 30% margin if no cost data)
      SUM(o.total_amount) * 0.3 as total_profit,
      30.0 as profit_margin
      
    FROM orders o
    WHERE o.created_at >= v_start_date
      AND o.client_name IS NOT NULL
      AND o.client_name != ''
    GROUP BY o.client_name, o.sector, o.sales_agent
  ),
  
  -- Get comparison period data for growth calculations
  comparison_metrics AS (
    SELECT 
      o.client_name,
      SUM(o.total_amount) as comparison_revenue,
      COUNT(DISTINCT o.order_id) as comparison_orders
    FROM orders o
    WHERE o.created_at >= v_comparison_start_date 
      AND o.created_at <= v_comparison_end_date
      AND o.client_name IS NOT NULL
      AND o.client_name != ''
    GROUP BY o.client_name
  ),
  
  -- Calculate rankings and percentiles
  ranked_clients AS (
    SELECT 
      cm.*,
      ROW_NUMBER() OVER (ORDER BY cm.total_revenue DESC) as revenue_rank,
      PERCENT_RANK() OVER (ORDER BY cm.total_revenue) * 100 as revenue_percentile,
      
      -- Growth calculations
      CASE 
        WHEN comp.comparison_revenue > 0 
        THEN ((cm.total_revenue - comp.comparison_revenue) / comp.comparison_revenue) * 100
        ELSE 0 
      END as revenue_growth_rate,
      
      CASE 
        WHEN comp.comparison_orders > 0 
        THEN ((cm.total_orders - comp.comparison_orders) / comp.comparison_orders::float) * 100
        ELSE 0 
      END as order_growth_rate
      
    FROM client_metrics cm
    LEFT JOIN comparison_metrics comp ON cm.client_name = comp.client_name
  ),
  
  -- Add segmentation logic
  segmented_clients AS (
    SELECT 
      *,
      -- Client tier based on revenue percentile
      CASE 
        WHEN revenue_percentile >= 90 THEN 'platinum'
        WHEN revenue_percentile >= 75 THEN 'gold'
        WHEN revenue_percentile >= 50 THEN 'silver'
        ELSE 'bronze'
      END as client_tier,
      
      -- Client status based on recency and frequency
      CASE 
        WHEN days_since_last_order <= 30 AND order_frequency >= 2 THEN 'active'
        WHEN days_since_last_order <= 60 AND order_frequency >= 1 THEN 'regular'
        WHEN days_since_last_order <= 90 THEN 'at-risk'
        ELSE 'inactive'
      END as client_status,
      
      -- Risk level based on payment behavior and recency
      CASE 
        WHEN payment_rate < 50 OR days_since_last_order > 90 THEN 'high'
        WHEN payment_rate < 80 OR days_since_last_order > 60 THEN 'medium'
        ELSE 'low'
      END as risk_level
      
    FROM ranked_clients
  ),
  
  -- Calculate summary metrics
  summary_data AS (
    SELECT 
      COUNT(*) as total_clients,
      SUM(total_revenue) as total_revenue,
      AVG(total_revenue) as avg_revenue_per_client,
      AVG(payment_rate) as avg_payment_rate,
      COUNT(*) FILTER (WHERE client_status = 'active') as active_clients,
      COUNT(*) FILTER (WHERE risk_level = 'high') as high_risk_clients
    FROM segmented_clients
  ),
  
  -- Calculate segment distributions
  tier_segments AS (
    SELECT 
      client_tier,
      COUNT(*) as count,
      SUM(total_revenue) as revenue,
      (SUM(total_revenue) / (SELECT SUM(total_revenue) FROM segmented_clients)) * 100 as percentage
    FROM segmented_clients
    GROUP BY client_tier
  ),
  
  status_segments AS (
    SELECT 
      client_status,
      COUNT(*) as count,
      SUM(total_revenue) as revenue
    FROM segmented_clients
    GROUP BY client_status
  )

  -- Build final JSON result
  SELECT json_build_object(
    'clients', (
      SELECT json_agg(
        json_build_object(
          'clientName', client_name,
          'sector', sector,
          'agent', agent,
          'totalRevenue', total_revenue,
          'totalOrders', total_orders,
          'avgOrderValue', avg_order_value,
          'totalPaid', total_paid,
          'outstandingBalance', outstanding_balance,
          'paymentRate', payment_rate,
          'lastOrderDate', last_order_date,
          'firstOrderDate', first_order_date,
          'daysSinceLastOrder', days_since_last_order,
          'orderFrequency', order_frequency,
          'totalProfit', total_profit,
          'profitMargin', profit_margin,
          'clientStatus', client_status,
          'riskLevel', risk_level,
          'clientTier', client_tier,
          'revenueRank', revenue_rank,
          'revenuePercentile', revenue_percentile,
          'revenueGrowthRate', revenue_growth_rate,
          'orderGrowthRate', order_growth_rate
        )
      )
      FROM segmented_clients
      ORDER BY total_revenue DESC
    ),
    'summary', (
      SELECT json_build_object(
        'totalClients', total_clients,
        'totalRevenue', total_revenue,
        'avgRevenuePerClient', avg_revenue_per_client,
        'avgPaymentRate', avg_payment_rate,
        'activeClients', active_clients,
        'highRiskClients', high_risk_clients
      )
      FROM summary_data
    ),
    'segments', json_build_object(
      'byTier', (
        SELECT json_object_agg(
          client_tier,
          json_build_object(
            'count', count,
            'revenue', revenue,
            'percentage', percentage
          )
        )
        FROM tier_segments
      ),
      'byStatus', (
        SELECT json_object_agg(
          client_status,
          json_build_object(
            'count', count,
            'revenue', revenue
          )
        )
        FROM status_segments
      )
    )
  ) INTO v_result;

  RETURN v_result;

EXCEPTION WHEN OTHERS THEN
  -- Return error information
  RETURN json_build_object(
    'error', SQLERRM,
    'clients', '[]'::json,
    'summary', json_build_object(
      'totalClients', 0,
      'totalRevenue', 0,
      'avgRevenuePerClient', 0,
      'avgPaymentRate', 0,
      'activeClients', 0,
      'highRiskClients', 0
    ),
    'segments', json_build_object(
      'byTier', '{}'::json,
      'byStatus', '{}'::json
    )
  );
END;
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION analytics_get_client_performance TO authenticated;
GRANT EXECUTE ON FUNCTION analytics_get_client_performance TO anon;

-- Add helpful comment
COMMENT ON FUNCTION analytics_get_client_performance IS 'Returns comprehensive client performance analytics including metrics, segmentation, and growth analysis';