-- Create user audit logs table for tracking user modifications
-- 
-- This table provides comprehensive audit trail for all user account changes
-- including who made changes, when, what was changed, and additional context
-- for compliance and security monitoring.

-- ============================================================================
-- CREATE USER AUDIT LOGS TABLE
-- ============================================================================

CREATE TABLE user_audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES authorized_users(id) ON DELETE CASCADE,
    modified_by UUID NOT NULL REFERENCES authorized_users(id) ON DELETE SET NULL,
    action TEXT NOT NULL CHECK (action IN ('created', 'updated', 'activated', 'deactivated')),
    changes JSONB NOT NULL DEFAULT '{}'::jsonb,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT now(),
    ip_address INET,
    user_agent TEXT,
    reason TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- ============================================================================
-- CREATE INDEXES FOR PERFORMANCE
-- ============================================================================

-- Index for querying by user_id (most common query pattern)
CREATE INDEX idx_user_audit_logs_user_id ON user_audit_logs(user_id);

-- Index for querying by timestamp (for recent activity queries)
CREATE INDEX idx_user_audit_logs_timestamp ON user_audit_logs(timestamp DESC);

-- Index for querying by modified_by (who made changes)
CREATE INDEX idx_user_audit_logs_modified_by ON user_audit_logs(modified_by);

-- Index for querying by action type
CREATE INDEX idx_user_audit_logs_action ON user_audit_logs(action);

-- Composite index for common queries (user + recent activity)
CREATE INDEX idx_user_audit_logs_user_timestamp ON user_audit_logs(user_id, timestamp DESC);

-- ============================================================================
-- CREATE RLS POLICIES
-- ============================================================================

-- Enable RLS
ALTER TABLE user_audit_logs ENABLE ROW LEVEL SECURITY;

-- Policy: Admins can read all audit logs
CREATE POLICY "user_audit_logs_admin_read" ON user_audit_logs
    FOR SELECT TO authenticated
    USING (is_admin());

-- Policy: Admins can create audit logs (system operations)
CREATE POLICY "user_audit_logs_admin_insert" ON user_audit_logs
    FOR INSERT TO authenticated
    WITH CHECK (is_admin());

-- Policy: Users can read their own audit logs
CREATE POLICY "user_audit_logs_self_read" ON user_audit_logs
    FOR SELECT TO authenticated
    USING (user_id = auth.uid());

-- ============================================================================
-- CREATE HELPER FUNCTIONS
-- ============================================================================

-- Function to automatically create audit log entry
CREATE OR REPLACE FUNCTION log_user_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Only log actual changes (not just timestamp updates)
    IF (TG_OP = 'UPDATE' AND OLD IS NOT DISTINCT FROM NEW) THEN
        RETURN NEW;
    END IF;

    -- Create audit log entry
    INSERT INTO user_audit_logs (
        user_id,
        modified_by,
        action,
        changes,
        timestamp,
        reason
    ) VALUES (
        CASE 
            WHEN TG_OP = 'INSERT' THEN NEW.id
            ELSE OLD.id
        END,
        auth.uid(), -- Current authenticated user
        CASE 
            WHEN TG_OP = 'INSERT' THEN 'created'
            WHEN TG_OP = 'UPDATE' THEN 
                CASE 
                    WHEN OLD.is_active = true AND NEW.is_active = false THEN 'deactivated'
                    WHEN OLD.is_active = false AND NEW.is_active = true THEN 'activated'
                    ELSE 'updated'
                END
            ELSE 'updated'
        END,
        CASE 
            WHEN TG_OP = 'INSERT' THEN '{}'::jsonb
            ELSE (
                SELECT jsonb_object_agg(key, jsonb_build_object('old', old_val, 'new', new_val))
                FROM (
                    SELECT 
                        key,
                        to_jsonb(OLD) -> key AS old_val,
                        to_jsonb(NEW) -> key AS new_val
                    FROM jsonb_each(to_jsonb(NEW))
                    WHERE to_jsonb(OLD) -> key IS DISTINCT FROM to_jsonb(NEW) -> key
                    AND key NOT IN ('updated_at', 'created_at') -- Exclude timestamp fields
                ) changes
            )
        END,
        now(),
        'Automatic trigger log'
    );

    RETURN CASE WHEN TG_OP = 'DELETE' THEN OLD ELSE NEW END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- CREATE TRIGGERS
-- ============================================================================

-- Trigger to automatically log user changes
CREATE TRIGGER user_audit_trigger
    AFTER INSERT OR UPDATE ON authorized_users
    FOR EACH ROW
    EXECUTE FUNCTION log_user_change();

-- ============================================================================
-- ADD CONSTRAINTS AND COMMENTS
-- ============================================================================

-- Add constraint to ensure changes is valid JSON
ALTER TABLE user_audit_logs 
ADD CONSTRAINT valid_changes_json 
CHECK (changes IS NOT NULL);

-- Add constraint to ensure user_agent is reasonable length
ALTER TABLE user_audit_logs 
ADD CONSTRAINT reasonable_user_agent_length 
CHECK (char_length(user_agent) <= 500);

-- Add constraint to ensure reason is reasonable length
ALTER TABLE user_audit_logs 
ADD CONSTRAINT reasonable_reason_length 
CHECK (char_length(reason) <= 500);

-- Add comments for documentation
COMMENT ON TABLE user_audit_logs IS 'Audit trail for all user account modifications';
COMMENT ON COLUMN user_audit_logs.user_id IS 'ID of the user whose account was modified';
COMMENT ON COLUMN user_audit_logs.modified_by IS 'ID of the user who made the modification';
COMMENT ON COLUMN user_audit_logs.action IS 'Type of action performed: created, updated, activated, or deactivated';
COMMENT ON COLUMN user_audit_logs.changes IS 'JSON object containing old and new values for changed fields';
COMMENT ON COLUMN user_audit_logs.timestamp IS 'When the change was made';
COMMENT ON COLUMN user_audit_logs.ip_address IS 'IP address of the user who made the change';
COMMENT ON COLUMN user_audit_logs.user_agent IS 'Browser/client information of the user who made the change';
COMMENT ON COLUMN user_audit_logs.reason IS 'Optional reason or context for the change';

-- ============================================================================
-- GRANT PERMISSIONS
-- ============================================================================

-- Grant necessary permissions to authenticated role
GRANT SELECT, INSERT ON user_audit_logs TO authenticated;
GRANT USAGE ON SEQUENCE user_audit_logs_id_seq TO authenticated;

-- ============================================================================
-- VERIFICATION
-- ============================================================================

-- Test query to verify table structure
DO $$
BEGIN
    RAISE NOTICE 'User audit logs table created successfully';
    RAISE NOTICE 'Indexes created: %', (
        SELECT count(*) 
        FROM pg_indexes 
        WHERE tablename = 'user_audit_logs'
    );
    RAISE NOTICE 'Policies created: %', (
        SELECT count(*) 
        FROM pg_policies 
        WHERE tablename = 'user_audit_logs'
    );
END
$$;