import React from 'react';
import type { UserStateResult } from '../../../../services/auth/userStateDetection.service';
import type { AccountCreationState } from '../types';

interface StatusMessagesProps {
  userState?: UserStateResult;
  state?: AccountCreationState;
  step: 'setup' | 'verification';
}

export const StatusMessages: React.FC<StatusMessagesProps> = ({ userState, state, step }) => {
  return (
    <>
      {/* UX IMPROVEMENT: Show user state information */}
      {userState && step === 'setup' && (
        <div className="mb-6 p-3 rounded-lg bg-blue-50 border border-blue-200">
          <div className="flex items-center">
            <svg className="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div className="flex-1">
              <p className="text-blue-600 text-sm mb-0 font-medium">{userState.message}</p>
              <p className="text-blue-500 text-xs mt-1 mb-0">Status: {userState.state.replace('_', ' ')} • {userState.nextAction}</p>
            </div>
          </div>
        </div>
      )}
      
      {state?.fromEmailVerification && step === 'setup' && (
        <div className="mb-6 p-3 rounded-lg bg-green-50 border border-green-200">
          <div className="flex items-center">
            <svg className="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7"></path>
            </svg>
            <p className="text-green-600 text-sm mb-0 font-medium">Email verified successfully!</p>
          </div>
          <p className="text-green-600 text-xs mt-1 mb-0 ml-6">Complete your account setup below.</p>
        </div>
      )}
    </>
  );
};