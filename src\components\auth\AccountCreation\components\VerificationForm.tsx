import React from 'react';
import { Button } from '../../../ui/button';
import { Input } from '../../../ui/input';
import { Label } from '../../../ui/label';
import { ButtonLoading } from '../../../ui/loading-indicator';
import { ErrorMessage, InfoMessage } from '../../../ui/message-display';

interface VerificationFormProps {
  email: string;
  verificationCode: string;
  onVerificationCodeChange: (value: string) => void;
  onKeyPress: (e: React.KeyboardEvent) => void;
  onSubmit: () => void;
  onBackToSetup: () => void;
  isCreating: boolean;
  error: string | null;
}

export const VerificationForm: React.FC<VerificationFormProps> = ({
  email,
  verificationCode,
  onVerificationCodeChange,
  onKeyPress,
  onSubmit,
  onBackToSetup,
  isCreating,
  error
}) => {
  return (
    <div className="space-y-6">
      <InfoMessage 
        message={`We've sent a verification code to ${email}`}
        className="mb-6"
      />

      <div className="space-y-2">
        <Label htmlFor="code" className="text-black text-sm font-medium">
          Verification Code
        </Label>
        <Input
          id="code"
          type="text"
          value={verificationCode}
          onChange={(e) => onVerificationCodeChange(e.target.value)}
          onKeyPress={onKeyPress}
          placeholder="Enter 6-digit code"
          className="bg-gray-100 border-0 rounded-lg px-4 py-3 text-gray-700 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-black transition-colors text-center text-lg tracking-wider"
          disabled={isCreating}
          maxLength={6}
        />
      </div>

      <Button
        onClick={onSubmit}
        disabled={isCreating || !verificationCode.trim()}
        className="w-full bg-black hover:bg-gray-800 text-white py-3 rounded-lg font-medium transition-all duration-200 mt-6"
      >
        {isCreating ? (
          <ButtonLoading text="Verifying..." />
        ) : (
          'Verify & Complete Setup'
        )}
      </Button>

      {error && (
        <ErrorMessage message={error} className="mt-4" />
      )}

      <div className="text-center pt-2">
        <button
          onClick={onBackToSetup}
          className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
          disabled={isCreating}
        >
          Back to account setup
        </button>
      </div>
    </div>
  );
};