import { useState, useEffect } from 'react'
import { useAuth } from '../../../../../contexts/AuthContext'
import { useToast } from '../../../../../hooks/use-toast'
import { AdminService } from '../../../../../services/admin'
import { RoleTemplateService } from '../../../../../services/roleTemplateService'
import { useFormValidation } from './useFormValidation'
import type { CreateUserFormData } from '../types'

export function useCreateUserForm(open: boolean, onSuccess: () => Promise<void>, onClose: () => void) {
  const { user } = useAuth()
  const { toast } = useToast()
  const { validateForm } = useFormValidation()
  
  const [activeTab, setActiveTab] = useState('basic')
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const [formData, setFormData] = useState<CreateUserFormData>({
    email: '',
    first_name: '',
    last_name: '',
    department: '',
    permissions: [],
    role_template: '',
    notes: '',
  })

  // Reset form when sheet opens/closes
  useEffect(() => {
    if (!open) {
      setFormData({
        email: '',
        first_name: '',
        last_name: '',
        department: '',
        permissions: [],
        role_template: '',
        notes: '',
      })
      setActiveTab('basic')
    }
  }, [open])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleRoleTemplateChange = async (roleTemplate: string) => {
    const rolePermissions = await RoleTemplateService.getRolePermissions(roleTemplate)
    setFormData(prev => ({
      ...prev,
      role_template: roleTemplate,
      permissions: rolePermissions
    }))
  }

  const handlePermissionToggle = (permission: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      permissions: checked
        ? [...prev.permissions, permission]
        : prev.permissions.filter(p => p !== permission)
    }))
  }

  const handleSubmit = async () => {
    const validationError = validateForm(formData)
    if (validationError) {
      toast({
        title: 'Validation Error',
        description: validationError,
        variant: 'destructive'
      })
      return
    }

    setIsSubmitting(true)
    try {
      const newUser = await AdminService.createUser({
        email: formData.email.trim(),
        first_name: formData.first_name.trim(),
        last_name: formData.last_name.trim(),
        department: formData.department?.trim() || undefined,
        permissions: formData.permissions,
        role_template: formData.role_template || undefined,
        notes: formData.notes?.trim() || undefined,
      })

      // Trigger parent refresh FIRST (like successful patterns)
      await onSuccess()

      // Show success toast AFTER refresh completes
      toast({
        title: 'User Created',
        description: `${newUser.first_name} ${newUser.last_name} has been added successfully`,
      })

      // Close dialog LAST (like successful patterns)
      onClose()
    } catch (error) {
      console.error('Failed to create user:', error)
      toast({
        title: 'Creation Failed',
        description: error instanceof Error ? error.message : 'Failed to create user account',
        variant: 'destructive'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return {
    activeTab,
    setActiveTab,
    formData,
    isSubmitting,
    handleInputChange,
    handleRoleTemplateChange,
    handlePermissionToggle,
    handleSubmit
  }
}