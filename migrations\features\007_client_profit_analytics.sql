-- Migration: Client Profit Analytics Functions
-- Purpose: Leverage existing production cost system for accurate client profit analytics

-- Function to analyze production cost coverage and data quality
CREATE OR REPLACE FUNCTION analyze_production_cost_coverage()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_result JSON;
BEGIN
  WITH coverage_stats AS (
    SELECT 
      COUNT(*) as total_order_items,
      COUNT(CASE WHEN production_cost > 0 THEN 1 END) as items_with_production_cost,
      COUNT(CASE WHEN profit_amount IS NOT NULL THEN 1 END) as items_with_profit,
      COUNT(DISTINCT order_id) as total_orders,
      COUNT(DISTINCT CASE WHEN production_cost > 0 THEN order_id END) as orders_with_costs,
      SUM(original_amount) as total_revenue,
      SUM(CASE WHEN production_cost > 0 THEN production_cost ELSE 0 END) as total_production_cost,
      SUM(CASE WHEN profit_amount IS NOT NULL THEN profit_amount ELSE 0 END) as total_profit
    FROM order_items
  )
  SELECT json_build_object(
    'totalOrderItems', total_order_items,
    'itemsWithProductionCost', items_with_production_cost,
    'itemsWithProfit', items_with_profit,
    'productionCostCoverage', 
      CASE WHEN total_order_items > 0 
        THEN (items_with_production_cost::float / total_order_items) * 100
        ELSE 0 
      END,
    'profitDataCoverage',
      CASE WHEN total_order_items > 0 
        THEN (items_with_profit::float / total_order_items) * 100
        ELSE 0 
      END,
    'totalOrders', total_orders,
    'ordersWithCosts', orders_with_costs,
    'orderCostCoverage',
      CASE WHEN total_orders > 0 
        THEN (orders_with_costs::float / total_orders) * 100
        ELSE 0 
      END,
    'totalRevenue', total_revenue,
    'totalProductionCost', total_production_cost,
    'totalProfit', total_profit,
    'overallProfitMargin',
      CASE WHEN total_revenue > 0 
        THEN (total_profit / total_revenue) * 100
        ELSE 0 
      END
  ) INTO v_result
  FROM coverage_stats;

  RETURN v_result;
END;
$$;

-- Function to calculate order profit summary using existing production cost data
CREATE OR REPLACE FUNCTION calculate_order_profit_summary()
RETURNS TABLE (
  order_id UUID,
  order_date DATE,
  client_name TEXT,
  total_revenue NUMERIC,
  total_production_cost NUMERIC,
  total_profit NUMERIC,
  profit_margin_percentage NUMERIC,
  items_with_costs INTEGER,
  total_items INTEGER,
  cost_data_available BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    o.order_id,
    o.created_at::date as order_date,
    o.client_name,
    o.total_amount as total_revenue,
    COALESCE(item_costs.total_production_cost, 0) as total_production_cost,
    COALESCE(item_costs.total_profit, 0) as total_profit,
    CASE 
      WHEN o.total_amount > 0 
      THEN (COALESCE(item_costs.total_profit, 0) / o.total_amount) * 100
      ELSE 0 
    END as profit_margin_percentage,
    COALESCE(item_costs.items_with_costs, 0) as items_with_costs,
    COALESCE(item_costs.total_items, 0) as total_items,
    COALESCE(item_costs.items_with_costs, 0) > 0 as cost_data_available
  FROM orders o
  LEFT JOIN (
    SELECT 
      oi.order_id,
      COUNT(*) as total_items,
      COUNT(CASE WHEN oi.production_cost > 0 THEN 1 END) as items_with_costs,
      SUM(COALESCE(oi.production_cost, 0)) as total_production_cost,
      SUM(COALESCE(oi.profit_amount, 0)) as total_profit
    FROM order_items oi
    GROUP BY oi.order_id
  ) item_costs ON o.order_id = item_costs.order_id
  WHERE o.client_name IS NOT NULL 
    AND o.client_name != ''
  ORDER BY o.created_at DESC;
END;
$$;

-- Function to get comprehensive client profit analytics
CREATE OR REPLACE FUNCTION get_client_profit_analytics(
  p_period_months INTEGER DEFAULT 12
)
RETURNS TABLE (
  client_name TEXT,
  total_revenue NUMERIC,
  total_production_cost NUMERIC,
  total_profit NUMERIC,
  avg_profit_margin NUMERIC,
  orders_with_costs INTEGER,
  total_orders INTEGER,
  items_with_costs INTEGER,
  total_items INTEGER,
  profit_data_reliability TEXT,
  last_order_date DATE,
  first_order_date DATE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_start_date DATE;
BEGIN
  v_start_date := CURRENT_DATE - INTERVAL '1 month' * p_period_months;

  RETURN QUERY
  WITH client_order_profits AS (
    SELECT 
      o.client_name,
      o.order_id,
      o.total_amount as order_revenue,
      o.created_at::date as order_date,
      COALESCE(item_summary.total_production_cost, 0) as order_production_cost,
      COALESCE(item_summary.total_profit, 0) as order_profit,
      COALESCE(item_summary.items_with_costs, 0) as order_items_with_costs,
      COALESCE(item_summary.total_items, 0) as order_total_items
    FROM orders o
    LEFT JOIN (
      SELECT 
        oi.order_id,
        COUNT(*) as total_items,
        COUNT(CASE WHEN oi.production_cost > 0 THEN 1 END) as items_with_costs,
        SUM(COALESCE(oi.production_cost, 0)) as total_production_cost,
        SUM(COALESCE(oi.profit_amount, 0)) as total_profit
      FROM order_items oi
      GROUP BY oi.order_id
    ) item_summary ON o.order_id = item_summary.order_id
    WHERE o.client_name IS NOT NULL 
      AND o.client_name != ''
      AND o.created_at >= v_start_date
  ),
  client_aggregates AS (
    SELECT 
      cop.client_name,
      SUM(cop.order_revenue) as total_revenue,
      SUM(cop.order_production_cost) as total_production_cost,
      SUM(cop.order_profit) as total_profit,
      COUNT(*) as total_orders,
      COUNT(CASE WHEN cop.order_items_with_costs > 0 THEN 1 END) as orders_with_costs,
      SUM(cop.order_items_with_costs) as items_with_costs,
      SUM(cop.order_total_items) as total_items,
      MAX(cop.order_date) as last_order_date,
      MIN(cop.order_date) as first_order_date
    FROM client_order_profits cop
    GROUP BY cop.client_name
  )
  SELECT 
    ca.client_name,
    ca.total_revenue,
    ca.total_production_cost,
    ca.total_profit,
    CASE 
      WHEN ca.total_revenue > 0 
      THEN (ca.total_profit / ca.total_revenue) * 100
      ELSE 0 
    END as avg_profit_margin,
    ca.orders_with_costs,
    ca.total_orders,
    ca.items_with_costs,
    ca.total_items,
    CASE 
      WHEN ca.total_items = 0 THEN 'none'
      WHEN (ca.items_with_costs::float / ca.total_items) >= 0.8 THEN 'high'
      WHEN (ca.items_with_costs::float / ca.total_items) >= 0.3 THEN 'partial'
      ELSE 'low'
    END as profit_data_reliability,
    ca.last_order_date,
    ca.first_order_date
  FROM client_aggregates ca
  ORDER BY ca.total_revenue DESC;
END;
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION analyze_production_cost_coverage TO authenticated;
GRANT EXECUTE ON FUNCTION analyze_production_cost_coverage TO anon;
GRANT EXECUTE ON FUNCTION calculate_order_profit_summary TO authenticated;
GRANT EXECUTE ON FUNCTION calculate_order_profit_summary TO anon;
GRANT EXECUTE ON FUNCTION get_client_profit_analytics TO authenticated;
GRANT EXECUTE ON FUNCTION get_client_profit_analytics TO anon;

-- Add helpful comments
COMMENT ON FUNCTION analyze_production_cost_coverage IS 'Analyzes production cost data coverage and quality across all order items';
COMMENT ON FUNCTION calculate_order_profit_summary IS 'Calculates profit summary for all orders using existing production cost data';
COMMENT ON FUNCTION get_client_profit_analytics IS 'Provides comprehensive client profit analytics leveraging existing production cost system';