{"main": {"id": "11e8d0e361d692e4", "type": "split", "children": [{"id": "4967c7a7842174c8", "type": "tabs", "children": [{"id": "5ad362d985f2d266", "type": "leaf", "state": {"type": "markdown", "state": {"file": "security/supabase-api-key-security-audit.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "supabase-api-key-security-audit"}}]}], "direction": "vertical"}, "left": {"id": "e54ab9c5c7265543", "type": "split", "children": [{"id": "ed9acdec5f7d4f2e", "type": "tabs", "children": [{"id": "6f26651880a18d36", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "e6477c98b88e675c", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "eb39bc1b922bdf01", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 300}, "right": {"id": "faa61059a3b5bcea", "type": "split", "children": [{"id": "d5ec129cf888c635", "type": "tabs", "children": [{"id": "33d565bf92598b1c", "type": "leaf", "state": {"type": "backlink", "state": {"file": "auth/supabase-auth-research.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for supabase-auth-research"}}, {"id": "ce90090d6e615f20", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "auth/supabase-auth-research.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from supabase-auth-research"}}, {"id": "375ca3c921fb456f", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "f54c38f4d5fbe810", "type": "leaf", "state": {"type": "outline", "state": {"file": "auth/supabase-auth-research.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of supabase-auth-research"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false}}, "active": "5ad362d985f2d266", "lastOpenFiles": ["security/service-role-key-architecture.md", "security/service-role-key-architecture.md.tmp.1093.1754883717943", "auth/authentication-system-ideation.md", "security/supabase-api-key-security-audit.md.tmp.1093.1754883182502", "security/supabase-api-key-security-audit.md.tmp.1093.1754883169959", "auth/authentication-system-ideation.md.tmp.3215.1754810848725", "auth/authentication-system-ideation.md.tmp.3215.1754810839071", "auth/authentication-system-ideation.md.tmp.3215.1754810824797", "auth/authentication-system-ideation.md.tmp.3215.1754810790476", "auth/authentication-system-ideation.md.tmp.798.1754677336838", "auth/authentication-system-ideation.md.tmp.798.1754677292910", "auth/authentication-system-ideation.md.tmp.798.1754677283675", "auth/implementation-examples.md", "auth/production-checklist.md", "auth/auth_system_design.md", "auth/implementation/permissions-implementation-roadmap.md", "auth/implementation/permissions-first-analysis.md", "auth/implementation/archive-delete-system-design.md", "auth/implementation/code-quality-analysis-from-real-world-examples.md", "auth/implementation/industry-standards-research-findings.md", "auth/implementation/permissions-system-design/planning-summary.md", "auth/implementation/permissions-system-design/page-level-permissions-revision.md", "auth/implementation/permissions-system-design/implementation-strategy.md", "auth/implementation/permissions-system-design/permission-registry.md", "auth/implementation/permissions-system-design/current-state-analysis.md", "auth/implementation/permissions-system-design/database-schema.md", "auth/implementation/permissions-system-design/permission-registry-revised.md", "auth/implementation/permissions-system-design/revised-implementation-strategy.md", "auth/implementation/permissions-system-design/README.md", "auth/implementation/code-quality-analysis.md", "auth/implementation/current-state-analysis/README.md", "auth/implementation/README.md", "auth/supabase-auth-research.md", "security/data-encryption-impact-audit.md", "development-guidelines/README.md", "development-guidelines/critical-rules-summary.md"]}