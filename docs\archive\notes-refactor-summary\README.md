# Notes Storage Refactor Summary

## Changes Made

### 1. Added `order_notes` Column to Orders Table
- Created migration: `006_add_order_notes_column.sql`
- Adds `order_notes` JSONB column to store general order notes directly in the orders table
- Structure: `[{content: string, created_at: string, created_by: string}]`

### 2. Updated Order Service (`order.service.ts`)
- **Removed** code that inserts into the separate `notes` table
- **Added** order notes to the `order_notes` column when creating orders
- **Updated** select queries to include `order_notes` column
- **Removed** code that fetches from the `notes` table
- Notes are now stored as simple JSONB objects

### 3. Updated UI Form (`BasicInfoSection.tsx`)
- Changed note creation to use simple objects suitable for JSONB storage
- Structure: `{content, created_at, created_by}`
- Removed unnecessary fields like `relatedType`, `category`, `noteType`

### 4. Updated Order Mapper (`order.mapper.ts`)
- Added backward compatibility to handle both old and new note formats
- Maps JSONB notes to the expected UI format
- Generates temporary IDs for notes from JSONB

## Current Storage Architecture

### Order Notes
- **Table**: `orders`
- **Column**: `order_notes` (JSONB)
- **Format**: Array of simple note objects

### Item Notes
- **Table**: `order_items`
- **Column**: `item_notes` (JSONB)
- **Format**: Array of note objects with category and type

### Payment Notes
- **Table**: `order_payments`
- **Column**: `notes` (TEXT)
- **Format**: Simple text string

### Separate Notes Table
- **Status**: Reserved for future use
- **Not used** for order, item, or payment notes anymore

## Benefits
1. Simpler data model - notes stored with their related entities
2. Better performance - no need for separate queries to fetch notes
3. Easier to maintain - no complex relationships to manage
4. The dedicated `notes` table remains available for future features

## Migration Required
Run the migration to add the `order_notes` column:
```sql
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS order_notes JSONB DEFAULT '[]'::jsonb;
```

## Testing
After applying the migration, test:
1. Creating new orders with notes
2. Viewing existing orders (they will show empty notes)
3. Adding/editing notes on orders
4. Ensure item notes and payment notes still work