/**
 * Permission Hierarchy Utility
 * Defines which CRUD permissions are included with page access permissions
 */

import type { PermissionKey } from '../types/permissions.types';

/**
 * Map of page access permissions to their included CRUD permissions
 * When a user has page access, they automatically get all associated CRUD permissions
 */
export const PAGE_PERMISSION_INCLUDES: Record<string, PermissionKey[]> = {
  // Orders page access includes all order CRUD operations
  'pages.orders_access': [
    'orders.create' as PermissionKey,
    'orders.edit' as PermissionKey,
    'orders.delete' as PermissionKey,
    'orders.general_info_edit' as PermissionKey,
    'orders.items_edit' as PermissionKey,
    'orders.payments_manage' as PermissionKey,
    'orders.status_update' as PermissionKey,
    'orders.notes_edit' as PermissionKey,
  ],
  
  // Products page access includes all product CRUD operations
  'pages.products_access': [
    'products.create' as PermissionKey,
    'products.edit' as PermissionKey,
    'products.delete' as PermissionKey,
    'products.pricing_edit' as PermissionKey,
    'products.view_costs' as Per<PERSON><PERSON><PERSON>,
  ],
  
  // Clients page access includes all client CRUD operations
  'pages.clients_access': [
    'clients.create' as PermissionKey,
    'clients.edit' as PermissionKey,
    'clients.delete' as Permission<PERSON><PERSON>,
    'clients.view_analytics' as PermissionKey,
  ],
  
  // Analytics pages include export capabilities
  'pages.analytics_overview_access': [
    'analytics.export' as PermissionKey,
    'analytics.advanced_metrics' as PermissionKey,
  ],
  
  'pages.analytics_sales_access': [
    'analytics.export' as PermissionKey,
    'analytics.custom_reports' as PermissionKey,
  ],
  
  'pages.analytics_production_access': [
    'analytics.export' as PermissionKey,
    'analytics.advanced_metrics' as PermissionKey,
  ],
  
  // Settings access includes user management
  'pages.settings_access': [
    'admin.system_settings' as PermissionKey,
  ],
  
  'pages.user_management_access': [
    'admin.user_management' as PermissionKey,
    'admin.users_create' as PermissionKey,
    'admin.permissions_assign' as PermissionKey,
  ],
  
  // Production cost access includes all cost management
  'pages.production_cost_access': [
    'production_costs.view' as PermissionKey,
    'production_costs.edit' as PermissionKey,
    'production_costs.templates' as PermissionKey,
  ]
};

/**
 * View-only permissions that override page access permissions
 */
export const VIEW_ONLY_OVERRIDES: Record<string, string[]> = {
  'orders.view_only': [
    'orders.create',
    'orders.edit',
    'orders.delete',
    'orders.general_info_edit',
    'orders.items_edit',
    'orders.payments_manage',
    'orders.status_update',
    'orders.notes_edit'
  ],
  'products.view_only': [
    'products.create',
    'products.edit',
    'products.delete',
    'products.pricing_edit'
  ],
  'clients.view_only': [
    'clients.create',
    'clients.edit',
    'clients.delete'
  ],
  'analytics.view_only': [
    'analytics.export',
    'analytics.advanced_metrics',
    'analytics.custom_reports'
  ]
};

/**
 * Check if a user has permission (including hierarchical permissions)
 * @param userPermissions - Array of permissions the user has
 * @param requiredPermission - The permission being checked
 * @returns boolean indicating if user has permission
 */
export function hasPermissionWithHierarchy(
  userPermissions: string[], 
  requiredPermission: PermissionKey
): boolean {
  // Direct permission check
  if (userPermissions.includes(requiredPermission)) {
    return true;
  }
  
  // System admin check
  if (userPermissions.includes('system.full_access')) {
    return true;
  }
  
  // Check if view-only permissions block this action
  for (const [viewOnlyPermission, blockedPermissions] of Object.entries(VIEW_ONLY_OVERRIDES)) {
    if (userPermissions.includes(viewOnlyPermission) && blockedPermissions.includes(requiredPermission)) {
      return false; // View-only permission explicitly blocks this action
    }
  }
  
  // Hierarchical permission check
  for (const [pagePermission, includedPermissions] of Object.entries(PAGE_PERMISSION_INCLUDES)) {
    if (userPermissions.includes(pagePermission) && includedPermissions.includes(requiredPermission)) {
      return true;
    }
  }
  
  return false;
}

/**
 * Get all permissions a user effectively has (including hierarchical)
 * @param userPermissions - Array of permissions the user explicitly has
 * @returns Array of all effective permissions (direct + inherited)
 */
export function getEffectivePermissions(userPermissions: string[]): PermissionKey[] {
  const effectivePermissions = new Set<PermissionKey>([...userPermissions] as PermissionKey[]);
  
  // Add hierarchical permissions
  for (const permission of userPermissions) {
    const includedPermissions = PAGE_PERMISSION_INCLUDES[permission];
    if (includedPermissions) {
      includedPermissions.forEach(p => effectivePermissions.add(p));
    }
  }
  
  return Array.from(effectivePermissions);
}

/**
 * Get specific CRUD permissions that come with a page permission
 * @param pagePermission - The page permission to check
 * @returns Array of included CRUD permissions
 */
export function getIncludedCrudPermissions(pagePermission: string): PermissionKey[] {
  return PAGE_PERMISSION_INCLUDES[pagePermission] || [];
}