/**
 * Auth Performance Monitor
 * Tracks authentication operation timing for optimization analysis
 */

interface AuthPerformanceMetric {
  operation: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  cached: boolean;
  userId?: string;
}

class AuthPerformanceMonitor {
  private metrics: AuthPerformanceMetric[] = [];
  private readonly MAX_METRICS = 100; // Keep last 100 metrics

  /**
   * Start timing an auth operation
   */
  startTiming(operation: string, cached = false, userId?: string): string {
    const id = `${operation}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.metrics.push({
      operation: `${operation}${cached ? '_cached' : ''}`,
      startTime: performance.now(),
      cached,
      userId
    });

    // Keep only the most recent metrics
    if (this.metrics.length > this.MAX_METRICS) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS);
    }

    return id;
  }

  /**
   * End timing an auth operation
   */
  endTiming(operation: string, cached = false): void {
    const metric = this.metrics
      .reverse()
      .find(m => m.operation === `${operation}${cached ? '_cached' : ''}` && !m.endTime);

    if (metric) {
      metric.endTime = performance.now();
      metric.duration = metric.endTime - metric.startTime;
      
      // Log performance data
      console.debug(`Auth Performance: ${metric.operation} took ${metric.duration.toFixed(2)}ms ${cached ? '(cached)' : '(fresh)'}`);
    }

    // Reverse back to maintain chronological order
    this.metrics.reverse();
  }

  /**
   * Get performance summary
   */
  getSummary() {
    const completed = this.metrics.filter(m => m.duration !== undefined);
    
    if (completed.length === 0) {
      return { message: 'No completed operations to analyze' };
    }

    const byOperation = completed.reduce((acc, metric) => {
      if (!acc[metric.operation]) {
        acc[metric.operation] = [];
      }
      acc[metric.operation].push(metric.duration!);
      return acc;
    }, {} as Record<string, number[]>);

    const summary = Object.entries(byOperation).map(([operation, durations]) => {
      const avg = durations.reduce((sum, d) => sum + d, 0) / durations.length;
      const min = Math.min(...durations);
      const max = Math.max(...durations);
      
      return {
        operation,
        count: durations.length,
        avgTime: Math.round(avg * 100) / 100,
        minTime: Math.round(min * 100) / 100,
        maxTime: Math.round(max * 100) / 100,
        totalTime: Math.round(durations.reduce((sum, d) => sum + d, 0) * 100) / 100
      };
    });

    return {
      totalOperations: completed.length,
      summary: summary.sort((a, b) => b.avgTime - a.avgTime),
      cacheHitRate: this.getCacheHitRate(),
      performanceGain: this.getPerformanceGain()
    };
  }

  /**
   * Calculate cache hit rate
   */
  private getCacheHitRate(): number {
    const completed = this.metrics.filter(m => m.duration !== undefined);
    const cached = completed.filter(m => m.cached);
    
    return completed.length > 0 ? (cached.length / completed.length) * 100 : 0;
  }

  /**
   * Calculate performance gain from caching
   */
  private getPerformanceGain(): { timeSaved: number; percentImprovement: number } {
    const completed = this.metrics.filter(m => m.duration !== undefined);
    const cached = completed.filter(m => m.cached);
    const fresh = completed.filter(m => !m.cached);
    
    if (cached.length === 0 || fresh.length === 0) {
      return { timeSaved: 0, percentImprovement: 0 };
    }

    const avgCachedTime = cached.reduce((sum, m) => sum + m.duration!, 0) / cached.length;
    const avgFreshTime = fresh.reduce((sum, m) => sum + m.duration!, 0) / fresh.length;
    
    const timeSaved = avgFreshTime - avgCachedTime;
    const percentImprovement = (timeSaved / avgFreshTime) * 100;
    
    return {
      timeSaved: Math.round(timeSaved * 100) / 100,
      percentImprovement: Math.round(percentImprovement * 100) / 100
    };
  }

  /**
   * Clear all metrics
   */
  clear(): void {
    this.metrics = [];
  }

  /**
   * Export metrics for analysis
   */
  exportMetrics(): AuthPerformanceMetric[] {
    return [...this.metrics];
  }
}

// Export singleton instance
export const authPerformanceMonitor = new AuthPerformanceMonitor();

// Development helper to access monitor from console
if (typeof window !== 'undefined') {
  (window as any).authPerformanceMonitor = authPerformanceMonitor;
}