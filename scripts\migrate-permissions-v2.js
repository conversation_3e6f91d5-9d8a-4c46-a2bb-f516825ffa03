#!/usr/bin/env node
/**
 * Permission System V2 Migration Script
 * 
 * Automatically migrates components from old permission system to V2
 * Following CLAUDE.md code quality guidelines
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// ============================================================================
// MIGRATION MAPPINGS
// ============================================================================

const PERMISSION_MAPPINGS = {
  // Page-level to resource-based
  'pages.orders_access': 'orders.view',
  'pages.products_access': 'products.view', 
  'pages.clients_access': 'clients.view',
  'pages.settings_access': 'settings.view',
  'pages.user_management_access': 'admin.users',
  'pages.analytics_overview_access': 'analytics.view',
  'pages.analytics_sales_access': 'analytics.view',
  'pages.analytics_production_access': 'analytics.view',
  'pages.analytics_general_access': 'analytics.view',
  'pages.production_cost_access': 'products.view',

  // Granular permissions consolidation
  'orders.general_info_edit': 'orders.edit',
  'orders.items_edit': 'orders.edit',
  'orders.payments_manage': 'orders.edit',
  'orders.status_update': 'orders.edit',
  'orders.notes_edit': 'orders.edit',
  
  'products.pricing_edit': 'products.edit',
  'products.view_costs': 'products.view',
  
  'clients.view_analytics': 'clients.view',
  
  'analytics.advanced_metrics': 'analytics.view',
  'analytics.custom_reports': 'analytics.export',
  
  'admin.system_settings': 'settings.edit',
  'admin.users_create': 'admin.users',
  'admin.user_management': 'admin.users',
  'admin.permissions_assign': 'admin.permissions',
  
  'system.full_access': 'system.admin'
};

const IMPORT_REPLACEMENTS = {
  "import { PermissionGuard } from '../../../components/permissions'": "import { PermissionGuardV2 } from '../../../components/permissions/PermissionGuardV2'",
  "import { PermissionGuard } from '../../components/permissions'": "import { PermissionGuardV2 } from '../../components/permissions/PermissionGuardV2'",
  "import { PermissionGuard } from '../components/permissions'": "import { PermissionGuardV2 } from '../components/permissions/PermissionGuardV2'",
  "import { usePermissions } from '../../../hooks/permissions'": "import { usePermissionsV2 } from '../../../hooks/permissions/usePermissionsV2'",
  "import { usePermissions } from '../../hooks/permissions'": "import { usePermissionsV2 } from '../../hooks/permissions/usePermissionsV2'",
  "import { usePermissions } from '../hooks/permissions'": "import { usePermissionsV2 } from '../hooks/permissions/usePermissionsV2'",
  "from '../../../utils/permissionHierarchy'": "// REMOVED: old permission hierarchy - using PermissionServiceV2",
  "from '../../utils/permissionHierarchy'": "// REMOVED: old permission hierarchy - using PermissionServiceV2",
  "from '../utils/permissionHierarchy'": "// REMOVED: old permission hierarchy - using PermissionServiceV2"
};

// ============================================================================
// MIGRATION FUNCTIONS
// ============================================================================

function findFilesToMigrate() {
  const patterns = [
    'src/**/*.tsx',
    'src/**/*.ts'
  ];
  
  const allFiles = [];
  patterns.forEach(pattern => {
    const files = glob.sync(pattern, { cwd: process.cwd() });
    allFiles.push(...files);
  });
  
  // Filter files that contain old permission patterns
  return allFiles.filter(file => {
    const content = fs.readFileSync(file, 'utf8');
    return (
      content.includes('hasPermissionWithHierarchy') ||
      content.includes('pages.') ||
      content.includes('permissionHierarchy') ||
      content.includes('PermissionGuard') ||
      content.includes('usePermissions') ||
      content.includes('system.full_access')
    );
  });
}

function migrateFileContent(content) {
  let migrated = content;
  
  // 1. Replace imports first
  Object.entries(IMPORT_REPLACEMENTS).forEach(([oldImport, newImport]) => {
    migrated = migrated.replace(new RegExp(escapeRegExp(oldImport), 'g'), newImport);
  });
  
  // 2. Replace permission strings
  Object.entries(PERMISSION_MAPPINGS).forEach(([oldPermission, newPermission]) => {
    // Replace in string literals
    migrated = migrated.replace(
      new RegExp(`['"\`]${escapeRegExp(oldPermission)}['"\`]`, 'g'), 
      `'${newPermission}'`
    );
  });
  
  // 3. Replace component names
  migrated = migrated.replace(/\bPermissionGuard\b(?!V2)/g, 'PermissionGuardV2');
  migrated = migrated.replace(/\busePermissions\b(?!V2)/g, 'usePermissionsV2');
  
  // 4. Replace function calls
  migrated = migrated.replace(/hasPermissionWithHierarchy\(/g, 'hasPermission(');
  
  // 5. Remove old permission hierarchy references
  migrated = migrated.replace(/import.*permissionHierarchy.*/g, '// REMOVED: old permission hierarchy');
  migrated = migrated.replace(/hasPermissionWithHierarchy\s*,?\s*/g, '');
  migrated = migrated.replace(/getEffectivePermissions\s*,?\s*/g, '');
  migrated = migrated.replace(/getIncludedCrudPermissions\s*,?\s*/g, '');
  
  return migrated;
}

function escapeRegExp(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function createMigrationReport() {
  const report = {
    timestamp: new Date().toISOString(),
    migratedFiles: [],
    errors: [],
    summary: {
      totalFiles: 0,
      successfulMigrations: 0,
      errors: 0,
      permissionsMapped: Object.keys(PERMISSION_MAPPINGS).length
    }
  };
  
  return report;
}

// ============================================================================
// MAIN MIGRATION LOGIC
// ============================================================================

function main() {
  console.log('🚀 Starting Permission System V2 Migration...\n');
  
  const report = createMigrationReport();
  const filesToMigrate = findFilesToMigrate();
  
  console.log(`Found ${filesToMigrate.length} files to migrate:\n`);
  
  filesToMigrate.forEach((file, index) => {
    try {
      console.log(`${index + 1}. Migrating: ${file}`);
      
      // Read original file
      const originalContent = fs.readFileSync(file, 'utf8');
      
      // Create backup
      const backupFile = `${file}.backup`;
      fs.writeFileSync(backupFile, originalContent);
      
      // Migrate content
      const migratedContent = migrateFileContent(originalContent);
      
      // Check if any changes were made
      if (originalContent !== migratedContent) {
        // Write migrated file
        fs.writeFileSync(file, migratedContent);
        
        report.migratedFiles.push({
          file,
          backupFile,
          changes: 'Updated permissions to V2 system'
        });
        
        console.log(`   ✅ Migrated successfully`);
        report.summary.successfulMigrations++;
      } else {
        // Remove backup if no changes
        fs.unlinkSync(backupFile);
        console.log(`   ⏭️  No changes needed`);
      }
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
      report.errors.push({
        file,
        error: error.message
      });
      report.summary.errors++;
    }
  });
  
  report.summary.totalFiles = filesToMigrate.length;
  
  // Write migration report
  fs.writeFileSync(
    'migration-report-v2.json',
    JSON.stringify(report, null, 2)
  );
  
  console.log('\n📊 Migration Summary:');
  console.log(`   Total files processed: ${report.summary.totalFiles}`);
  console.log(`   Successful migrations: ${report.summary.successfulMigrations}`);
  console.log(`   Errors: ${report.summary.errors}`);
  console.log(`   Permissions mapped: ${report.summary.permissionsMapped}`);
  
  if (report.summary.errors > 0) {
    console.log('\n❌ Migration completed with errors. Check migration-report-v2.json for details.');
    process.exit(1);
  } else {
    console.log('\n✅ Migration completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. Test the migrated components');
    console.log('   2. Run database migration');
    console.log('   3. Remove old permission files');
    console.log('   4. Update tests');
  }
}

// ============================================================================
// ROLLBACK FUNCTIONALITY
// ============================================================================

function rollback() {
  console.log('🔄 Rolling back Permission System V2 Migration...\n');
  
  const reportFile = 'migration-report-v2.json';
  
  if (!fs.existsSync(reportFile)) {
    console.log('❌ No migration report found. Cannot rollback.');
    process.exit(1);
  }
  
  const report = JSON.parse(fs.readFileSync(reportFile, 'utf8'));
  
  report.migratedFiles.forEach((migration, index) => {
    try {
      console.log(`${index + 1}. Rolling back: ${migration.file}`);
      
      if (fs.existsSync(migration.backupFile)) {
        // Restore from backup
        const backupContent = fs.readFileSync(migration.backupFile, 'utf8');
        fs.writeFileSync(migration.file, backupContent);
        
        // Remove backup file
        fs.unlinkSync(migration.backupFile);
        
        console.log(`   ✅ Rolled back successfully`);
      } else {
        console.log(`   ⚠️  Backup file not found: ${migration.backupFile}`);
      }
      
    } catch (error) {
      console.log(`   ❌ Error rolling back: ${error.message}`);
    }
  });
  
  console.log('\n✅ Rollback completed!');
}

// ============================================================================
// CLI INTERFACE
// ============================================================================

if (require.main === module) {
  const command = process.argv[2];
  
  switch (command) {
    case 'rollback':
      rollback();
      break;
    case 'migrate':
    default:
      main();
      break;
  }
}

module.exports = {
  main,
  rollback,
  PERMISSION_MAPPINGS,
  IMPORT_REPLACEMENTS
};