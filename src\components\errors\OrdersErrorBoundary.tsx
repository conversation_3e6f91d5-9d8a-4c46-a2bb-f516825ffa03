import React from 'react'
import { FeatureErrorBoundary } from '../ui/FeatureErrorBoundary'
import { Package } from 'lucide-react'

interface OrdersErrorBoundaryProps {
  children: React.ReactNode
}

/**
 * Error boundary specifically for the Orders feature
 */
export function OrdersErrorBoundary({ children }: OrdersErrorBoundaryProps) {
  return (
    <FeatureErrorBoundary
      featureName="Orders Management"
      fallback={
        <div className="flex items-center justify-center min-h-[400px] p-4">
          <div className="text-center space-y-4">
            <div className="mx-auto p-3 bg-blue-100 rounded-full w-fit">
              <Package className="h-8 w-8 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold mb-2">Orders System Unavailable</h2>
              <p className="text-muted-foreground mb-4">
                The orders management system is temporarily unavailable.
              </p>
              <button 
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Reload Application
              </button>
            </div>
          </div>
        </div>
      }
    >
      {children}
    </FeatureErrorBoundary>
  )
}