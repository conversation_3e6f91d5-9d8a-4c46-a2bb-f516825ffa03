import React, { useState } from 'react';
import { Input } from './input';
import { Label } from './label';
import { Eye, EyeOff } from 'lucide-react';
import { PasswordStrengthIndicator } from './password-strength-indicator';
import { PasswordMatchIndicator } from './password-match-indicator';

interface PasswordFieldProps {
  id: string;
  label: string;
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  onKeyPress?: (e: React.KeyboardEvent) => void;
  disabled?: boolean;
  required?: boolean;
  autoComplete?: string;
  showStrengthIndicator?: boolean;
  showMatchIndicator?: boolean;
  matchPassword?: string;
  className?: string;
  error?: string;
}

/**
 * Reusable PasswordField component with visibility toggle and optional indicators
 * Following CLAUDE.md guidelines - focused functionality under 250 lines
 */
export const PasswordField: React.FC<PasswordFieldProps> = ({
  id,
  label,
  placeholder = '',
  value,
  onChange,
  onKeyPress,
  disabled = false,
  required = false,
  autoComplete,
  showStrengthIndicator = false,
  showMatchIndicator = false,
  matchPassword,
  className = '',
  error
}) => {
  const [showPassword, setShowPassword] = useState(false);

  return (
    <div className={`space-y-2 ${className}`}>
      <Label htmlFor={id} className="text-black text-sm font-medium">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      
      <div className="relative">
        <Input
          id={id}
          type={showPassword ? "text" : "password"}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyPress={onKeyPress}
          placeholder={placeholder}
          className={`bg-gray-100 border-0 rounded-lg px-4 py-3 pr-12 text-gray-700 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-black transition-colors ${error ? 'ring-2 ring-red-500' : ''}`}
          disabled={disabled}
          required={required}
          autoComplete={autoComplete}
        />
        
        <button
          type="button"
          onClick={() => setShowPassword(!showPassword)}
          className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600"
          disabled={disabled}
        >
          {showPassword ? (
            <EyeOff className="h-4 w-4" />
          ) : (
            <Eye className="h-4 w-4" />
          )}
        </button>
      </div>

      {/* Error Message */}
      {error && (
        <p className="text-red-600 text-sm mb-0">{error}</p>
      )}

      {/* Password Strength Indicator */}
      {showStrengthIndicator && (
        <PasswordStrengthIndicator 
          password={value} 
          className="mt-3"
          showRequirements={true}
        />
      )}

      {/* Password Match Indicator */}
      {showMatchIndicator && matchPassword !== undefined && (
        <PasswordMatchIndicator 
          password={matchPassword}
          confirmPassword={value}
          debounceMs={600}
        />
      )}
    </div>
  );
};