-- Migration 016: Fix hard_delete_products_batch function
-- Fixes the component_value_history table deletion to use correct column

-- Drop and recreate the function with the fix
CREATE OR REPLACE FUNCTION hard_delete_products_batch(p_product_ids UUID[])
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    result JSONB;
    products_deleted INTEGER := 0;
    component_values_deleted INTEGER := 0;
    template_applications_deleted INTEGER := 0;
    calculation_results_deleted INTEGER := 0;
    value_history_deleted INTEGER := 0;
    product_record RECORD;
    combination_keys TEXT[] := ARRAY[]::TEXT[];
BEGIN
    -- Validate input
    IF array_length(p_product_ids, 1) IS NULL OR array_length(p_product_ids, 1) = 0 THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'No product IDs provided',
            'products_deleted', 0
        );
    END IF;

    -- Get product combination keys for related data deletion
    FOR product_record IN 
        SELECT id, category_id, product_type_id, size_id 
        FROM product_line 
        WHERE id = ANY(p_product_ids)
    LOOP
        combination_keys := array_append(combination_keys, 
            product_record.category_id::TEXT || '_' || 
            product_record.product_type_id::TEXT || '_' || 
            product_record.size_id::TEXT
        );
    END LOOP;

    -- Begin transaction (function is atomic by default)
    
    -- 1. Delete calculation results first (child table)
    DELETE FROM template_calculation_results 
    WHERE product_id = ANY(p_product_ids);
    
    GET DIAGNOSTICS calculation_results_deleted = ROW_COUNT;

    -- 2. Delete component value history using product_id (FIXED: was using wrong columns)
    DELETE FROM component_value_history
    WHERE product_id = ANY(p_product_ids);
    
    GET DIAGNOSTICS value_history_deleted = ROW_COUNT;

    -- 3. Delete component values using combination matching
    DELETE FROM production_cost_component_values pcv
    WHERE EXISTS (
        SELECT 1 FROM product_line pl 
        WHERE pl.id = ANY(p_product_ids)
        AND pcv.product_category_id = pl.category_id
        AND pcv.product_type_id = pl.product_type_id
        AND pcv.size_id = pl.size_id
    );
    
    GET DIAGNOSTICS component_values_deleted = ROW_COUNT;

    -- 4. Delete template applications using combination matching
    DELETE FROM template_applications ta
    WHERE EXISTS (
        SELECT 1 FROM product_line pl 
        WHERE pl.id = ANY(p_product_ids)
        AND ta.product_category_id = pl.category_id
        AND ta.product_type_id = pl.product_type_id
        AND ta.size_id = pl.size_id
    );
    
    GET DIAGNOSTICS template_applications_deleted = ROW_COUNT;

    -- 5. Finally, delete from product_line (parent table)
    DELETE FROM product_line 
    WHERE id = ANY(p_product_ids);
    
    GET DIAGNOSTICS products_deleted = ROW_COUNT;

    -- Build result
    result := jsonb_build_object(
        'success', true,
        'products_deleted', products_deleted,
        'product_line_deleted', products_deleted,
        'component_values_deleted', component_values_deleted,
        'template_applications_deleted', template_applications_deleted,
        'calculation_results_deleted', calculation_results_deleted,
        'value_history_deleted', value_history_deleted,
        'combination_keys_processed', array_length(combination_keys, 1)
    );

    RETURN result;

EXCEPTION WHEN OTHERS THEN
    -- Return error information
    RETURN jsonb_build_object(
        'success', false,
        'error', SQLERRM,
        'products_deleted', 0,
        'product_line_deleted', 0,
        'component_values_deleted', 0,
        'template_applications_deleted', 0,
        'calculation_results_deleted', 0,
        'value_history_deleted', 0
    );
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION hard_delete_products_batch(UUID[]) TO authenticated;

-- Update function description
COMMENT ON FUNCTION hard_delete_products_batch(UUID[]) IS 
'Permanently deletes products and ALL associated data. 
This includes product_line entries, component values, template applications, calculation results, and value history.
WARNING: This operation cannot be undone. Use soft deletion for recoverable operations.
FIXED: Now correctly handles component_value_history table deletion using product_id column.';