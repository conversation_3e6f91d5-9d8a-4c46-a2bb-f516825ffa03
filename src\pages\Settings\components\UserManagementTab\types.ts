import type { AuthorizedUser } from '../../../../services/admin'

export interface UserManagementTabProps {
  users: AuthorizedUser[]
  onRefresh: () => Promise<void>
  canCreate: boolean
  canEdit: boolean
  canDelete: boolean
}

export interface UserFilters {
  search: string
  activeFilter: boolean | null
}

export interface UserActions {
  onEdit: (user: AuthorizedUser) => void
  onToggleStatus: (user: AuthorizedUser) => void
  onDelete: (user: AuthorizedUser) => void
}