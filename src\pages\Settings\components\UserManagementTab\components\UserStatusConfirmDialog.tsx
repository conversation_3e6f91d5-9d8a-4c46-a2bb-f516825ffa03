import React from 'react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../../../../../components/ui/alert-dialog'
import { Badge } from '../../../../../components/ui/badge'
import { UserCheck, UserX, AlertTriangle } from 'lucide-react'
import type { AuthorizedUser } from '../../../../../services/admin'

interface UserStatusConfirmDialogProps {
  user: AuthorizedUser | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: (user: AuthorizedUser) => void
  isProcessing: boolean
}

export function UserStatusConfirmDialog({
  user,
  open,
  onOpenChange,
  onConfirm,
  isProcessing
}: UserStatusConfirmDialogProps) {
  if (!user) return null

  const newStatus = !user.is_active
  const actionType = newStatus ? 'activate' : 'deactivate'
  const ActionIcon = newStatus ? UserCheck : UserX

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <div className="flex items-center gap-3">
            <div className={`h-10 w-10 rounded-full flex items-center justify-center ${
              newStatus 
                ? 'bg-green-100 border-2 border-green-200' 
                : 'bg-red-100 border-2 border-red-200'
            }`}>
              <ActionIcon className={`h-5 w-5 ${newStatus ? 'text-green-600' : 'text-red-600'}`} />
            </div>
            <div>
              <AlertDialogTitle className="text-xl">
                {newStatus ? 'Activate' : 'Deactivate'} User Account
              </AlertDialogTitle>
            </div>
          </div>
        </AlertDialogHeader>
        
        <div className="space-y-4">
          <AlertDialogDescription className="text-base">
            {newStatus ? (
              <>
                You are about to <strong>activate</strong> the user account for{' '}
                <strong>{user.first_name} {user.last_name}</strong>. This will allow them to log in 
                and access the system again.
              </>
            ) : (
              <>
                You are about to <strong>deactivate</strong> the user account for{' '}
                <strong>{user.first_name} {user.last_name}</strong>. They will no longer be able to 
                log in or access the system.
              </>
            )}
          </AlertDialogDescription>

          {/* User Info Card */}
          <div className="bg-gray-50 rounded-lg p-4 space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">User</span>
              <span className="text-sm">{user.first_name} {user.last_name}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Email</span>
              <span className="text-sm">{user.email}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Current Status</span>
              <Badge variant={user.is_active ? 'success' : 'secondary'} className="text-xs">
                {user.is_active ? 'Active' : 'Inactive'}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Department</span>
              <span className="text-sm">{user.department || 'Not assigned'}</span>
            </div>
          </div>

          {/* Warning for deactivation */}
          {!newStatus && (
            <div className="flex items-start gap-3 p-4 bg-amber-50 border border-amber-200 rounded-lg">
              <AlertTriangle className="h-5 w-5 text-amber-600 flex-shrink-0 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-amber-800 mb-1">
                  Important Notice
                </p>
                <p className="text-sm text-amber-700 mb-0">
                  Deactivating this user will immediately prevent them from accessing the system. 
                  Any active sessions will be terminated.
                </p>
              </div>
            </div>
          )}

          {/* Success info for activation */}
          {newStatus && (
            <div className="flex items-start gap-3 p-4 bg-green-50 border border-green-200 rounded-lg">
              <UserCheck className="h-5 w-5 text-green-600 flex-shrink-0 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-green-800 mb-1">
                  User Activation
                </p>
                <p className="text-sm text-green-700 mb-0">
                  This user will be able to log in and access the system according to their 
                  assigned permissions.
                </p>
              </div>
            </div>
          )}
        </div>

        <AlertDialogFooter>
          <AlertDialogCancel disabled={isProcessing}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={() => onConfirm(user)}
            disabled={isProcessing}
            className={newStatus 
              ? 'bg-green-600 hover:bg-green-700 text-white' 
              : 'bg-red-600 hover:bg-red-700 text-white'
            }
          >
            {isProcessing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                {newStatus ? 'Activating...' : 'Deactivating...'}
              </>
            ) : (
              <>
                <ActionIcon className="h-4 w-4 mr-2" />
                {newStatus ? 'Activate User' : 'Deactivate User'}
              </>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}