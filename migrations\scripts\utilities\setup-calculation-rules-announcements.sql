-- Quick Setup Script: Calculation Rules Announcements System
-- Run this script to set up and test the calculation rules error announcement system

-- ============================================
-- Test the system with a sample calculation rules error
-- ============================================

-- Test 1: Simulate missing calculation rule scenario
DO $$
DECLARE
    v_error_id UUID;
    v_announcement_id UUID;
BEGIN
    -- Create a test error entry
    INSERT INTO calculation_rules_errors (
        error_type,
        error_message,
        severity,
        product_category,
        expected_rule_type,
        available_rules,
        quantity,
        nos
    ) VALUES (
        'missing_calculation_rule',
        'No calculation rule found for category: Test Category',
        'high',
        'Test Category',
        'product_category',
        jsonb_build_array(
            jsonb_build_object('product_category', 'Photo Boards', 'rule_type', 'multiply_by_quantity'),
            jsonb_build_object('product_category', 'Photo Books', 'rule_type', 'multiply_by_quantity_and_nos')
        ),
        1,
        1
    ) RETURNING id INTO v_error_id;

    -- Create announcement for this error
    SELECT create_calculation_rules_error_announcement(
        v_error_id,
        'Test Category',
        'missing_calculation_rule',
        'Create calculation rule for Test Category in Product settings'
    ) INTO v_announcement_id;

    RAISE NOTICE 'Test setup complete:';
    RAISE NOTICE 'Error ID: %', v_error_id;
    RAISE NOTICE 'Announcement ID: %', v_announcement_id;
END;
$$;

-- ============================================
-- Verify the setup
-- ============================================

-- Check created announcements
SELECT 
    id,
    title,
    message,
    type,
    priority,
    category,
    is_active,
    target_audience,
    error_context->>'product_category' as affected_category,
    created_at
FROM system_announcements 
WHERE category = 'calculation_rules'
ORDER BY created_at DESC;

-- Check error log
SELECT 
    id,
    error_type,
    product_category,
    error_message,
    severity,
    resolution_status,
    announcement_id IS NOT NULL as has_announcement,
    created_at
FROM calculation_rules_errors
ORDER BY created_at DESC;

-- ============================================
-- Test cleanup function
-- ============================================

-- Test expired announcements cleanup
SELECT cleanup_expired_announcements() as cleaned_announcements_count;

-- ============================================
-- View active announcements (what frontend will see)
-- ============================================

SELECT 
    id,
    title,
    message,
    type,
    priority,
    'admin' = ANY(target_audience) as visible_to_admin,
    display_until,
    is_active,
    auto_dismiss_after_seconds,
    view_count,
    dismiss_count
FROM system_announcements 
WHERE is_active = true 
  AND (display_until IS NULL OR display_until > NOW())
  AND 'admin' = ANY(target_audience)
ORDER BY 
    CASE priority 
        WHEN 'critical' THEN 1
        WHEN 'high' THEN 2  
        WHEN 'medium' THEN 3
        ELSE 4
    END,
    created_at DESC;