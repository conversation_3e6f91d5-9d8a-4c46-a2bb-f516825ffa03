import { supabase } from '../lib/supabase'
import type { PermissionKey } from '../types/permissions.types'

// ============================================================================
// TYPES
// ============================================================================

export interface AuthorizedUser {
  readonly id: string
  readonly email: string
  readonly first_name: string
  readonly last_name: string
  readonly department?: string | null
  readonly permissions: readonly string[]
  readonly role_template?: string | null
  readonly is_active: boolean
  readonly invited_by?: string | null
  readonly invited_at: string
  readonly first_login_at?: string | null
  readonly last_login_at?: string | null
  readonly notes?: string | null
  readonly created_at: string
  readonly updated_at: string
}

export interface CreateUserRequest {
  readonly email: string
  readonly first_name: string
  readonly last_name: string
  readonly department?: string
  readonly permissions: readonly string[]
  readonly role_template?: string
  readonly notes?: string
}

export interface UpdateUserRequest {
  readonly first_name?: string
  readonly last_name?: string
  readonly department?: string
  readonly permissions?: readonly string[]
  readonly role_template?: string
  readonly is_active?: boolean
  readonly notes?: string
}

export interface UserStats {
  readonly total_users: number
  readonly active_users: number
  readonly inactive_users: number
  readonly recent_logins: number
}

// ============================================================================
// ADMIN SERVICES
// ============================================================================

export class AdminService {
  
  /**
   * Get all authorized users with filtering and pagination
   */
  static async getUsers({
    limit = 50,
    offset = 0,
    search,
    role_template,
    is_active,
    department
  }: {
    limit?: number
    offset?: number
    search?: string
    role_template?: string
    is_active?: boolean
    department?: string
  } = {}): Promise<{ users: AuthorizedUser[]; totalCount: number }> {
    let query = supabase
      .from('authorized_users')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false })

    // Apply filters
    if (search) {
      query = query.or(`email.ilike.%${search}%,first_name.ilike.%${search}%,last_name.ilike.%${search}%`)
    }
    
    if (role_template) {
      query = query.eq('role_template', role_template)
    }
    
    if (typeof is_active === 'boolean') {
      query = query.eq('is_active', is_active)
    }
    
    if (department) {
      query = query.eq('department', department)
    }

    // Apply pagination
    if (limit && offset !== undefined) {
      query = query.range(offset, offset + limit - 1)
    }

    const { data, error, count } = await query

    if (error) {
      throw new Error(`Failed to fetch users: ${error.message}`)
    }

    return {
      users: (data || []) as AuthorizedUser[],
      totalCount: count || 0
    }
  }

  /**
   * Get user statistics for dashboard
   */
  static async getUserStats(): Promise<UserStats> {
    const [totalResult, activeResult, recentResult] = await Promise.all([
      supabase
        .from('authorized_users')
        .select('id', { count: 'exact', head: true }),
      
      supabase
        .from('authorized_users')
        .select('id', { count: 'exact', head: true })
        .eq('is_active', true),
      
      supabase
        .from('authorized_users')
        .select('id', { count: 'exact', head: true })
        .gte('last_login_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
    ])

    if (totalResult.error || activeResult.error || recentResult.error) {
      throw new Error('Failed to fetch user statistics')
    }

    const total = totalResult.count || 0
    const active = activeResult.count || 0

    return {
      total_users: total,
      active_users: active,
      inactive_users: total - active,
      recent_logins: recentResult.count || 0
    }
  }

  /**
   * Create a new authorized user
   */
  static async createUser(userData: CreateUserRequest, invitedBy?: string): Promise<AuthorizedUser> {
    const { data, error } = await supabase
      .from('authorized_users')
      .insert({
        email: userData.email,
        first_name: userData.first_name,
        last_name: userData.last_name,
        department: userData.department || null,
        permissions: JSON.stringify(userData.permissions),
        role_template: userData.role_template || null,
        notes: userData.notes || null,
        invited_by: invitedBy || null,
        is_active: true
      })
      .select()
      .single()

    if (error) {
      if (error.code === '23505') { // Unique constraint violation
        throw new Error('A user with this email already exists')
      }
      throw new Error(`Failed to create user: ${error.message}`)
    }

    return data as AuthorizedUser
  }

  /**
   * Update an existing user
   */
  static async updateUser(userId: string, updates: UpdateUserRequest): Promise<AuthorizedUser> {
    const updateData: any = { ...updates }
    
    // Handle permissions array serialization
    if (updates.permissions) {
      updateData.permissions = JSON.stringify(updates.permissions)
    }

    const { data, error } = await supabase
      .from('authorized_users')
      .update(updateData)
      .eq('id', userId)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update user: ${error.message}`)
    }

    return data as AuthorizedUser
  }

  /**
   * Delete a user (soft delete by deactivating)
   */
  static async deactivateUser(userId: string): Promise<void> {
    const { error } = await supabase
      .from('authorized_users')
      .update({ is_active: false })
      .eq('id', userId)

    if (error) {
      throw new Error(`Failed to deactivate user: ${error.message}`)
    }
  }

  /**
   * Reactivate a user
   */
  static async reactivateUser(userId: string): Promise<void> {
    const { error } = await supabase
      .from('authorized_users')
      .update({ is_active: true })
      .eq('id', userId)

    if (error) {
      throw new Error(`Failed to reactivate user: ${error.message}`)
    }
  }

  /**
   * Get all available permissions
   */
  static async getPermissions(): Promise<Array<{
    key: string
    name: string
    description: string | null
    category: string
  }>> {
    const { data, error } = await supabase
      .from('permissions')
      .select('key, name, description, category')
      .eq('is_active', true)
      .order('category, name')

    if (error) {
      throw new Error(`Failed to fetch permissions: ${error.message}`)
    }

    return data || []
  }

  /**
   * Get user activity/audit log
   */
  static async getUserActivity(userId?: string, limit = 50): Promise<any[]> {
    // Note: This would require implementing an audit log system
    // For now, return empty array as placeholder
    return []
  }
}