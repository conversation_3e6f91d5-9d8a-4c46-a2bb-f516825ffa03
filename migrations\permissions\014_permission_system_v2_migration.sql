-- ============================================================================
-- PERMISSION SYSTEM V2 MIGRATION
-- Migration: 042_permission_system_v2_migration.sql
-- 
-- Migrates from over-engineered permission system to production-ready v2
-- Based on industry standards and performance requirements
-- ============================================================================

-- Create backup tables for rollback capability
CREATE TABLE permissions_v1_backup AS SELECT * FROM permissions;
CREATE TABLE roles_v1_backup AS SELECT * FROM roles;
CREATE TABLE authorized_users_v1_backup AS SELECT * FROM authorized_users;

-- ============================================================================
-- STEP 1: UPDATE PERMISSIONS TABLE
-- ============================================================================

-- Clear existing permissions and insert new standardized set
DELETE FROM permissions;

-- Insert 19 production-ready permissions (resource.action format)
INSERT INTO permissions (key, name, description, category, is_active) VALUES
-- Orders Resource (4 permissions)
('orders.view', 'View Orders', 'View order information and history', 'orders', TRUE),
('orders.create', 'Create Orders', 'Create new orders in the system', 'orders', TRUE), 
('orders.edit', 'Edit Orders', 'Modify existing orders and their details', 'orders', TRUE),
('orders.delete', 'Delete Orders', 'Permanently delete orders from system', 'orders', TRUE),

-- Products Resource (4 permissions)
('products.view', 'View Products', 'View product catalog and details', 'products', TRUE),
('products.create', 'Create Products', 'Add new products to catalog', 'products', TRUE),
('products.edit', 'Edit Products', 'Modify existing products and pricing', 'products', TRUE),
('products.delete', 'Delete Products', 'Permanently remove products', 'products', TRUE),

-- Clients Resource (4 permissions)
('clients.view', 'View Clients', 'View client information and history', 'clients', TRUE),
('clients.create', 'Create Clients', 'Add new client records', 'clients', TRUE),
('clients.edit', 'Edit Clients', 'Modify existing client information', 'clients', TRUE),
('clients.delete', 'Delete Clients', 'Permanently delete client records', 'clients', TRUE),

-- Analytics Resource (2 permissions)
('analytics.view', 'View Analytics', 'Access reports and analytics dashboards', 'analytics', TRUE),
('analytics.export', 'Export Analytics', 'Export analytics data and reports', 'analytics', TRUE),

-- Settings Resource (2 permissions)
('settings.view', 'View Settings', 'Access system settings and configuration', 'settings', TRUE),
('settings.edit', 'Edit Settings', 'Modify system settings and configuration', 'settings', TRUE),

-- Admin Resource (3 permissions)
('admin.users', 'Manage Users', 'Create and manage user accounts', 'admin', TRUE),
('admin.permissions', 'Assign Permissions', 'Assign permissions and roles to users', 'admin', TRUE),
('system.admin', 'System Administrator', 'Full administrative access to all system functions', 'system', TRUE);

-- ============================================================================
-- STEP 2: UPDATE ROLES TABLE 
-- ============================================================================

-- Clear existing roles and create 4 business-aligned roles
DELETE FROM roles;

-- Insert new role templates aligned with business functions
INSERT INTO roles (id, name, display_name, description, permissions, is_active, is_system_role) VALUES
(1, 'viewer', 'Viewer', 'Read-only access to core business data', 
 '["orders.view", "products.view", "clients.view", "analytics.view"]'::jsonb, TRUE, FALSE),

(2, 'operator', 'Operator', 'Daily operations staff - can create and edit core records',
 '["orders.view", "orders.create", "orders.edit", "products.view", "clients.view", "clients.create", "clients.edit", "analytics.view"]'::jsonb, TRUE, FALSE),

(3, 'manager', 'Manager', 'Management access - full business operations with delete permissions',
 '["orders.view", "orders.create", "orders.edit", "orders.delete", "products.view", "products.create", "products.edit", "clients.view", "clients.create", "clients.edit", "clients.delete", "analytics.view", "analytics.export", "settings.view"]'::jsonb, TRUE, FALSE),

(4, 'admin', 'Administrator', 'Full system administration access',
 '["system.admin"]'::jsonb, TRUE, TRUE);

-- ============================================================================
-- STEP 3: MIGRATE USER PERMISSIONS
-- ============================================================================

-- Create migration function to update user permissions
CREATE OR REPLACE FUNCTION migrate_user_permissions_v2()
RETURNS TABLE(user_id uuid, old_role text, new_role text, old_perms jsonb, new_perms jsonb) AS $$
DECLARE
    user_record RECORD;
    old_permissions jsonb;
    new_role_name text := 'viewer';
    new_permissions jsonb := '[]'::jsonb;
BEGIN
    FOR user_record IN SELECT * FROM authorized_users WHERE is_active = TRUE LOOP
        old_permissions := user_record.permissions;
        new_role_name := 'viewer';
        new_permissions := '[]'::jsonb;
        
        -- Migration logic based on current permissions
        IF old_permissions ? 'system.full_access' THEN
            -- System admin users
            new_role_name := 'admin';
            new_permissions := '["system.admin"]'::jsonb;
            
        ELSIF jsonb_array_length(old_permissions) >= 8 THEN
            -- Users with many permissions -> Manager
            new_role_name := 'manager';
            new_permissions := '[]'::jsonb; -- Will inherit from role
            
        ELSIF old_permissions ? 'orders.create' OR old_permissions ? 'clients.create' THEN
            -- Users who can create -> Operator
            new_role_name := 'operator';
            new_permissions := '[]'::jsonb; -- Will inherit from role
            
        ELSE
            -- Default to viewer
            new_role_name := 'viewer';
            new_permissions := '[]'::jsonb; -- Will inherit from role
        END IF;
        
        -- Return migration info for logging
        user_id := user_record.id;
        old_role := user_record.role_template;
        new_role := new_role_name;
        old_perms := old_permissions;
        new_perms := new_permissions;
        
        -- Update the user record
        UPDATE authorized_users 
        SET 
            role_template = new_role_name,
            permissions = new_permissions,
            updated_at = NOW()
        WHERE id = user_record.id;
        
        RETURN NEXT;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Execute migration and log results
CREATE TEMP TABLE migration_results AS 
SELECT * FROM migrate_user_permissions_v2();

-- Log migration results
INSERT INTO user_audit_logs (user_id, action, details, created_at)
SELECT 
    user_id,
    'permission_migration_v2',
    jsonb_build_object(
        'old_role', old_role,
        'new_role', new_role,
        'old_permissions', old_perms,
        'new_permissions', new_perms,
        'migration_date', NOW()
    ),
    NOW()
FROM migration_results;

-- ============================================================================
-- STEP 4: ADD PERFORMANCE INDEXES
-- ============================================================================

-- Add indexes for optimal permission checking performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_permissions_category_v2 
    ON permissions(category, is_active) 
    WHERE is_active = TRUE;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_authorized_users_role_v2 
    ON authorized_users(role_template, is_active) 
    WHERE is_active = TRUE;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_roles_name_active_v2 
    ON roles(name, is_active) 
    WHERE is_active = TRUE;

-- GIN index for permission array searches
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_roles_permissions_gin_v2 
    ON roles USING GIN(permissions);

-- ============================================================================
-- STEP 5: CREATE PERMISSION VALIDATION FUNCTIONS
-- ============================================================================

-- Function to validate permission format
CREATE OR REPLACE FUNCTION is_valid_permission_format(permission_key text)
RETURNS boolean AS $$
BEGIN
    -- Validate resource.action format
    RETURN permission_key ~ '^[a-z]+\.[a-z]+$' OR permission_key = 'system.admin';
END;
$$ LANGUAGE plpgsql;

-- Function to get effective user permissions (role + overrides)
CREATE OR REPLACE FUNCTION get_user_effective_permissions(user_id uuid)
RETURNS jsonb AS $$
DECLARE
    user_record RECORD;
    role_permissions jsonb := '[]'::jsonb;
    effective_permissions jsonb := '[]'::jsonb;
BEGIN
    -- Get user and their role
    SELECT au.role_template, au.permissions, r.permissions as role_perms
    INTO user_record
    FROM authorized_users au
    LEFT JOIN roles r ON r.name = au.role_template AND r.is_active = TRUE
    WHERE au.id = user_id AND au.is_active = TRUE;
    
    IF NOT FOUND THEN
        RETURN '[]'::jsonb;
    END IF;
    
    -- Start with role permissions
    role_permissions := COALESCE(user_record.role_perms, '[]'::jsonb);
    
    -- Add user-specific permission overrides
    effective_permissions := role_permissions;
    
    IF user_record.permissions IS NOT NULL AND jsonb_array_length(user_record.permissions) > 0 THEN
        effective_permissions := effective_permissions || user_record.permissions;
    END IF;
    
    RETURN effective_permissions;
END;
$$ LANGUAGE plpgsql;

-- Function to check if user has specific permission
CREATE OR REPLACE FUNCTION user_has_permission(user_id uuid, required_permission text)
RETURNS boolean AS $$
DECLARE
    user_permissions jsonb;
BEGIN
    user_permissions := get_user_effective_permissions(user_id);
    
    -- Check for system admin (grants everything)
    IF user_permissions ? 'system.admin' THEN
        RETURN TRUE;
    END IF;
    
    -- Check for specific permission
    RETURN user_permissions ? required_permission;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- STEP 6: UPDATE TABLE COMMENTS AND DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE permissions IS 'V2 Permission registry - resource.action format for clear authorization';
COMMENT ON TABLE roles IS 'V2 Business-aligned role templates with JSON permission arrays';
COMMENT ON FUNCTION get_user_effective_permissions IS 'Returns combined permissions from role template + user overrides';
COMMENT ON FUNCTION user_has_permission IS 'Server-side permission check function for RLS policies';

-- ============================================================================
-- STEP 7: MIGRATION VERIFICATION
-- ============================================================================

DO $$
DECLARE
    permission_count INTEGER;
    role_count INTEGER;
    migrated_users INTEGER;
BEGIN
    -- Verify permissions
    SELECT COUNT(*) INTO permission_count FROM permissions WHERE is_active = TRUE;
    IF permission_count != 19 THEN
        RAISE EXCEPTION 'Expected 19 permissions, found %', permission_count;
    END IF;
    
    -- Verify roles  
    SELECT COUNT(*) INTO role_count FROM roles WHERE is_active = TRUE;
    IF role_count != 4 THEN
        RAISE EXCEPTION 'Expected 4 roles, found %', role_count;
    END IF;
    
    -- Verify user migrations
    SELECT COUNT(*) INTO migrated_users FROM authorized_users WHERE role_template IS NOT NULL;
    
    RAISE NOTICE 'Permission System V2 Migration Completed Successfully:';
    RAISE NOTICE '- % permissions created', permission_count;
    RAISE NOTICE '- % roles created', role_count;  
    RAISE NOTICE '- % users migrated', migrated_users;
    RAISE NOTICE '- Performance indexes created';
    RAISE NOTICE '- Validation functions deployed';
END $$;

-- Clean up migration function
DROP FUNCTION IF EXISTS migrate_user_permissions_v2();

-- ============================================================================
-- ROLLBACK INSTRUCTIONS (For Emergency Use Only)
-- ============================================================================

/*
-- EMERGENCY ROLLBACK SCRIPT:
-- Only use if critical issues are discovered

DELETE FROM permissions;
INSERT INTO permissions SELECT * FROM permissions_v1_backup;

DELETE FROM roles; 
INSERT INTO roles SELECT * FROM roles_v1_backup;

UPDATE authorized_users SET 
    permissions = b.permissions,
    role_template = b.role_template,
    updated_at = NOW()
FROM authorized_users_v1_backup b
WHERE authorized_users.id = b.id;

DROP TABLE IF EXISTS permissions_v1_backup;
DROP TABLE IF EXISTS roles_v1_backup;
DROP TABLE IF EXISTS authorized_users_v1_backup;
*/

-- ============================================================================
-- END OF MIGRATION
-- ============================================================================