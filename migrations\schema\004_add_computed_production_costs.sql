-- Migration: Add computed production cost column and triggers
-- This migration adds a computed column for total_production_cost to improve performance and consistency

-- Step 1: Add the computed column to product_line table
ALTER TABLE product_line 
ADD COLUMN IF NOT EXISTS total_production_cost DECIMAL(10,2) DEFAULT 0.00;

-- Step 2: Add index for better performance on production cost queries
CREATE INDEX IF NOT EXISTS idx_product_line_total_production_cost 
ON product_line(total_production_cost);

-- Step 3: Add updated_at trigger for production cost changes
CREATE OR REPLACE FUNCTION update_product_line_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply timestamp trigger to product_line table
DROP TRIGGER IF EXISTS trigger_product_line_updated_at ON product_line;
CREATE TRIGGER trigger_product_line_updated_at
  BEFORE UPDATE ON product_line
  FOR EACH ROW
  EXECUTE FUNCTION update_product_line_timestamp();

-- Step 4: Create function to calculate production cost for a product combination
CREATE OR REPLACE FUNCTION calculate_production_cost(
  p_category_id UUID,
  p_product_type_id UUID, 
  p_size_id UUID
) RETURNS DECIMAL(10,2) AS $$
DECLARE
  total_cost DECIMAL(10,2) := 0.00;
  template_record RECORD;
  component_cost DECIMAL(10,2);
BEGIN
  -- Get all active templates applied to this product combination
  FOR template_record IN
    SELECT 
      ct.id as template_id,
      ct.selected_components,
      ct.calculation_method,
      ta.applied_at
    FROM calculation_templates ct
    JOIN template_applications ta ON ta.template_id = ct.id
    WHERE ta.product_category_id = p_category_id
      AND ta.product_type_id = p_product_type_id
      AND ta.size_id = p_size_id
      AND ta.is_active = true
      AND ct.status = 'active'
    ORDER BY ta.applied_at ASC
  LOOP
    -- For each template, sum up the component costs
    IF template_record.selected_components IS NOT NULL THEN
      -- Sum component values for this template's selected components
      SELECT COALESCE(SUM(pcv.value), 0) INTO component_cost
      FROM production_cost_component_values pcv
      JOIN production_cost_components pcc ON pcc.id = pcv.component_id
      WHERE pcv.product_category_id = p_category_id
        AND pcv.product_type_id = p_product_type_id
        AND pcv.size_id = p_size_id
        AND pcv.is_current = true
        AND pcc.status = 'active'
        AND pcc.code = ANY(template_record.selected_components);
      
      -- Add this template's cost to total (simple sum method)
      total_cost := total_cost + component_cost;
    END IF;
  END LOOP;
  
  RETURN total_cost;
END;
$$ LANGUAGE plpgsql;

-- Step 5: Create trigger function to automatically update production costs
CREATE OR REPLACE FUNCTION update_production_costs()
RETURNS TRIGGER AS $$
DECLARE
  affected_product RECORD;
BEGIN
  -- Handle different trigger scenarios
  CASE TG_TABLE_NAME
    WHEN 'production_cost_component_values' THEN
      -- Update costs for the specific product combination
      UPDATE product_line 
      SET total_production_cost = calculate_production_cost(
        COALESCE(NEW.product_category_id, OLD.product_category_id),
        COALESCE(NEW.product_type_id, OLD.product_type_id),
        COALESCE(NEW.size_id, OLD.size_id)
      )
      WHERE category_id = COALESCE(NEW.product_category_id, OLD.product_category_id)
        AND product_type_id = COALESCE(NEW.product_type_id, OLD.product_type_id)
        AND size_id = COALESCE(NEW.size_id, OLD.size_id);
        
    WHEN 'template_applications' THEN
      -- Update costs for the specific product combination
      UPDATE product_line 
      SET total_production_cost = calculate_production_cost(
        COALESCE(NEW.product_category_id, OLD.product_category_id),
        COALESCE(NEW.product_type_id, OLD.product_type_id),
        COALESCE(NEW.size_id, OLD.size_id)
      )
      WHERE category_id = COALESCE(NEW.product_category_id, OLD.product_category_id)
        AND product_type_id = COALESCE(NEW.product_type_id, OLD.product_type_id)
        AND size_id = COALESCE(NEW.size_id, OLD.size_id);
        
    WHEN 'calculation_templates' THEN
      -- If template selected_components changed, update all products using this template
      IF (NEW.selected_components IS DISTINCT FROM OLD.selected_components) THEN
        FOR affected_product IN
          SELECT DISTINCT ta.product_category_id, ta.product_type_id, ta.size_id
          FROM template_applications ta
          WHERE ta.template_id = NEW.id AND ta.is_active = true
        LOOP
          UPDATE product_line 
          SET total_production_cost = calculate_production_cost(
            affected_product.product_category_id,
            affected_product.product_type_id,
            affected_product.size_id
          )
          WHERE category_id = affected_product.product_category_id
            AND product_type_id = affected_product.product_type_id
            AND size_id = affected_product.size_id;
        END LOOP;
      END IF;
  END CASE;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Step 6: Create triggers for automatic cost updates
DROP TRIGGER IF EXISTS trigger_update_production_costs_on_component_values ON production_cost_component_values;
CREATE TRIGGER trigger_update_production_costs_on_component_values
  AFTER INSERT OR UPDATE OR DELETE ON production_cost_component_values
  FOR EACH ROW 
  EXECUTE FUNCTION update_production_costs();

DROP TRIGGER IF EXISTS trigger_update_production_costs_on_template_apps ON template_applications;
CREATE TRIGGER trigger_update_production_costs_on_template_apps
  AFTER INSERT OR UPDATE OR DELETE ON template_applications
  FOR EACH ROW 
  EXECUTE FUNCTION update_production_costs();

DROP TRIGGER IF EXISTS trigger_update_production_costs_on_templates ON calculation_templates;
CREATE TRIGGER trigger_update_production_costs_on_templates
  AFTER UPDATE ON calculation_templates
  FOR EACH ROW 
  EXECUTE FUNCTION update_production_costs();

-- Step 7: Initial population of existing production costs
-- This will calculate and populate costs for all existing product combinations
UPDATE product_line 
SET total_production_cost = calculate_production_cost(category_id, product_type_id, size_id)
WHERE is_active = true;

-- Step 8: Add comment for documentation
COMMENT ON COLUMN product_line.total_production_cost IS 'Computed column: Sum of all component costs from applied templates. Automatically updated via triggers.';

-- Step 9: Create index for better query performance
CREATE INDEX IF NOT EXISTS idx_product_line_cost_lookup 
ON product_line(category_id, product_type_id, size_id, total_production_cost);

-- Migration completed successfully
-- This migration adds:
-- 1. total_production_cost computed column
-- 2. Automatic calculation function
-- 3. Triggers for real-time updates
-- 4. Initial population of existing data
-- 5. Performance indexes