# Fix Migrations

Bug fixes, data corruption repairs, and integrity corrections.

## Files in this directory:

*Note: This directory is currently empty but ready for future fix migrations.*

## Purpose

This directory will contain migrations that:
- Fix data corruption issues
- Repair integrity constraints
- Correct calculation errors
- Resolve security vulnerabilities
- Address performance bottlenecks

## Naming Convention

Fix migrations should follow the pattern: `###_fix_[description].sql`

Examples:
- `050_fix_order_total_calculation.sql`
- `051_fix_category_corruption_and_safeguards.sql`
- `052_fix_item_notes_jsonb_structure.sql`

## Execution Guidelines

- **Priority**: Fix migrations should be executed as soon as possible
- **Testing**: Always test fixes in development environment first
- **Backup**: Create database backups before applying fixes
- **Validation**: Verify that fixes resolve the intended issues

## Documentation Required

Each fix migration should include:
- Description of the bug being fixed
- Root cause analysis (in comments)
- Expected impact of the fix
- Validation queries to verify the fix

## Dependencies

- Fix migrations may have dependencies on specific schema versions
- Some fixes may require specific data states
- Document all dependencies in migration comments

## Notes

- Fix migrations are critical for system stability
- May need to be applied out of sequence (based on urgency)
- Keep detailed logs of all fixes applied
- Consider creating rollback procedures for complex fixes