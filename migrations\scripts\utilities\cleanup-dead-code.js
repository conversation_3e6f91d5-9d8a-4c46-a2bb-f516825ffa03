#!/usr/bin/env node
/**
 * Dead Code Cleanup Script
 * 
 * Identifies and removes unused permission files following CLAUDE.md guidelines
 * Ensures no dead code remains in the codebase
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// ============================================================================
// FILES TO REMOVE (Dead Code)
// ============================================================================

const DEAD_CODE_FILES = [
  // Old permission system files
  'src/utils/permissionHierarchy.ts',
  'src/utils/permissionFormatter.ts',
  'src/services/permissions/permissionRegistry.service.ts',
  'src/hooks/permissions/usePermissionCheck.ts',
  'src/services/admin/permissionQueries.ts',
  
  // Old components (will be replaced by V2)
  'src/components/permissions/PermissionGuard/PermissionGuard.tsx',
  'src/components/permissions/PermissionWrapper/PermissionWrapper.tsx',
  'src/components/ui/quick-permissions-select.tsx',
  'src/components/ui/PermissionsDisplay.tsx',
  
  // Old test files
  'src/tests/permissionHierarchy.test.ts',
  'tests/scripts/test-hierarchical-permissions.js',
  
  // Old documentation (replaced by v2 docs)
  'docs/permissions-architecture-upgrade.md',
  'docs/auth/implementation/permissions-implementation-roadmap.md',
  'docs/auth/implementation/permissions-first-analysis.md',
  'docs/auth/implementation/permissions-system-design/implementation-strategy.md'
];

// ============================================================================
// FUNCTIONS TO REMOVE FROM EXISTING FILES
// ============================================================================

const DEAD_CODE_PATTERNS = [
  // Unused imports
  "import.*permissionHierarchy.*",
  "import.*permissionFormatter.*",
  "import.*usePermissionCheck.*",
  "import.*PermissionRegistry.*",
  
  // Unused functions
  "hasPermissionWithHierarchy",
  "getEffectivePermissions", 
  "getIncludedCrudPermissions",
  "PAGE_PERMISSION_INCLUDES",
  "VIEW_ONLY_OVERRIDES",
  
  // Old permission constants
  "pages\\.[a-z_]+_access"
];

// ============================================================================
// DEAD CODE ANALYSIS
// ============================================================================

function analyzeDeadCode() {
  console.log('🔍 Analyzing dead code in permission system...\n');
  
  const analysis = {
    filesToRemove: [],
    filesToClean: [],
    deadCodeSize: 0,
    errors: []
  };
  
  // 1. Check files that should be removed entirely
  DEAD_CODE_FILES.forEach(file => {
    if (fs.existsSync(file)) {
      const stats = fs.statSync(file);
      analysis.filesToRemove.push({
        file,
        size: stats.size,
        lastModified: stats.mtime
      });
      analysis.deadCodeSize += stats.size;
    }
  });
  
  // 2. Check files that need cleaning
  const allFiles = glob.sync('src/**/*.{ts,tsx}');
  
  allFiles.forEach(file => {
    try {
      const content = fs.readFileSync(file, 'utf8');
      let hasDeadCode = false;
      const deadCodeFound = [];
      
      DEAD_CODE_PATTERNS.forEach(pattern => {
        const regex = new RegExp(pattern, 'g');
        const matches = content.match(regex);
        if (matches) {
          hasDeadCode = true;
          deadCodeFound.push(...matches);
        }
      });
      
      if (hasDeadCode) {
        analysis.filesToClean.push({
          file,
          deadCode: deadCodeFound,
          lineCount: content.split('\n').length
        });
      }
      
    } catch (error) {
      analysis.errors.push({
        file,
        error: error.message
      });
    }
  });
  
  return analysis;
}

// ============================================================================
// DEAD CODE REMOVAL
// ============================================================================

function removeDeadCode(analysis) {
  console.log('🧹 Removing dead code...\n');
  
  let removedFiles = 0;
  let cleanedFiles = 0;
  
  // 1. Remove entire files
  analysis.filesToRemove.forEach(({ file, size }) => {
    try {
      // Create backup first
      const backupFile = `${file}.backup`;
      fs.copyFileSync(file, backupFile);
      
      // Remove the file
      fs.unlinkSync(file);
      
      console.log(`🗑️  Removed: ${file} (${(size / 1024).toFixed(2)}KB)`);
      removedFiles++;
      
    } catch (error) {
      console.log(`❌ Error removing ${file}: ${error.message}`);
    }
  });
  
  // 2. Clean files with dead code patterns
  analysis.filesToClean.forEach(({ file, deadCode }) => {
    try {
      let content = fs.readFileSync(file, 'utf8');
      let modified = false;
      
      // Create backup
      const backupFile = `${file}.cleanup.backup`;
      fs.writeFileSync(backupFile, content);
      
      // Remove dead code patterns
      DEAD_CODE_PATTERNS.forEach(pattern => {
        const regex = new RegExp(pattern, 'g');
        const oldContent = content;
        content = content.replace(regex, '// REMOVED: dead code cleaned up');
        if (content !== oldContent) {
          modified = true;
        }
      });
      
      // Clean up empty imports and unused variables
      content = cleanupEmptyImports(content);
      content = cleanupUnusedVariables(content);
      
      if (modified) {
        fs.writeFileSync(file, content);
        console.log(`🧽 Cleaned: ${file} (removed ${deadCode.length} dead code patterns)`);
        cleanedFiles++;
      } else {
        // Remove backup if no changes
        fs.unlinkSync(backupFile);
      }
      
    } catch (error) {
      console.log(`❌ Error cleaning ${file}: ${error.message}`);
    }
  });
  
  return { removedFiles, cleanedFiles };
}

function cleanupEmptyImports(content) {
  // Remove empty import statements
  return content
    .replace(/import\s*{\s*}\s*from\s*['"][^'"]*['"];?\n?/g, '')
    .replace(/import\s*['"][^'"]*['"];\s*\/\/.*REMOVED.*/g, '')
    .replace(/\/\*.*REMOVED.*\*\//g, '')
    .replace(/\/\/\s*REMOVED.*/g, '')
    .replace(/\n\n\n+/g, '\n\n'); // Remove excessive newlines
}

function cleanupUnusedVariables(content) {
  // This is a simplified cleanup - in production you'd want more sophisticated AST parsing
  return content
    .replace(/const\s+\w+\s*=\s*\/\/\s*REMOVED.*/g, '')
    .replace(/let\s+\w+\s*=\s*\/\/\s*REMOVED.*/g, '')
    .replace(/var\s+\w+\s*=\s*\/\/\s*REMOVED.*/g, '');
}

// ============================================================================
// VALIDATION
// ============================================================================

function validateCleanup() {
  console.log('✅ Validating cleanup...\n');
  
  const validationErrors = [];
  
  // Check that dead files are actually removed
  DEAD_CODE_FILES.forEach(file => {
    if (fs.existsSync(file)) {
      validationErrors.push(`File still exists: ${file}`);
    }
  });
  
  // Check for remaining dead code patterns
  const allFiles = glob.sync('src/**/*.{ts,tsx}');
  allFiles.forEach(file => {
    try {
      const content = fs.readFileSync(file, 'utf8');
      
      // Check for specific patterns that should be gone
      if (content.includes('hasPermissionWithHierarchy')) {
        validationErrors.push(`Still contains hasPermissionWithHierarchy: ${file}`);
      }
      
      if (content.includes('permissionHierarchy')) {
        validationErrors.push(`Still contains permissionHierarchy references: ${file}`);
      }
      
    } catch (error) {
      validationErrors.push(`Error reading ${file}: ${error.message}`);
    }
  });
  
  return validationErrors;
}

// ============================================================================
// ROLLBACK FUNCTIONALITY
// ============================================================================

function rollbackCleanup() {
  console.log('🔄 Rolling back cleanup...\n');
  
  let restoredFiles = 0;
  
  // Find all backup files
  const backupFiles = glob.sync('**/*.backup');
  
  backupFiles.forEach(backupFile => {
    try {
      const originalFile = backupFile.replace('.backup', '');
      
      // Restore from backup
      fs.copyFileSync(backupFile, originalFile);
      fs.unlinkSync(backupFile);
      
      console.log(`↩️  Restored: ${originalFile}`);
      restoredFiles++;
      
    } catch (error) {
      console.log(`❌ Error restoring ${backupFile}: ${error.message}`);
    }
  });
  
  console.log(`\n✅ Restored ${restoredFiles} files from backup`);
}

// ============================================================================
// MAIN FUNCTIONS
// ============================================================================

function main() {
  console.log('🚀 Starting Permission System Dead Code Cleanup...\n');
  
  const analysis = analyzeDeadCode();
  
  console.log('📊 Dead Code Analysis:');
  console.log(`   Files to remove entirely: ${analysis.filesToRemove.length}`);
  console.log(`   Files to clean: ${analysis.filesToClean.length}`);
  console.log(`   Total dead code size: ${(analysis.deadCodeSize / 1024).toFixed(2)}KB`);
  console.log(`   Errors: ${analysis.errors.length}\n`);
  
  if (analysis.errors.length > 0) {
    console.log('❌ Errors found during analysis:');
    analysis.errors.forEach(({ file, error }) => {
      console.log(`   ${file}: ${error}`);
    });
    console.log('');
  }
  
  // Show what will be removed
  if (analysis.filesToRemove.length > 0) {
    console.log('📋 Files to be removed:');
    analysis.filesToRemove.forEach(({ file, size }) => {
      console.log(`   ${file} (${(size / 1024).toFixed(2)}KB)`);
    });
    console.log('');
  }
  
  if (analysis.filesToClean.length > 0) {
    console.log('📋 Files to be cleaned:');
    analysis.filesToClean.forEach(({ file, deadCode }) => {
      console.log(`   ${file} (${deadCode.length} dead code patterns)`);
    });
    console.log('');
  }
  
  // Perform cleanup
  const { removedFiles, cleanedFiles } = removeDeadCode(analysis);
  
  // Validate cleanup
  const validationErrors = validateCleanup();
  
  console.log('📊 Cleanup Summary:');
  console.log(`   Files removed: ${removedFiles}`);
  console.log(`   Files cleaned: ${cleanedFiles}`);
  console.log(`   Validation errors: ${validationErrors.length}`);
  
  if (validationErrors.length > 0) {
    console.log('\n❌ Validation errors:');
    validationErrors.forEach(error => {
      console.log(`   ${error}`);
    });
    process.exit(1);
  } else {
    console.log('\n✅ Dead code cleanup completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. Run tests to ensure nothing is broken');
    console.log('   2. Run TypeScript compiler to check for errors');
    console.log('   3. Test the application manually');
    console.log('   4. Commit the changes');
  }
}

// ============================================================================
// CLI INTERFACE
// ============================================================================

if (require.main === module) {
  const command = process.argv[2];
  
  switch (command) {
    case 'analyze':
      console.log('🔍 Analysis only mode...\n');
      const analysis = analyzeDeadCode();
      console.log('Analysis complete. Use "node cleanup-dead-code.js" to perform cleanup.');
      break;
    case 'rollback':
      rollbackCleanup();
      break;
    default:
      main();
      break;
  }
}

module.exports = {
  analyzeDeadCode,
  removeDeadCode,
  validateCleanup,
  rollbackCleanup
};