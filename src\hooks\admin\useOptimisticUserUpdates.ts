import { useSWRConfig } from 'swr'
import { AdminService, type AuthorizedUser } from '../../services/admin'
import { ADMIN_KEYS, type AdminData } from './useAdminData'
import { useToast } from '../use-toast'

export function useOptimisticUserUpdates(
  currentFilters?: { limit?: number; offset?: number; search?: string; active?: boolean }
) {
  const { mutate } = useSWRConfig()
  const { toast } = useToast()

  // Use provided filters or fallback to default
  const filters = currentFilters || { limit: 100 }
  const cacheKey = ADMIN_KEYS.combined(filters)

  const updateUserOptimistically = async (
    userId: string, 
    updates: Partial<AuthorizedUser>,
    options?: { 
      revalidate?: boolean
      optimisticUpdate?: boolean
    }
  ) => {
    const { revalidate = true, optimisticUpdate = true } = options || {}

    try {

      // Optimistic update - update cache immediately
      if (optimisticUpdate) {
        await mutate(
          cacheKey,
          (currentData: AdminData | undefined) => {
            if (!currentData) return currentData

            const updatedUsers = currentData.users.map(user =>
              user.id === userId ? { ...user, ...updates } : user
            )

            return {
              ...currentData,
              users: updatedUsers
            }
          },
          false // Don't revalidate immediately
        )
      }

      // Make actual API call
      await AdminService.updateUser(userId, updates)

      // Show success feedback
      toast({
        title: 'User Updated',
        description: 'User information has been updated successfully',
      })

      // Revalidate to sync with server
      if (revalidate) {
        await mutate(cacheKey)
      }

      return true
    } catch (error) {
      // Rollback optimistic update on error
      if (optimisticUpdate) {
        await mutate(cacheKey)
      }

      // Show error feedback
      const errorMessage = error instanceof Error ? error.message : 'Failed to update user'
      toast({
        title: 'Update Failed',
        description: errorMessage,
        variant: 'destructive'
      })

      throw error
    }
  }

  const deactivateUserOptimistically = async (userId: string) => {
    try {

      // Optimistic update - mark user as inactive
      await mutate(
        cacheKey,
        (currentData: AdminData | undefined) => {
          if (!currentData) return currentData

          const updatedUsers = currentData.users.map(user =>
            user.id === userId ? { ...user, is_active: false } : user
          )
          
          return {
            ...currentData,
            users: updatedUsers,
            stats: {
              ...currentData.stats,
              active_users: currentData.stats.active_users - 1
            }
          }
        },
        false
      )

      // Make actual API call (soft delete via deactivation)
      await AdminService.deactivateUser(userId)

      toast({
        title: 'User Deactivated',
        description: 'User has been deactivated successfully',
      })

      // Revalidate to sync with server
      await mutate(cacheKey)

      return true
    } catch (error) {
      // Rollback on error
      await mutate(cacheKey)

      const errorMessage = error instanceof Error ? error.message : 'Failed to deactivate user'
      toast({
        title: 'Deactivation Failed',
        description: errorMessage,
        variant: 'destructive'
      })

      throw error
    }
  }

  const createUserOptimistically = async (userData: any) => {
    try {
      // Make API call first for creation (we need the ID)
      const newUser = await AdminService.createUser(userData)

      // Update cache with new user

      await mutate(
        cacheKey,
        (currentData: AdminData | undefined) => {
          if (!currentData) return currentData

          return {
            ...currentData,
            users: [...currentData.users, newUser],
            totalUsers: currentData.totalUsers + 1,
            stats: {
              ...currentData.stats,
              total_users: currentData.stats.total_users + 1,
              active_users: newUser.is_active ? currentData.stats.active_users + 1 : currentData.stats.active_users
            }
          }
        },
        false
      )

      toast({
        title: 'User Created',
        description: `${newUser.first_name} ${newUser.last_name} has been added successfully`,
      })

      // Revalidate to ensure consistency
      await mutate(cacheKey)

      return newUser
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create user'
      toast({
        title: 'Creation Failed',
        description: errorMessage,
        variant: 'destructive'
      })

      throw error
    }
  }

  return {
    updateUserOptimistically,
    deactivateUserOptimistically,
    createUserOptimistically
  }
}