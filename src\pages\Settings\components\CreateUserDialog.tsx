import React, { useState, useEffect } from 'react'
import { AdminService, type CreateUserRequest } from '../../../services/admin'
import { ALL_PERMISSIONS } from '../../../types/permissions.types'
import { RoleTemplateService } from '../../../services/roleTemplateService'
import { useAuth } from '../../../contexts/AuthContext'
import { useToast } from '../../../hooks/use-toast'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../../../components/ui/dialog'
import { Button } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { Label } from '../../../components/ui/label'
import { Textarea } from '../../../components/ui/textarea'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../components/ui/select'
import { Checkbox } from '../../../components/ui/checkbox'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/card'
import { ScrollArea } from '../../../components/ui/scroll-area'
import { Badge } from '../../../components/ui/badge'
import { MetricBadge } from '../../../components/ui/metric-badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../components/ui/tabs'
import { User, Shield, FileText } from 'lucide-react'

interface CreateUserDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => Promise<void>
}


export function CreateUserDialog({ open, onOpenChange, onSuccess }: CreateUserDialogProps) {
  const { user: currentUser } = useAuth()
  const { toast } = useToast()
  
  const [loading, setLoading] = useState(false)
  const [permissions, setPermissions] = useState<Array<{ key: string; name: string; category: string }>>([])
  const [roleTemplates, setRoleTemplates] = useState<Array<{value: string; label: string; description: string}>>([])
  const [formData, setFormData] = useState<CreateUserRequest>({
    email: '',
    first_name: '',
    last_name: '',
    department: '',
    permissions: [],
    role_template: '',
    notes: ''
  })

  // Load available permissions and role templates
  useEffect(() => {
    if (open) {
      loadPermissions()
      loadRoleTemplates()
    }
  }, [open])

  const loadPermissions = async () => {
    try {
      const perms = await AdminService.getPermissions()
      setPermissions(perms)
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load permissions',
        variant: 'destructive'
      })
    }
  }

  const loadRoleTemplates = async () => {
    try {
      const templates = await RoleTemplateService.getRoleOptions()
      setRoleTemplates(templates)
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load role templates',
        variant: 'destructive'
      })
    }
  }

  const handleInputChange = (field: keyof CreateUserRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleRoleChange = async (role: string) => {
    const rolePermissions = await RoleTemplateService.getRolePermissions(role)
    setFormData(prev => ({
      ...prev,
      role_template: role,
      permissions: rolePermissions
    }))
  }

  const handlePermissionToggle = (permission: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      permissions: checked 
        ? [...prev.permissions, permission]
        : prev.permissions.filter(p => p !== permission)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.email || !formData.first_name || !formData.last_name) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields',
        variant: 'destructive'
      })
      return
    }

    try {
      setLoading(true)
      
      await AdminService.createUser(formData, currentUser?.id)
      
      toast({
        title: 'User Created',
        description: `${formData.first_name} ${formData.last_name} has been added to the system`,
      })
      
      await onSuccess()
      onOpenChange(false)
      
      // Reset form
      setFormData({
        email: '',
        first_name: '',
        last_name: '',
        department: '',
        permissions: [],
        role_template: '',
        notes: ''
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create user',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const groupedPermissions = permissions.reduce((groups, permission) => {
    if (!groups[permission.category]) {
      groups[permission.category] = []
    }
    groups[permission.category].push(permission)
    return groups
  }, {} as Record<string, typeof permissions>)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Add New User</DialogTitle>
          <DialogDescription>
            Create a new user account with specific permissions and role
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Tabs defaultValue="details" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="details" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Details
              </TabsTrigger>
              <TabsTrigger value="role" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Role & Permissions
              </TabsTrigger>
              <TabsTrigger value="notes" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Notes
              </TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="department">Department</Label>
                  <Input
                    id="department"
                    value={formData.department}
                    onChange={(e) => handleInputChange('department', e.target.value)}
                    placeholder="e.g., Sales, Marketing"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="first_name">First Name *</Label>
                  <Input
                    id="first_name"
                    value={formData.first_name}
                    onChange={(e) => handleInputChange('first_name', e.target.value)}
                    placeholder="John"
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="last_name">Last Name *</Label>
                  <Input
                    id="last_name"
                    value={formData.last_name}
                    onChange={(e) => handleInputChange('last_name', e.target.value)}
                    placeholder="Doe"
                    required
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="role" className="space-y-4">
              {/* Role Template Selection */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Role Template</CardTitle>
                  <CardDescription className="text-xs">
                    Choose a pre-configured role or customize permissions manually
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Select value={formData.role_template} onValueChange={handleRoleChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a role template" />
                    </SelectTrigger>
                    <SelectContent>
                      {roleTemplates.map((role) => (
                        <SelectItem key={role.value} value={role.value}>
                          <div>
                            <div className="font-medium">{role.label}</div>
                            <div className="text-xs text-muted-foreground">{role.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </CardContent>
              </Card>

              {/* Selected Permissions */}
              {formData.permissions.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm flex items-center gap-2">
                      Selected Permissions <MetricBadge value={formData.permissions.length} variant="info" className="text-xs" />
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-1">
                      {formData.permissions.map((permission) => (
                        <Badge key={permission} variant="outline" className="text-xs">
                          {permission}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="notes" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="notes">Administrative Notes</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  placeholder="Additional notes about this user..."
                  rows={4}
                />
              </div>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end gap-3 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Creating...' : 'Create User'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}