import { supabase } from '../../lib/supabase';
import { PreAuthService, type AuthorizedUser } from '../preAuthService';

/**
 * Authorization Service
 * Handles user authorization checks and permission validation
 * Following CLAUDE.md guidelines - focused, under 250 lines
 */
export class AuthorizationService {
  /**
   * Check if user email is authorized in system
   */
  static async checkUserAuthorization(email: string): Promise<AuthorizedUser | null> {
    try {
      return await PreAuthService.validateEmail(email);
    } catch (error) {
      console.error('Error checking user authorization:', error);
      return null;
    }
  }

  /**
   * Get user permissions by email
   */
  static async getUserPermissions(email: string): Promise<string[]> {
    try {
      const authorizedUser = await this.checkUserAuthorization(email);
      return authorizedUser?.permissions || [];
    } catch (error) {
      console.error('Error getting user permissions:', error);
      return [];
    }
  }

  /**
   * Check if user has specific permission
   */
  static async hasPermission(email: string, permission: string): Promise<boolean> {
    try {
      const permissions = await this.getUserPermissions(email);
      return permissions.includes(permission) || permissions.includes('system.full_access');
    } catch (error) {
      console.error('Error checking permission:', error);
      return false;
    }
  }

  /**
   * Update user's last login timestamp via Edge Function
   */
  static async updateLastLogin(email: string): Promise<void> {
    try {
      console.log('Updating last login for:', email);
      
      // Use Edge Function to securely update last login
      const { data, error } = await supabase.functions.invoke('validate-user-auth', {
        body: { 
          email,
          action: 'update_last_login'
        }
      });

      if (error) {
        console.error('Error updating last login via Edge Function:', error);
        console.error('This is not critical - user can still proceed');
      } else {
        console.log('Last login updated successfully');
      }
    } catch (error) {
      console.error('Error updating last login:', error);
      console.error('This is not critical - user can still proceed');
    }
  }

  /**
   * Get role template permissions
   */
  static async getRolePermissions(roleName: string): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from('roles')
        .select('permissions')
        .eq('name', roleName)
        .eq('is_active', true)
        .single();

      if (error || !data) {
        return [];
      }

      return data.permissions || [];
    } catch (error) {
      console.error('Error getting role permissions:', error);
      return [];
    }
  }
}