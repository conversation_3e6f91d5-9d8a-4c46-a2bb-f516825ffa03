-- Migration 023: Fix Component Source Attribution
-- Problem: Component sources always show "Manual Entry" in ViewSheet after editing
-- Root Cause: update_component_values_safely function does not preserve template_id
-- Solution: Update function to handle template_id properly during component value updates

-- Drop existing function if it exists
DROP FUNCTION IF EXISTS update_component_values_safely(UUID, UUID, UUID, JSONB);

-- Create fixed function that preserves template_id during editing operations
CREATE OR REPLACE FUNCTION update_component_values_safely(
    p_category_id UUID,
    p_product_type_id UUID,
    p_size_id UUID,
    p_component_values JSONB
)
RETURNS TABLE(
    id UUID,
    component_id UUID,
    value NUMERIC,
    tier_metadata JSONB,
    template_id UUID,
    notes TEXT,
    effective_date TIMESTAMP,
    operation_type TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_component_value JSONB;
    v_existing_record RECORD;
    v_new_record RECORD;
    v_template_id UUID;
    v_tier_metadata JSONB;
    v_notes TEXT;
    v_effective_date TIMESTAMP := NOW();
BEGIN
    -- Validate inputs
    IF p_category_id IS NULL OR p_product_type_id IS NULL OR p_size_id IS NULL THEN
        RAISE EXCEPTION 'Category ID, Product Type ID, and Size ID cannot be null';
    END IF;

    IF p_component_values IS NULL OR jsonb_array_length(p_component_values) = 0 THEN
        RAISE EXCEPTION 'Component values array cannot be null or empty';
    END IF;

    -- Process each component value update
    FOR v_component_value IN SELECT * FROM jsonb_array_elements(p_component_values)
    LOOP
        -- Validate component value structure
        IF NOT (v_component_value ? 'component_id' AND v_component_value ? 'value') THEN
            RAISE WARNING 'Skipping invalid component value: missing component_id or value';
            CONTINUE;
        END IF;

        -- Extract template_id (preserve if provided, otherwise keep existing or set to null)
        v_template_id := CASE 
            WHEN v_component_value->>'template_id' IS NOT NULL 
                 AND v_component_value->>'template_id' != 'null' 
                 AND v_component_value->>'template_id' != '' THEN 
                (v_component_value->>'template_id')::UUID
            ELSE NULL 
        END;

        -- Extract tier_metadata
        v_tier_metadata := CASE 
            WHEN v_component_value ? 'tier_metadata' THEN 
                v_component_value->'tier_metadata'
            ELSE NULL 
        END;

        -- Extract notes
        v_notes := v_component_value->>'notes';

        -- Check if there's an existing current record for this component
        SELECT * INTO v_existing_record
        FROM production_cost_component_values
        WHERE product_category_id = p_category_id
          AND product_type_id = p_product_type_id
          AND size_id = p_size_id
          AND component_id = (v_component_value->>'component_id')::UUID
          AND is_current = true;

        -- If template_id is not explicitly provided, try to preserve the existing one
        IF v_template_id IS NULL AND v_existing_record IS NOT NULL THEN
            v_template_id := v_existing_record.template_id;
        END IF;

        -- Mark existing record as not current
        IF v_existing_record IS NOT NULL THEN
            UPDATE production_cost_component_values
            SET is_current = false
            WHERE id = v_existing_record.id;
        END IF;

        -- Insert new current record with preserved template_id
        INSERT INTO production_cost_component_values (
            component_id,
            product_category_id,
            product_type_id,
            size_id,
            value,
            effective_date,
            is_current,
            notes,
            tier_metadata,
            template_id  -- CRITICAL: Preserve template source attribution
        )
        VALUES (
            (v_component_value->>'component_id')::UUID,
            p_category_id,
            p_product_type_id,
            p_size_id,
            (v_component_value->>'value')::NUMERIC,
            v_effective_date,
            true,
            v_notes,
            v_tier_metadata,
            v_template_id  -- Preserved template source
        )
        RETURNING * INTO v_new_record;

        -- Return the updated record with preserved template_id
        RETURN QUERY SELECT 
            v_new_record.id,
            v_new_record.component_id,
            v_new_record.value,
            v_new_record.tier_metadata,
            v_new_record.template_id,  -- Now correctly preserved
            v_new_record.notes,
            v_new_record.effective_date,
            CASE 
                WHEN v_existing_record IS NOT NULL THEN 'update'
                ELSE 'insert'
            END::TEXT;

    END LOOP;

    -- Update product line total cost after component changes
    PERFORM update_product_line_total_cost(p_category_id, p_product_type_id, p_size_id);

EXCEPTION WHEN OTHERS THEN
    -- Log error and re-raise with context
    RAISE EXCEPTION 'Error in update_component_values_safely: % (Category: %, ProductType: %, Size: %)', 
        SQLERRM, p_category_id, p_product_type_id, p_size_id;
END;
$$;

-- Add function comment explaining the fix
COMMENT ON FUNCTION update_component_values_safely IS 
'Fixed version that preserves template_id during component value updates to maintain source attribution in ViewSheet. Critical for showing correct template names instead of "Manual Entry" after editing operations.';

-- Test the function exists and has correct signature
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_proc p
        JOIN pg_namespace n ON p.pronamespace = n.oid
        WHERE n.nspname = 'public' 
        AND p.proname = 'update_component_values_safely'
    ) THEN
        RAISE EXCEPTION 'Function update_component_values_safely was not created successfully';
    END IF;
    
    RAISE NOTICE 'Component source attribution fix applied successfully';
    RAISE NOTICE 'Function update_component_values_safely now preserves template_id during editing';
END;
$$;