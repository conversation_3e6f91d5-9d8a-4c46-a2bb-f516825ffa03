# Production Cost SWR Cache Architecture Analysis

## Overview
This document analyzes the SWR cache architecture in the production cost system, identifying cache keys, interdependencies, invalidation patterns, and potential issues.

## 1. Cache Keys Inventory

### Templates Cache Keys
- **Base key**: `'production-cost-templates'`
- **Template by ID**: `'production-cost-template-${id}'`
- **Product-specific templates**: `'production-cost-templates-product-${categoryId}-${productTypeId}-${sizeId}'`
- **Replacement templates**: `'production-cost-templates-replacement-${categoryId}-${productTypeId}-${sizeId}-${category}-${method}-${tier}'`

### Components Cache Keys
- **Base key**: `['production-cost-components', filters]`
- **Categories**: `'component-categories'`
- **All components (alt)**: `'production_cost_components'`

### Component Values Cache Keys
- **By product**: `/production-cost/component-values/${productId}`
- **Batch**: `/production-cost/component-values/batch/${productIds.sort().join(',')}`
- **Editing session**: `/production-cost/component-values/${productId}/editing/${editSession}`
- **By combination**: `/production-cost/component-values/combination/${categoryId}/${productTypeId}/${sizeId}`
- **Template-specific**: `/production-cost/component-values/template/${templateId}/${categoryId}/${productTypeId}/${sizeId}`

### Production Costs Cache Keys
- **Product line costs**: `/production-cost/product-line`
- **Product line costs (alt)**: `/production-cost/product-line-costs`
- **Product costs**: `/production-cost/products`
- **Base components**: `/production-cost/components`

## 2. Cache Interdependencies

### Template → Component Values Flow
```
Template Application
    ↓
Updates component_values table
    ↓
Should invalidate:
- /production-cost/component-values/${productId}
- /production-cost/component-values/combination/${categoryId}/${productTypeId}/${sizeId}
- /production-cost/product-line (production costs)
```

### Component → Component Values Flow
```
Component Update/Delete
    ↓
Affects all component values using that component
    ↓
Should invalidate:
- All /production-cost/component-values/* caches
- /production-cost/product-line (if costs change)
```

### Product Line → Multiple Caches Flow
```
Product Line Update (deprecation/revival)
    ↓
Should invalidate:
- /production-cost/product-line
- /production-cost/component-values/combination/* (for that combination)
- Template application caches
```

## 3. Cache Invalidation Patterns

### Explicit Invalidations Found

1. **Template Deletion** (useTemplatesSWR.ts):
   ```javascript
   // Comprehensive invalidation on template deletion
   mutate(TEMPLATE_KEY(id), undefined, false);
   mutate(TEMPLATES_KEY);
   // Clears all template-related caches
   for (const key of cache.keys()) {
     if (key.includes('production-cost-template')) {
       mutate(key);
     }
   }
   ```

2. **Component Operations** (useComponentsSWR.ts):
   - Uses optimistic updates with `mutate(data, false)`
   - Triggers revalidation with `mutate(data, true)` after success
   - Reverts on error with `mutate()`

3. **Component Values** (useComponentValuesSWR.ts):
   - Updates specific product cache: `mutate(getComponentValuesKey(productId))`
   - Batch operations update multiple caches
   - Editing session has separate cache key

### Missing Invalidations Identified

1. **Template Application** (templateApplicationService.ts):
   - ❌ No cache invalidation after applying templates
   - Should invalidate:
     - Component values for affected products
     - Product line costs
     - Template application count

2. **Product Line Updates**:
   - ❌ No invalidation of component values when product line changes
   - ❌ No invalidation of production costs when deprecation status changes

3. **Cross-Entity Updates**:
   - ❌ Component deletion doesn't invalidate component values caches
   - ❌ Template updates don't invalidate production cost calculations

## 4. Potential Race Conditions

### 1. Template Application Race
```
User applies template → Creates component values
                     ↓
Another user views production costs (stale cache)
                     ↓
First user's changes not visible until manual refresh
```

### 2. Editing Session Race
```
User A starts editing product costs
User B starts editing same product
Both have different editing cache keys
Changes can overwrite each other
```

### 3. Deprecation/Revival Race
```
Product deprecated → Should invalidate caches
                  ↓
Revival operation starts before caches clear
                  ↓
Stale deprecation status shown
```

## 5. Stale Data Issues

### 1. Production Cost Calculations
- **Issue**: `useProductLineProductionCosts` doesn't invalidate when:
  - Templates are applied
  - Component values change
  - Products are deprecated/revived
- **Impact**: Incorrect cost calculations shown in UI

### 2. Template-Component Mismatch
- **Issue**: Template caches don't refresh when components are updated
- **Impact**: Templates may reference deleted/inactive components

### 3. Component Value Orphans
- **Issue**: Component values remain cached after component deletion
- **Impact**: Ghost values appear in calculations

## 6. Recommendations

### 1. Implement Cross-Cache Invalidation Service
```javascript
// services/cacheInvalidation.service.ts
export const invalidateRelatedCaches = async (entity: string, operation: string, ids: string[]) => {
  switch(entity) {
    case 'template':
      // Invalidate component values for products using this template
      // Invalidate production costs
      break;
    case 'component':
      // Invalidate all component value caches
      // Invalidate production costs
      break;
    case 'product_line':
      // Invalidate component values for this combination
      // Invalidate production costs
      break;
  }
};
```

### 2. Add Cache Dependencies Map
```javascript
const CACHE_DEPENDENCIES = {
  'template_application': [
    '/production-cost/component-values/*',
    '/production-cost/product-line',
    'production-cost-templates-product-*'
  ],
  'component_update': [
    '/production-cost/component-values/*',
    '/production-cost/product-line'
  ],
  'product_deprecation': [
    '/production-cost/product-line',
    '/production-cost/component-values/combination/*'
  ]
};
```

### 3. Implement Cache Versioning
```javascript
// Add version to cache keys to force refresh
const getCacheKey = (base: string, version?: number) => {
  return version ? `${base}?v=${version}` : base;
};
```

### 4. Add Global Cache Invalidation on Critical Operations
```javascript
// After template application
await mutate(() => true, undefined, { 
  revalidate: (key) => {
    return key.includes('component-values') || 
           key.includes('product-line');
  }
});
```

### 5. Implement Optimistic Locking for Editing Sessions
```javascript
// Check for concurrent edits before saving
const checkEditConflicts = async (productId: string, editSession: string) => {
  const latestData = await fetchComponentValues(productId);
  // Compare with editing session data
  // Warn user if conflicts detected
};
```

## 7. Critical Issues to Fix Immediately

1. **Add cache invalidation after template application**
2. **Invalidate production costs when component values change**
3. **Clear component value caches when components are deleted**
4. **Implement proper deprecation/revival cache clearing**
5. **Add cross-entity cache invalidation for related data**

## 8. Performance Considerations

- Current `dedupingInterval` values range from 3-30 seconds
- Consider increasing for rarely-changing data (templates, components)
- Implement selective revalidation instead of full cache clears
- Use SWR's `mutate` with specific keys rather than global invalidation