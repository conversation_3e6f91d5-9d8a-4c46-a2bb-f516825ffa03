import { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { AuthService } from '../../../../services/auth/authService';
import { logger } from '../../../../utils/logger';
import { useAuthForm } from '../../../../hooks/auth/useAuthForm';
import type { AuthorizedUser } from '../../../../services/preAuthService';
import type { SetupStep } from '../types';

const accountLogger = logger.withPrefix('AccountCreation');

interface UseAccountCreationProps {
  email: string;
  authorizedUser: AuthorizedUser;
}

export const useAccountCreation = ({ email, authorizedUser }: UseAccountCreationProps) => {
  const navigate = useNavigate();
  
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [pin, setPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [firstName, setFirstName] = useState(authorizedUser?.first_name || '');
  const [lastName, setLastName] = useState(authorizedUser?.last_name || '');
  const [step, setStep] = useState<SetupStep>('setup');
  const [verificationCode, setVerificationCode] = useState('');
  
  // Use shared auth form state management
  const { loading: isCreating, error, success, setLoading, setError, setSuccess, clearMessages } = useAuthForm();

  const handleCreateAccount = useCallback(async () => {
    if (!email) {
      setError('Email is required');
      return;
    }

    // Determine if this is an admin user
    const isAdmin = authorizedUser.role === 'admin' || authorizedUser.role === 'administrator';

    setLoading(true);
    clearMessages();

    try {
      accountLogger.info('Creating account for authorized user:', email);
      
      // Use edited names for admins, or original names for regular users
      const finalFirstName = isAdmin ? firstName.trim() : authorizedUser.first_name;
      const finalLastName = isAdmin ? lastName.trim() : authorizedUser.last_name;
      
      // Validate admin name fields if they're editable
      if (isAdmin) {
        if (!finalFirstName || !finalLastName) {
          setError('Please enter both first and last name');
          return;
        }
      }
      
      // Determine credential based on user role
      const credential = isAdmin ? password : pin;
      const credentialType = isAdmin ? 'password' : 'PIN';

      // Validation
      if (isAdmin) {
        if (!password || !confirmPassword || password !== confirmPassword) {
          setError('Please enter matching passwords');
          return;
        }
      } else {
        if (!pin || !confirmPin || pin !== confirmPin) {
          setError('Please enter matching PINs');
          return;
        }
        if (pin.length < 4) {
          setError('PIN must be at least 4 digits');
          return;
        }
      }

      // Both user types use the same signup method
      await AuthService.signUp(email, credential, {
        first_name: finalFirstName,
        last_name: finalLastName,
        department: authorizedUser.department,
        role: authorizedUser.role,
        credential_type: credentialType // Optional: track credential type
      });

      accountLogger.info(`${credentialType} account created successfully, moving to verification`);
      setStep('verification');
      
    } catch (err: any) {
      accountLogger.error('Account creation failed:', err);
      setError(err.message || 'Failed to create account. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [email, password, confirmPassword, pin, confirmPin, firstName, lastName, authorizedUser, setLoading, setError, clearMessages]);

  const handleVerifyCode = useCallback(async () => {
    if (!verificationCode.trim()) {
      setError('Please enter the verification code');
      return;
    }

    setLoading(true);
    clearMessages();

    try {
      accountLogger.info('Verifying email code for:', email);
      
      // Both user types now use the same verification method since both use signUp
      const result = await AuthService.verifySignupOTP(email, verificationCode.trim());
      
      // Ensure user profile is created properly
      if (result.user?.id) {
        accountLogger.info('Email verified successfully, ensuring profile exists...');
        
        // Import profile service and ensure profile exists
        const { ensureProfileExists } = await import('../../../../services/profileService');
        const profile = await ensureProfileExists(result.user.id);
        
        if (profile) {
          accountLogger.info('Profile confirmed, redirecting to dashboard');
        } else {
          accountLogger.warn('Profile creation failed, but proceeding to dashboard');
        }
      }
      
      accountLogger.info('Account setup completed successfully, redirecting to dashboard');
      navigate('/', { replace: true });
      
    } catch (err: any) {
      accountLogger.error('Email verification failed:', err);
      setError(err.message || 'Invalid verification code. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [email, verificationCode, navigate, setLoading, setError]);

  return {
    // State
    password,
    confirmPassword,
    pin,
    confirmPin,
    firstName,
    lastName,
    step,
    verificationCode,
    isCreating,
    error,
    success,

    // Actions
    setPassword,
    setConfirmPassword,
    setPin,
    setConfirmPin,
    setFirstName,
    setLastName,
    setStep,
    setVerificationCode,
    handleCreateAccount,
    handleVerifyCode,
    clearMessages
  };
};