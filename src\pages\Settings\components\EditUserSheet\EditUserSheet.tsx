import React from 'react'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>Header,
  SheetTitle,
} from '../../../../components/ui/sheet'
import { Button } from '../../../../components/ui/button'
import { ScrollArea } from '../../../../components/ui/scroll-area'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '../../../../components/ui/tabs'
import { User, Shield, FileText, UserCog } from 'lucide-react'
import { useEditUserForm } from './hooks/useEditUserForm'
import { BasicInfoTab } from './components/BasicInfoTab'
import { PermissionsTab } from './components/PermissionsTab'
import { NotesTab } from './components/NotesTab'
import type { EditUserSheetProps } from './types'

export function EditUserSheet({ open, onOpenChange, user, onSuccess }: EditUserSheetProps) {
  const {
    activeTab,
    setActiveTab,
    formData,
    isSubmitting,
    hasChanges,
    handleInputChange,
    handlePermissionToggle,
    handleSubmit,
    handleReset
  } = useEditUserForm(open, user, onSuccess, () => onOpenChange(false))

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-2xl flex flex-col">
        <SheetHeader className="space-y-3">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
              <UserCog className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <SheetTitle className="text-xl">Edit User</SheetTitle>
              <SheetDescription>
                Update user account information, permissions, and access levels.
              </SheetDescription>
            </div>
          </div>
        </SheetHeader>

        <div className="flex-1 min-h-0 mt-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-3 flex-shrink-0">
              <TabsTrigger value="basic" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Basic Info
              </TabsTrigger>
              <TabsTrigger value="permissions" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Permissions
              </TabsTrigger>
              <TabsTrigger value="notes" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Additional Info
              </TabsTrigger>
            </TabsList>

            <div className="flex-1 min-h-0 mt-4">
              <ScrollArea className="h-full">
                <div className="pr-4 pb-4">
                  <TabsContent value="basic" className="space-y-6 mt-0">
                    <BasicInfoTab 
                      formData={formData}
                      onInputChange={handleInputChange}
                    />
                  </TabsContent>

                  <TabsContent value="permissions" className="space-y-6 mt-0">
                    <PermissionsTab
                      formData={formData}
                      onPermissionToggle={handlePermissionToggle}
                    />
                  </TabsContent>

                  <TabsContent value="notes" className="space-y-6 mt-0">
                    <NotesTab
                      formData={formData}
                      onInputChange={handleInputChange}
                    />
                  </TabsContent>
                </div>
              </ScrollArea>
            </div>
          </Tabs>
        </div>

        <SheetFooter className="flex-shrink-0 pt-6 border-t">
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
            className="border-gray-300 text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </Button>
          {hasChanges && (
            <Button 
              variant="ghost"
              onClick={handleReset}
              disabled={isSubmitting}
              className="text-gray-600 hover:text-gray-800"
            >
              Reset Changes
            </Button>
          )}
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting || !hasChanges}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                Updating User...
              </>
            ) : (
              <>
                <UserCog className="h-4 w-4 mr-2" />
                Update User
              </>
            )}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  )
}