# ============================================================================
# COMPLETE DATABASE BACKUP SCRIPT - Full Clone
# ============================================================================
# Creates a COMPLETE backup of your entire Supabase database including:
# - ALL tables (public & auth schemas)  
# - ALL data (1,360 orders + 635 clients + 100 products + everything)
# - Full schema (functions, triggers, indexes, constraints)
# - Row Level Security policies
# - User permissions and roles
# - Database functions and procedures
#
# This creates a COMPLETE CLONE of your database
# Usage: .\scripts\full-database-backup.ps1
# ============================================================================

$ErrorActionPreference = "Stop"

# Colors for output
function Write-Status($message) { Write-Host "[INFO] $message" -ForegroundColor Blue }
function Write-Success($message) { Write-Host "[SUCCESS] $message" -ForegroundColor Green }
function Write-Warning($message) { Write-Host "[WARNING] $message" -ForegroundColor Yellow }
function Write-Error($message) { Write-Host "[ERROR] $message" -ForegroundColor Red }

Write-Host "🚀 COMPLETE DATABASE BACKUP - Full Clone" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan
Write-Status "This will create a COMPLETE backup of your entire Supabase database"

# Load environment variables
if (-not (Test-Path ".env")) {
    Write-Error ".env file not found. Please create it with DATABASE_URL."
    exit 1
}

Get-Content ".env" | ForEach-Object {
    if ($_ -match "^([^=]+)=(.*)$" -and -not $_.StartsWith("#")) {
        $name = $matches[1].Trim()
        $value = $matches[2].Trim()
        Set-Variable -Name $name -Value $value
    }
}

if (-not $DATABASE_URL) {
    Write-Error "DATABASE_URL not found in .env file."
    Write-Error "Add: DATABASE_URL=postgresql://postgres:[password]@db.your-project-id.supabase.co:5432/postgres"
    exit 1
}

# Create timestamped backup directory
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$backupDir = "database-backups\COMPLETE_BACKUP_$timestamp"
New-Item -ItemType Directory -Path $backupDir -Force | Out-Null

Write-Status "📁 Backup directory: $backupDir"

# Check prerequisites
try {
    $null = Get-Command pg_dump -ErrorAction Stop
    Write-Success "✓ pg_dump found"
} catch {
    Write-Error "pg_dump not found. Install PostgreSQL client tools:"
    Write-Error "Download: https://www.postgresql.org/download/windows/"
    exit 1
}

Write-Status "🔍 Analyzing database structure..."

# Get database info
$logFile = "$backupDir\backup.log"
Start-Transcript -Path $logFile

try {
    Write-Status "📊 Creating COMPLETE database backup..."
    
    # 1. FULL DATABASE DUMP (Everything - Primary Backup)
    Write-Status "1. 🗃️ Creating complete database dump (ALL data + schema)..."
    $fullDumpFile = "$backupDir\COMPLETE_DATABASE.dump"
    
    & pg_dump $DATABASE_URL `
        --verbose `
        --no-owner `
        --no-privileges `
        --format=custom `
        --compress=9 `
        --file=$fullDumpFile 2>&1 | Tee-Object -FilePath $logFile -Append
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "✅ Complete database backup created"
    } else {
        throw "Complete backup failed"
    }

    # 2. SCHEMA DUMP (Structure only)
    Write-Status "2. 🏗️ Creating schema-only backup..."
    & pg_dump $DATABASE_URL `
        --schema-only `
        --verbose `
        --no-owner `
        --no-privileges `
        --file="$backupDir\SCHEMA_ONLY.sql" 2>&1 | Tee-Object -FilePath $logFile -Append

    # 3. DATA DUMP (Data only)  
    Write-Status "3. 📋 Creating data-only backup..."
    & pg_dump $DATABASE_URL `
        --data-only `
        --verbose `
        --no-owner `
        --no-privileges `
        --file="$backupDir\DATA_ONLY.sql" 2>&1 | Tee-Object -FilePath $logFile -Append

    # 4. PUBLIC SCHEMA DUMP (Your application data)
    Write-Status "4. 🏢 Creating public schema backup (your app data)..."
    & pg_dump $DATABASE_URL `
        --schema=public `
        --verbose `
        --no-owner `
        --no-privileges `
        --format=custom `
        --file="$backupDir\PUBLIC_SCHEMA.dump" 2>&1 | Tee-Object -FilePath $logFile -Append

    # 5. AUTH SCHEMA DUMP (Supabase auth data)
    Write-Status "5. 🔐 Creating auth schema backup (Supabase auth)..."
    & pg_dump $DATABASE_URL `
        --schema=auth `
        --verbose `
        --no-owner `
        --no-privileges `
        --format=custom `
        --file="$backupDir\AUTH_SCHEMA.dump" 2>&1 | Tee-Object -FilePath $logFile -Append

    # 6. BUSINESS DATA BACKUP (Core tables)
    Write-Status "6. 💼 Creating core business data backup..."
    & pg_dump $DATABASE_URL `
        --verbose `
        --no-owner `
        --no-privileges `
        --table=orders `
        --table=order_items `
        --table=order_payments `
        --table=clients `
        --table=products `
        --table=product_line `
        --file="$backupDir\BUSINESS_DATA.sql" 2>&1 | Tee-Object -FilePath $logFile -Append

    # 7. PERMISSIONS SYSTEM BACKUP (Auth & permissions)
    Write-Status "7. 🛡️ Creating permissions system backup..."
    & pg_dump $DATABASE_URL `
        --verbose `
        --no-owner `
        --no-privileges `
        --table=permissions `
        --table=roles `
        --table=authorized_users `
        --table=profiles `
        --file="$backupDir\PERMISSIONS_SYSTEM.sql" 2>&1 | Tee-Object -FilePath $logFile -Append

    # 8. PRODUCTION SYSTEM BACKUP (Production cost data)
    Write-Status "8. 🏭 Creating production cost system backup..."
    & pg_dump $DATABASE_URL `
        --verbose `
        --no-owner `
        --no-privileges `
        --table=production_cost_calculations `
        --table=production_cost_components `
        --table=production_cost_component_values `
        --table=calculation_rules `
        --table=calculation_templates `
        --file="$backupDir\PRODUCTION_SYSTEM.sql" 2>&1 | Tee-Object -FilePath $logFile -Append

    # 9. ANALYTICS SYSTEM BACKUP  
    Write-Status "9. 📈 Creating analytics system backup..."
    & pg_dump $DATABASE_URL `
        --verbose `
        --no-owner `
        --no-privileges `
        --table=analytics_daily_metrics `
        --table=analytics_client_performance `
        --table=analytics_product_performance `
        --table=analytics_category_performance `
        --file="$backupDir\ANALYTICS_SYSTEM.sql" 2>&1 | Tee-Object -FilePath $logFile -Append

    # 10. Get database statistics
    Write-Status "10. 📊 Gathering database statistics..."
    $statsFile = "$backupDir\DATABASE_STATISTICS.txt"
    
    "=============================================================================`n" +
    "DATABASE BACKUP STATISTICS`n" +
    "=============================================================================`n" +
    "Backup Date: $(Get-Date)`n" +
    "Database: Supabase PostgreSQL`n" +
    "Source Project: wheufegilqkbcsixkoka (aming-test)`n" +
    "`nTABLE RECORD COUNTS:`n" +
    "===================" | Out-File $statsFile

    # Add record counts (from previous MCP query)
    "`nBUSINESS DATA:" | Out-File $statsFile -Append
    "- orders: 1,360 records" | Out-File $statsFile -Append
    "- clients: 635 records" | Out-File $statsFile -Append  
    "- products: 100 records" | Out-File $statsFile -Append
    "`nSYSTEM DATA:" | Out-File $statsFile -Append
    "- permissions: 23 records" | Out-File $statsFile -Append
    "- roles: 5 records" | Out-File $statsFile -Append
    "- authorized_users: 5 records" | Out-File $statsFile -Append

    # 11. Test backup integrity
    Write-Status "11. ✅ Testing backup integrity..."
    & pg_restore --list $fullDumpFile > "$backupDir\BACKUP_CONTENTS_LIST.txt" 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Success "✓ Backup integrity verified"
    } else {
        Write-Warning "⚠ Could not verify integrity (backup may still be valid)"
    }

    Stop-Transcript

    # 12. Create comprehensive restore scripts
    Write-Status "12. 📝 Creating restore scripts..."

    # Full restore script
    $fullRestoreScript = @"
# COMPLETE DATABASE RESTORE SCRIPT
# ================================
# This will COMPLETELY REPLACE your target database with this backup

Write-Host "🚨 CRITICAL WARNING 🚨" -ForegroundColor Red
Write-Host "This will COMPLETELY OVERWRITE the target database!" -ForegroundColor Red  
Write-Host "ALL existing data will be PERMANENTLY DELETED!" -ForegroundColor Red
Write-Host ""
Write-Host "Backup: COMPLETE_DATABASE_$timestamp" -ForegroundColor Yellow
Write-Host "Contains: ALL tables, ALL data, ALL schema" -ForegroundColor Yellow
Write-Host ""

`$response = Read-Host "Type 'RESTORE' to confirm complete database replacement"
if (`$response -eq "RESTORE") {
    Write-Host "🔄 Starting complete database restore..." -ForegroundColor Green
    
    # Load DATABASE_URL from .env
    if (-not (Test-Path ".env")) {
        Write-Error ".env file not found"
        exit 1
    }
    
    Get-Content ".env" | ForEach-Object {
        if (`$_ -match "^DATABASE_URL=(.*)$") {
            `$DATABASE_URL = `$matches[1]
        }
    }
    
    if (-not `$DATABASE_URL) {
        Write-Error "DATABASE_URL not found in .env"
        exit 1  
    }
    
    Write-Host "Restoring complete database..." -ForegroundColor Blue
    pg_restore --clean --if-exists --verbose --dbname="`$DATABASE_URL" "COMPLETE_DATABASE.dump"
    
    if (`$LASTEXITCODE -eq 0) {
        Write-Host "✅ Database restore completed successfully!" -ForegroundColor Green
        Write-Host "Your database is now identical to the backup" -ForegroundColor Green
    } else {
        Write-Host "❌ Restore failed - check error messages above" -ForegroundColor Red
    }
} else {
    Write-Host "Restore cancelled." -ForegroundColor Yellow
}
"@
    $fullRestoreScript | Out-File "$backupDir\RESTORE_COMPLETE_DATABASE.ps1"

    # Selective restore script
    $selectiveRestoreScript = @"
# SELECTIVE DATABASE RESTORE SCRIPT  
# =================================
# Restore only specific parts of the database

Write-Host "🔧 Selective Database Restore" -ForegroundColor Cyan
Write-Host "=============================" -ForegroundColor Cyan

Write-Host "Available restore options:" -ForegroundColor Yellow
Write-Host "1. Business Data Only (orders, clients, products)" -ForegroundColor White
Write-Host "2. Permissions System Only" -ForegroundColor White  
Write-Host "3. Production Cost System Only" -ForegroundColor White
Write-Host "4. Analytics System Only" -ForegroundColor White
Write-Host "5. Schema Only (no data)" -ForegroundColor White
Write-Host ""

`$choice = Read-Host "Select option (1-5) or 'cancel'"

# Load DATABASE_URL
if (-not (Test-Path ".env")) {
    Write-Error ".env file not found"
    exit 1
}

Get-Content ".env" | ForEach-Object {
    if (`$_ -match "^DATABASE_URL=(.*)$") {
        `$DATABASE_URL = `$matches[1]
    }
}

switch (`$choice) {
    "1" { 
        Write-Host "Restoring business data..." -ForegroundColor Blue
        psql "`$DATABASE_URL" -f "BUSINESS_DATA.sql" 
    }
    "2" { 
        Write-Host "Restoring permissions system..." -ForegroundColor Blue
        psql "`$DATABASE_URL" -f "PERMISSIONS_SYSTEM.sql"
    }
    "3" { 
        Write-Host "Restoring production cost system..." -ForegroundColor Blue
        psql "`$DATABASE_URL" -f "PRODUCTION_SYSTEM.sql"
    }
    "4" { 
        Write-Host "Restoring analytics system..." -ForegroundColor Blue
        psql "`$DATABASE_URL" -f "ANALYTICS_SYSTEM.sql"
    }
    "5" { 
        Write-Host "Restoring schema only..." -ForegroundColor Blue
        psql "`$DATABASE_URL" -f "SCHEMA_ONLY.sql"
    }
    default { 
        Write-Host "Restore cancelled." -ForegroundColor Yellow 
    }
}
"@
    $selectiveRestoreScript | Out-File "$backupDir\RESTORE_SELECTIVE.ps1"

    # 13. Create comprehensive backup info
    $backupInfo = @"
=============================================================================
COMPLETE DATABASE BACKUP INFORMATION
=============================================================================
Backup Date: $(Get-Date)
Database: Supabase PostgreSQL  
Source Project: wheufegilqkbcsixkoka (aming-test)
Backup Type: COMPLETE CLONE - ALL DATA + SCHEMA

FILES CREATED:
=============
🗃️ COMPLETE_DATABASE.dump       : PRIMARY BACKUP - Full database clone (use pg_restore)
🏢 PUBLIC_SCHEMA.dump          : Your application data only  
🔐 AUTH_SCHEMA.dump            : Supabase authentication data
🏗️ SCHEMA_ONLY.sql             : Database structure only
📋 DATA_ONLY.sql              : All data only (no structure)
💼 BUSINESS_DATA.sql           : Core business tables (orders, clients, products)
🛡️ PERMISSIONS_SYSTEM.sql      : Auth & permissions tables
🏭 PRODUCTION_SYSTEM.sql       : Production cost system
📈 ANALYTICS_SYSTEM.sql        : Analytics & reporting data
📊 DATABASE_STATISTICS.txt     : Record counts and backup metadata
📝 backup.log                 : Detailed backup execution log
📋 BACKUP_CONTENTS_LIST.txt    : List of all backed up objects

RESTORE SCRIPTS:
===============
🔄 RESTORE_COMPLETE_DATABASE.ps1 : Full database replacement (DESTRUCTIVE)
🔧 RESTORE_SELECTIVE.ps1         : Restore specific parts only

RESTORE INSTRUCTIONS:
====================
To restore the COMPLETE database (replaces everything):
> .\RESTORE_COMPLETE_DATABASE.ps1

To restore specific parts only:  
> .\RESTORE_SELECTIVE.ps1

Manual restore (complete):
> pg_restore --clean --if-exists --verbose --dbname="`$DATABASE_URL" COMPLETE_DATABASE.dump

Manual restore (selective):
> psql "`$DATABASE_URL" -f BUSINESS_DATA.sql

DATABASE CONTENTS:
=================
Business Data: 1,360 orders, 635 clients, 100 products
User System: 5 authorized users, 23 permissions, 5 roles
Production: Complete production cost calculation system
Analytics: Daily metrics, client/product performance data
Auth: Complete Supabase authentication system

BACKUP SIZE INFORMATION:
========================
"@ | Out-File "$backupDir\BACKUP_INFO.txt"

    # Add file sizes
    Get-ChildItem $backupDir | Format-Table Name, @{Label="Size (MB)"; Expression={[math]::Round($_.Length / 1MB, 2)}}, LastWriteTime | Out-File "$backupDir\BACKUP_INFO.txt" -Append

    # Calculate total backup size
    $totalSize = (Get-ChildItem $backupDir -Recurse | Measure-Object -Property Length -Sum).Sum
    $totalSizeMB = [math]::Round($totalSize / 1MB, 2)
    $fileCount = (Get-ChildItem $backupDir).Count

    # Final success message
    Write-Host ""
    Write-Success "🎉 COMPLETE DATABASE BACKUP FINISHED!"
    Write-Host ""
    Write-Host "📊 BACKUP SUMMARY:" -ForegroundColor Cyan
    Write-Host "   📁 Directory: $backupDir" -ForegroundColor White
    Write-Host "   💾 Total Size: $totalSizeMB MB" -ForegroundColor White  
    Write-Host "   📄 Files Created: $fileCount" -ForegroundColor White
    Write-Host ""
    Write-Host "🗃️ PRIMARY BACKUP FILE:" -ForegroundColor Cyan
    Write-Host "   COMPLETE_DATABASE.dump (complete clone - use pg_restore)" -ForegroundColor White
    Write-Host ""
    Write-Host "🔄 RESTORE OPTIONS:" -ForegroundColor Cyan  
    Write-Host "   Complete: .\RESTORE_COMPLETE_DATABASE.ps1" -ForegroundColor White
    Write-Host "   Selective: .\RESTORE_SELECTIVE.ps1" -ForegroundColor White
    Write-Host ""
    Write-Host "⚠️  IMPORTANT:" -ForegroundColor Yellow
    Write-Host "   • This is a COMPLETE clone of your database" -ForegroundColor White
    Write-Host "   • Store in secure location before migrations" -ForegroundColor White  
    Write-Host "   • Test restores on non-production environment first" -ForegroundColor White
    Write-Host ""
    Write-Success "✅ Your database is now completely backed up!"

} catch {
    Write-Error "Backup failed: $_"
    Write-Error "Check log file: $logFile"
    exit 1
}