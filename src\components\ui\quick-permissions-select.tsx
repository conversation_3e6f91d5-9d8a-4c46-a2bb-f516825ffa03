import React, { useState } from 'react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from './dropdown-menu'
import { MetricBadge } from './metric-badge'
import { useToast } from '../../hooks/use-toast'
import { Settings, Loader2, ChevronDown, Shield, Plus, Minus } from 'lucide-react'
import { cn } from '../../lib/utils'

interface QuickPermissionsSelectProps {
  userId: string
  currentPermissions: string[]
  size?: 'sm' | 'md' | 'lg'
  onPermissionsChange?: (userId: string, newPermissions: string[]) => Promise<void>
  onManagePermissions?: (userId: string) => void
  disabled?: boolean
}

export function QuickPermissionsSelect({
  userId,
  currentPermissions,
  size = 'md',
  onPermissionsChange,
  onManagePermissions,
  disabled = false
}: QuickPermissionsSelectProps) {
  const { toast } = useToast()
  const [isOpen, setIsOpen] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)

  const quickActions = [
    {
      label: 'Add Basic Access',
      permissions: ['orders.view', 'products.view', 'clients.view'],
      type: 'add' as const,
      icon: Plus,
    },
    {
      label: 'Add Admin Access',
      permissions: ['admin.users', 'system.admin'],
      type: 'add' as const,
      icon: Shield,
    },
    {
      label: 'Remove Admin Access',
      permissions: ['admin.users', 'system.admin'],
      type: 'remove' as const,
      icon: Minus,
    },
  ]

  const handleQuickAction = async (action: typeof quickActions[0]) => {
    if (!onPermissionsChange) return
    
    try {
      setIsUpdating(true)
      let newPermissions = [...currentPermissions]
      
      if (action.type === 'add') {
        // Add permissions that aren't already present
        action.permissions.forEach(permission => {
          if (!newPermissions.includes(permission)) {
            newPermissions.push(permission)
          }
        })
      } else {
        // Remove permissions but ensure at least 1 permission remains
        newPermissions = newPermissions.filter(permission => 
          !action.permissions.includes(permission)
        )
        
        // Prevent removing all permissions - must have at least 1
        if (newPermissions.length === 0) {
          throw new Error('Cannot remove all permissions. Each user must have at least one permission.')
        }
      }
      
      await onPermissionsChange(userId, newPermissions)
      setIsOpen(false)
      
      toast({
        title: 'Permissions Updated',
        description: `User permissions have been successfully updated.`,
      })
    } catch (error) {
      console.error('Failed to update permissions:', error)
      toast({
        title: 'Update Failed',
        description: error instanceof Error ? error.message : 'Failed to update user permissions',
        variant: 'destructive'
      })
    } finally {
      setIsUpdating(false)
    }
  }

  const handleManageClick = () => {
    onManagePermissions?.(userId)
    setIsOpen(false)
  }

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild disabled={disabled}>
        <div
          className={cn(
            "relative inline-flex items-center",
            isUpdating && "cursor-wait",
            !disabled && "cursor-pointer"
          )}
          onClick={(e) => e.stopPropagation()}
        >
          {isUpdating ? (
            <div className="flex items-center animate-pulse">
              <Loader2 className="h-3 w-3 animate-spin mr-1.5 text-gray-600" />
              <MetricBadge
                value={currentPermissions.length}
                label="permissions"
                variant="info"
                className="opacity-60 text-xs"
              />
            </div>
          ) : (
            <div className="group relative">
              <MetricBadge
                value={currentPermissions.length}
                label="permissions"
                variant="info"
                className={cn(
                  "pr-6 text-xs transition-all",
                  !disabled && "hover:opacity-90"
                )}
              />
              {!disabled && (
                <ChevronDown className="h-3 w-3 text-gray-600 dark:text-gray-400 absolute right-1.5 top-1/2 -translate-y-1/2 group-hover:text-gray-800 dark:group-hover:text-gray-200" />
              )}
            </div>
          )}
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent 
        align="start" 
        className="w-56 p-1"
        sideOffset={5}
        onClick={(e) => e.stopPropagation()}
      >
        <DropdownMenuLabel className="text-xs text-muted-foreground">
          Quick Actions
        </DropdownMenuLabel>
        {quickActions.map((action, index) => {
          const IconComponent = action.icon
          const canApply = action.type === 'add' 
            ? !action.permissions.every(p => currentPermissions.includes(p))
            : action.permissions.some(p => currentPermissions.includes(p))
            
          return (
            <DropdownMenuItem
              key={index}
              onClick={(e) => {
                e.stopPropagation()
                handleQuickAction(action)
              }}
              className="cursor-pointer transition-colors text-xs"
              disabled={isUpdating || !canApply}
            >
              <div className="flex items-center gap-2">
                <IconComponent className="h-3.5 w-3.5" />
                <span>{action.label}</span>
              </div>
            </DropdownMenuItem>
          )
        })}
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem
          onClick={(e) => {
            e.stopPropagation()
            handleManageClick()
          }}
          className="cursor-pointer transition-colors text-xs"
          disabled={isUpdating}
        >
          <div className="flex items-center gap-2">
            <Settings className="h-3.5 w-3.5" />
            <span>Manage All Permissions</span>
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}