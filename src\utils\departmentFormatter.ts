/**
 * Department formatting utilities
 * Converts database department codes to human-readable labels
 */

export const DEPARTMENT_LABELS: Record<string, string> = {
  administration: 'Administration',
  sales: 'Sales & Marketing',
  operations: 'Operations',
  customer_service: 'Customer Service',
  finance: 'Finance & Accounting',
  human_resources: 'Human Resources',
  it_support: 'IT Support',
  management: 'Management'
}

/**
 * Format a department code to human-readable label
 * @param department - The department code from database
 * @returns Human-readable department label
 */
export function formatDepartmentLabel(department: string | null | undefined): string {
  if (!department) return '—'
  
  const label = DEPARTMENT_LABELS[department.toLowerCase()]
  if (label) return label
  
  // Fallback: capitalize first letter and replace underscores with spaces
  return department
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

/**
 * Get all department options for selects/dropdowns
 * @returns Array of department options with value and label
 */
export function getDepartmentOptions() {
  return Object.entries(DEPARTMENT_LABELS).map(([value, label]) => ({
    value,
    label
  }))
}