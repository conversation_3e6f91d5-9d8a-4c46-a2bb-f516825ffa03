import React from 'react';
import { Button } from '../../../ui/button';
import { Input } from '../../../ui/input';
import { Label } from '../../../ui/label';
import { ButtonLoading } from '../../../ui/loading-indicator';
import { Mail } from 'lucide-react';

interface EmailStepProps {
  email: string;
  onEmailChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSubmit: () => void;
  loading: boolean;
}

export const EmailStep: React.FC<EmailStepProps> = ({
  email,
  onEmailChange,
  onSubmit,
  loading
}) => {
  return (
    <>
      <div className="space-y-2">
        <Label htmlFor="email" className="flex items-center gap-2 text-black text-sm font-medium">
          <Mail className="h-4 w-4" />
          Email
        </Label>
        <Input
          id="email"
          type="email"
          placeholder="Email"
          value={email}
          onChange={onEmailChange}
          disabled={loading}
          className="bg-gray-100 border-0 rounded-lg px-4 py-3 text-gray-700 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-black transition-colors"
          autoComplete="email"
          required
        />
      </div>

      <Button
        type="submit"
        className="w-full bg-black hover:bg-gray-800 text-white py-3 rounded-lg font-medium transition-all duration-200"
        disabled={loading || !email}
      >
        {loading ? (
          <ButtonLoading text="Processing..." />
        ) : (
          'Continue'
        )}
      </Button>
    </>
  );
};