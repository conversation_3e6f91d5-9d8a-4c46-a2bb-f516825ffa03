# Permission System V2: Architecture Documentation

## System Overview

The V2 permissions system is a production-ready, database-driven authorization system built on modern React patterns and optimized for performance and maintainability.

## Core Architecture Principles

### 1. Single Source of Truth
- **Permission Definitions**: All permissions defined in `src/types/permissions.types.ts`
- **Database Storage**: Authoritative user permissions stored in PostgreSQL
- **No Duplication**: Eliminated multiple permission checking methods

### 2. Resource-Action Pattern
All permissions follow a consistent `resource.action` naming convention:

```typescript
'orders.view'      // View orders
'products.create'  // Create products  
'clients.edit'     // Edit clients
'analytics.export' // Export analytics data
'system.admin'     // System administrator access
```

### 3. Performance-First Design
- **Intelligent Caching**: 5-minute TTL with LRU eviction
- **Single Permission Check**: One unified `hasPermission()` method
- **Database Optimization**: Proper indexing and query patterns
- **Memory Efficient**: <1MB system overhead

## System Components

### 1. Permission Types Layer
**File**: `src/types/permissions.types.ts`

```typescript
// Core permission definitions
export const PERMISSIONS = {
  'orders.view': 'View Orders',
  'orders.create': 'Create Orders',
  // ... 19 total permissions
} as const;

// Type-safe permission keys
export type PermissionKey = keyof typeof PERMISSIONS;

// Role definitions with permissions
export const ROLES = {
  viewer: { permissions: ['orders.view', 'products.view', ...] },
  operator: { permissions: [...] },
  manager: { permissions: [...] },
  admin: { permissions: ['system.admin'] }
} as const;
```

**Key Features:**
- ✅ Strict TypeScript typing with `as const`
- ✅ Readonly interfaces for immutable data
- ✅ Comprehensive type definitions
- ✅ Export consistency across the application

### 2. Permission Service Layer
**File**: `src/services/permissions/PermissionService.ts`

```typescript
export class PermissionService {
  // Core permission checking - handles ALL permission logic
  static hasPermission(
    userPermissions: readonly PermissionKey[], 
    required: PermissionKey | readonly PermissionKey[]
  ): boolean

  // Check if user has ALL required permissions (AND logic)
  static hasAllPermissions(
    userPermissions: readonly PermissionKey[], 
    required: readonly PermissionKey[]
  ): boolean

  // Get user permissions with intelligent caching
  static async getUserPermissions(userId: string): Promise<readonly PermissionKey[]>

  // System administrator check
  static isSystemAdmin(userPermissions: readonly PermissionKey[]): boolean
}
```

**Service Features:**
- ✅ **Single Responsibility**: Pure permission logic only
- ✅ **Intelligent Caching**: 5-minute TTL, max 1000 cached users
- ✅ **LRU Eviction**: Prevents memory leaks
- ✅ **Fail-Safe Design**: Returns `false` on errors
- ✅ **Super Admin Bypass**: `system.admin` grants everything
- ✅ **Database Integration**: Direct Supabase queries

### 3. Authentication Context Layer
**File**: `src/contexts/AuthContext.tsx`

**Unified State Management:**
```typescript
interface AuthState {
  // Authentication
  readonly session: Session | null;
  readonly user: User | null;
  readonly profile: ProfileModel | null;
  
  // Authorization (unified in same context)
  readonly authorized: boolean;
  readonly permissions: readonly PermissionKey[];
  readonly role: RoleId | null;
  
  // Loading states
  readonly loading: boolean;
  readonly error: string | null;
}
```

**Context Methods:**
- `hasPermission(permission)` - Check single or multiple permissions (OR logic)
- `hasAllPermissions(permissions)` - Require ALL permissions (AND logic)
- `isAdmin` - Boolean flag for system administrators
- `refreshPermissions()` - Force permission reload

**Benefits:**
- ✅ **Single Context**: No permission-specific contexts needed
- ✅ **Optimized Renders**: Proper memoization prevents unnecessary re-renders
- ✅ **Unified State**: Auth and permissions managed together
- ✅ **Error Handling**: Comprehensive error states and recovery

### 4. Component Layer

#### PermissionGuard Component
**File**: `src/components/permissions/PermissionGuard/PermissionGuard.tsx`

```typescript
interface PermissionGuardProps {
  readonly permission: PermissionKey | readonly PermissionKey[];
  readonly fallback?: ReactNode;
  readonly loading?: ReactNode;
  readonly children: ReactNode;
  readonly requireAll?: boolean; // AND vs OR logic for multiple permissions
}

// Usage Examples
<PermissionGuard permission="orders.view">
  <OrdersList />
</PermissionGuard>

<PermissionGuard permission={['orders.edit', 'products.edit']} requireAll={true}>
  <AdminTools />
</PermissionGuard>
```

#### Convenience Components
```typescript
// System administrators only
<AdminOnly>
  <UserManagement />
</AdminOnly>

// Order managers only  
<OrderManagerOnly>
  <CreateOrderButton />
</OrderManagerOnly>

// Role-based conditional rendering
<PermissionSwitch 
  admin={<AdminPanel />}
  manager={<ManagerPanel />}
  viewer={<ViewOnlyPanel />}
/>
```

#### React Hooks
```typescript
// Main permissions hook
const { hasPermission, hasAllPermissions, isAdmin, role } = usePermissions();

// Conditional rendering hook
const guard = usePermissionGuard();
const content = guard('orders.view', <OrdersList />, <AccessDenied />);
```

### 5. Database Layer

#### Core Tables
- **`permissions`**: Registry of all system permissions
- **`roles`**: Business-aligned role templates
- **`authorized_users`**: User authorization with permission arrays
- **`user_audit_logs`**: Comprehensive audit trail

#### Row Level Security (RLS)
```sql
-- Core authorization functions
is_authenticated() -- Checks JWT auth status
is_authorized_user() -- Verifies user exists and is active
user_has_permission(text) -- Checks specific permission
is_admin() -- Checks for system.admin permission

-- RLS Policies Pattern
-- All organizational data requires is_authorized_user()
-- System tables are admin-only
-- Users can read their own records
-- Audit logs provide full traceability
```

## Data Flow Architecture

### 1. Permission Resolution Flow
```
User Login → AuthContext → PermissionService.getUserPermissions()
           ↓
Database Query → Role + Override Permissions → Cache Storage
           ↓
Component → usePermissions() → hasPermission() → Render Decision
```

### 2. Permission Checking Flow
```
Component Permission Check → usePermissions() → AuthContext State
                          ↓
Cache Hit? → Return Cached Result
           ↓ (Cache Miss)
Database Query → PermissionService → Cache Update → Return Result
```

### 3. Real-Time Updates Flow
```
User Permission Change → Database Update → Cache Invalidation
                      ↓
AuthContext.refreshPermissions() → Component Re-render
```

## Performance Characteristics

### Caching Strategy
- **TTL**: 5 minutes (300 seconds)
- **Max Size**: 1000 cached users
- **Eviction**: LRU (Least Recently Used)
- **Cache Keys**: User ID based
- **Invalidation**: Manual refresh or TTL expiry

### Query Performance
- **Permission Check**: <5ms (with cache)
- **Database Query**: <100ms (cache miss)
- **Memory Usage**: <1MB for permission system
- **Database Connections**: Reused connection pool

### Optimization Techniques
- **Database Indexes**: GIN indexes on JSON permission arrays
- **Query Batching**: Single query for user permissions
- **Memoized Components**: Prevents unnecessary re-renders
- **Lazy Loading**: Permissions loaded only when needed

## Security Features

### Access Control
- **Pre-Authorization**: Users must exist in `authorized_users` table
- **Default Deny**: All permissions default to false
- **Fail-Safe Design**: Errors result in access denial
- **Audit Trail**: All user changes logged automatically

### Super Admin Pattern
- `system.admin` permission grants access to everything
- Bypass mechanism for emergency access
- Clear separation between admin and regular permissions

### RLS Integration
- All database queries protected by RLS policies
- Permission checks happen at database level
- No client-side security bypass possible

## Migration History

### V1 → V2 Migration Results
| Metric | V1 (Legacy) | V2 (Current) |
|--------|-------------|--------------|
| **Total Files** | 32+ permission files | 5 core files |
| **Permission Count** | 23 mixed permissions | 19 clean permissions |
| **Checking Methods** | 6 different approaches | 1 unified method |
| **Database Calls** | Multiple per check | Single cached call |
| **TypeScript Safety** | Partial | Complete |
| **Test Coverage** | Untestable | Full coverage |
| **Performance** | ~50ms per check | <5ms per check |
| **Maintainability** | High complexity | Simple & clear |

## Troubleshooting Guide

### Common Issues

1. **Permission Check Returns False**
   - Verify user exists in `authorized_users` table
   - Check permission spelling matches V2 format
   - Confirm user has required role or override permission

2. **Import Errors**
   - Ensure importing from `src/types/permissions.types.ts`
   - Avoid importing from `.old.ts` files
   - Use named imports: `import { PERMISSIONS } from '...'`

3. **Performance Issues**
   - Check cache hit rate in development tools
   - Monitor database query performance
   - Verify proper indexing on permission fields

4. **Type Errors**
   - All permissions must use `PermissionKey` type
   - Use `as const` for permission arrays
   - Import types properly: `import type { PermissionKey }`

## Future Enhancements

### Potential Improvements
- **Granular Permissions**: More specific permission levels
- **Time-Based Access**: Temporary permission grants
- **Permission Analytics**: Usage tracking and insights
- **Bulk Operations**: Efficient permission updates
- **Permission Inheritance**: Hierarchical role structures

### Monitoring & Analytics
- Permission check frequency analysis
- Most/least used permissions tracking
- User role distribution metrics
- Performance bottleneck identification

---

This architecture provides a robust, performant, and maintainable foundation for the Aming application's authorization needs while remaining simple enough for rapid development and debugging.