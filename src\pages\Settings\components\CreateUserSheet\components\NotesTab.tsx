import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../../../components/ui/card'
import { Textarea } from '../../../../../components/ui/textarea'
import { Label } from '../../../../../components/ui/label'
import type { CreateUserFormData } from '../types'

interface NotesTabProps {
  formData: CreateUserFormData
  onInputChange: (field: string, value: string) => void
}

export function NotesTab({ formData, onInputChange }: NotesTabProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Additional Notes</CardTitle>
        <CardDescription>
          Add any additional information or special notes about this user.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <Label htmlFor="notes">Notes</Label>
          <Textarea
            id="notes"
            placeholder="Enter any additional notes about this user's role, responsibilities, or special access requirements..."
            value={formData.notes}
            onChange={(e) => onInputChange('notes', e.target.value)}
            rows={4}
          />
        </div>
      </CardContent>
    </Card>
  )
}