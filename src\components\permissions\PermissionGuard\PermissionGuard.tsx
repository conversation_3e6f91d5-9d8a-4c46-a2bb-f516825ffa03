/**
 * PermissionGuard Component
 * 
 * Production-grade permission wrapper following CLAUDE.md guidelines:
 * - Single responsibility: permission-based rendering
 * - <250 lines
 * - Pure component patterns
 * - No mb-0 needed (no paragraph tags)
 */

import React from 'react';
import type { ReactNode } from 'react';
import { usePermissions } from '../../../hooks/permissions/usePermissions';
import type { PermissionKey } from '../../../types/permissions.types';

// ============================================================================
// TYPES
// ============================================================================

export interface PermissionGuardProps {
  readonly permission: PermissionKey | readonly PermissionKey[];
  readonly fallback?: ReactNode;
  readonly loading?: ReactNode;
  readonly children: ReactNode;
  readonly requireAll?: boolean; // For multiple permissions: AND vs OR logic
}

// ============================================================================
// PERMISSION GUARD COMPONENT
// ============================================================================

export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  permission,
  fallback = null,
  loading = <div className="animate-pulse">Loading...</div>,
  children,
  requireAll = false
}) => {
  const { hasPermission, hasAllPermissions, loading: permissionsLoading } = usePermissions();
  
  // Show loading state while permissions are being fetched
  if (permissionsLoading) {
    return <>{loading}</>;
  }
  
  // Check permissions based on requireAll flag
  const hasAccess = Array.isArray(permission) && requireAll
    ? hasAllPermissions(permission)
    : hasPermission(permission);
  
  // Render children if permission granted, otherwise show fallback
  return hasAccess ? <>{children}</> : <>{fallback}</>;
};

// ============================================================================
// CONVENIENCE COMPONENTS
// ============================================================================

/**
 * Show content only to system administrators
 */
export const AdminOnly: React.FC<{ children: ReactNode; fallback?: ReactNode }> = ({
  children,
  fallback = null
}) => (
  <PermissionGuard permission="system.admin" fallback={fallback}>
    {children}
  </PermissionGuard>
);

/**
 * Show content only to users who can manage orders
 */
export const OrderManagerOnly: React.FC<{ children: ReactNode; fallback?: ReactNode }> = ({
  children,
  fallback = null
}) => (
  <PermissionGuard permission={['orders.create', 'orders.edit']} fallback={fallback}>
    {children}
  </PermissionGuard>
);

/**
 * Show different content based on permission level
 */
interface PermissionSwitchProps {
  readonly admin?: ReactNode;
  readonly manager?: ReactNode;
  readonly operator?: ReactNode;
  readonly viewer?: ReactNode;
  readonly fallback?: ReactNode;
}

export const PermissionSwitch: React.FC<PermissionSwitchProps> = ({
  admin,
  manager,
  operator,
  viewer,
  fallback = null
}) => {
  const { hasPermission } = usePermissions();
  
  // Check permissions in order of hierarchy
  if (admin && hasPermission('system.admin')) {
    return <>{admin}</>;
  }
  
  if (manager && hasPermission(['orders.delete', 'products.edit', 'analytics.export'])) {
    return <>{manager}</>;
  }
  
  if (operator && hasPermission(['orders.create', 'orders.edit'])) {
    return <>{operator}</>;
  }
  
  if (viewer && hasPermission(['orders.view', 'products.view'])) {
    return <>{viewer}</>;
  }
  
  return <>{fallback}</>;
};