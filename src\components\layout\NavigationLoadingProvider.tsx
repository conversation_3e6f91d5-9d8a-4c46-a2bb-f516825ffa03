import React, { createContext, useContext, useState, useTransition, ReactNode } from 'react'

interface NavigationLoadingContextType {
  isNavigating: boolean
  isPending: boolean
  startNavigationTransition: (callback: () => void) => void
}

const NavigationLoadingContext = createContext<NavigationLoadingContextType | null>(null)

export const useNavigationLoading = () => {
  const context = useContext(NavigationLoadingContext)
  if (!context) {
    throw new Error('useNavigationLoading must be used within NavigationLoadingProvider')
  }
  return context
}

interface NavigationLoadingProviderProps {
  children: ReactNode
}

export function NavigationLoadingProvider({ children }: NavigationLoadingProviderProps) {
  const [isNavigating, setIsNavigating] = useState(false)
  const [isPending, startTransition] = useTransition()

  const startNavigationTransition = (callback: () => void) => {
    setIsNavigating(true)
    
    startTransition(() => {
      callback()
      // Reset navigating state after transition
      setTimeout(() => setIsNavigating(false), 150)
    })
  }

  return (
    <NavigationLoadingContext.Provider value={{
      isNavigating,
      isPending,
      startNavigationTransition
    }}>
      {children}
    </NavigationLoadingContext.Provider>
  )
}