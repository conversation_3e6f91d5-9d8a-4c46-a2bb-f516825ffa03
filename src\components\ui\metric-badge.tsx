import React from 'react'
import { Badge } from './badge'
import { cn } from '../../lib/utils'

interface MetricBadgeProps {
  value: number | string
  label?: string
  variant?: 'primary' | 'secondary' | 'info' | 'success' | 'warning' | 'neutral'
  className?: string
}

export function MetricBadge({ value, label, variant = 'neutral', className }: MetricBadgeProps) {
  const content = label ? `${value} ${label}` : value

  const getVariantClass = () => {
    switch (variant) {
      case 'primary':
        return 'bg-blue-100 text-blue-800 border-blue-300'
      case 'secondary':
        return 'bg-purple-100 text-purple-800 border-purple-300'
      case 'info':
        return 'bg-cyan-100 text-cyan-800 border-cyan-300'
      case 'success':
        return 'bg-green-100 text-green-800 border-green-300'
      case 'warning':
        return 'bg-amber-100 text-amber-800 border-amber-300'
      case 'neutral':
        return 'bg-gray-100 text-gray-800 border-gray-300'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300'
    }
  }

  return (
    <Badge 
      variant="outline"
      className={cn(getVariantClass(), className)}
    >
      {content}
    </Badge>
  )
}