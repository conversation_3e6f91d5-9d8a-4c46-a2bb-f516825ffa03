-- Migration: Standardize attribute_type casing to prevent category mapping issues
-- This migration ensures all attribute_type values use consistent UPPERCASE format

BEGIN;

-- Update all lowercase attribute types to uppercase
UPDATE product_attributes 
SET attribute_type = UPPER(attribute_type),
    updated_at = NOW()
WHERE attribute_type != UPPER(attribute_type);

-- Log the changes for audit
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % attribute_type values to uppercase', updated_count;
END $$;

-- Create a function to enforce uppercase attribute_type on insert/update
CREATE OR REPLACE FUNCTION enforce_uppercase_attribute_type()
RETURNS TRIGGER AS $$
BEGIN
    NEW.attribute_type = UPPER(NEW.attribute_type);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically uppercase attribute_type
DROP TRIGGER IF EXISTS ensure_uppercase_attribute_type ON product_attributes;
CREATE TRIGGER ensure_uppercase_attribute_type
    BEFORE INSERT OR UPDATE OF attribute_type ON product_attributes
    FOR EACH ROW
    EXECUTE FUNCTION enforce_uppercase_attribute_type();

-- Add a check constraint to ensure only specific uppercase values are allowed
ALTER TABLE product_attributes 
DROP CONSTRAINT IF EXISTS check_attribute_type_values;

ALTER TABLE product_attributes
ADD CONSTRAINT check_attribute_type_values CHECK (
    attribute_type IN (
        'CATEGORY',
        'PRODUCT_TYPE',
        'SIZE',
        'ADDITIONAL_CHARGE',
        'AGENT',
        'BOX_TYPE',
        'COVER_TYPE',
        'LAMINATION_TYPE',
        'PRODUCT_STATUS',
        'SECTOR'
    )
);

COMMIT;

-- Verify the migration
SELECT attribute_type, COUNT(*) as count 
FROM product_attributes 
GROUP BY attribute_type 
ORDER BY attribute_type;