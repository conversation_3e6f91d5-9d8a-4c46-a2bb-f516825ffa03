import React from 'react'
import { Avatar, AvatarFallback } from '../../../../../components/ui/avatar'
import { Button } from '../../../../../components/ui/button'
import { StatusBadge } from '../../../../../components/ui/status-badge'
import { QuickRoleSelect } from '../../../../../components/ui/quick-role-select'
import { QuickPermissionsSelect } from '../../../../../components/ui/quick-permissions-select'
import { TableCell, TableRow } from '../../../../../components/ui/table'
import { AdminService } from '../../../../../services/admin'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../../../../../components/ui/dropdown-menu'
import {
  More<PERSON><PERSON>zon<PERSON>,
  <PERSON>r<PERSON>heck,
  UserX,
  Edit,
  Trash2,
  Mail,
} from 'lucide-react'
import type { AuthorizedUser } from '../../../../../services/admin'

interface UserTableRowProps {
  user: AuthorizedUser
  onEdit: (user: AuthorizedUser) => void
  onToggleStatus: (user: AuthorizedUser) => void
  onDelete: (user: AuthorizedUser) => void
  onPermissionUpdate: (userId: string, permissions: string[]) => Promise<void>
  processingUserId: string | null
  canEdit: boolean
  canDelete: boolean
}

export function UserTableRow({
  user,
  onEdit,
  onToggleStatus,
  onDelete,
  onPermissionUpdate,
  processingUserId,
  canEdit,
  canDelete
}: UserTableRowProps) {
  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase()
  }

  return (
    <TableRow>
      {/* Name with Avatar */}
      <TableCell>
        <div className="flex items-center gap-3">
          <Avatar className="h-8 w-8">
            <AvatarFallback className="text-xs">
              {getInitials(user.first_name, user.last_name)}
            </AvatarFallback>
          </Avatar>
          <div>
            <p className="font-medium text-sm mb-0">
              {user.first_name} {user.last_name}
            </p>
          </div>
        </div>
      </TableCell>
      
      {/* Email */}
      <TableCell>
        <div className="flex items-center gap-1">
          <Mail className="h-3 w-3 text-muted-foreground" />
          <span className="text-sm">{user.email}</span>
        </div>
      </TableCell>
      
      {/* Interactive Role */}
      <TableCell>
        <QuickRoleSelect
          userId={user.id}
          currentRole={user.role_template || 'user'}
          size="sm"
          onRoleChange={async (userId, newRole) => {
            // TODO: Implement role update
            console.log('Update role:', userId, newRole)
          }}
          disabled={!canEdit}
        />
      </TableCell>
      
      {/* Interactive Permissions */}
      <TableCell>
        <QuickPermissionsSelect
          userId={user.id}
          currentPermissions={[...user.permissions]}
          size="sm"
          onPermissionsChange={async (userId, newPermissions) => {
            // Validate at least 1 permission is required
            if (newPermissions.length === 0) {
              throw new Error('At least one permission must be assigned to each user account')
            }
            
            await onPermissionUpdate(userId, newPermissions)
          }}
          onManagePermissions={(userId) => {
            // TODO: Open permissions management dialog
            console.log('Manage permissions for:', userId)
          }}
          disabled={!canEdit}
        />
      </TableCell>
      
      {/* Status */}
      <TableCell>
        <StatusBadge 
          status={user.is_active ? "active" : "inactive"}
          withIcon={true}
          size="sm"
        >
          {user.is_active ? "Active" : "Inactive"}
        </StatusBadge>
      </TableCell>
      
      {/* Department */}
      <TableCell>
        <span className="text-sm">
          {user.department || <span className="text-muted-foreground">—</span>}
        </span>
      </TableCell>
      
      {/* Actions */}
      <TableCell className="text-right">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="ghost" 
              size="sm"
              disabled={processingUserId === user.id}
            >
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {canEdit && (
              <DropdownMenuItem onClick={() => onEdit(user)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit User
              </DropdownMenuItem>
            )}
            
            {canEdit && (
              <DropdownMenuItem 
                onClick={() => onToggleStatus(user)}
                disabled={processingUserId === user.id}
              >
                {user.is_active ? (
                  <>
                    <UserX className="h-4 w-4 mr-2" />
                    Deactivate
                  </>
                ) : (
                  <>
                    <UserCheck className="h-4 w-4 mr-2" />
                    Reactivate
                  </>
                )}
              </DropdownMenuItem>
            )}
            
            {canDelete && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  className="text-red-600"
                  onClick={() => onDelete(user)}
                  disabled={processingUserId === user.id}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete User
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </TableRow>
  )
}