import useSWR from 'swr'
import { fetchOrdersForCurrentYear } from '../../services'

// Key generator for year orders
export const getYearOrdersKey = () => '/orders/year'

/**
 * Hook for fetching all orders for the current year (for metrics)
 */
export function useYearOrders() {
  const { data, error, isLoading, isValidating } = useSWR(
    getYearOrdersKey(),
    async () => {
      const data = await fetchOrdersForCurrentYear()
      return data
    },
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      dedupingInterval: 60000, // 1 minute
    }
  )

  return {
    yearOrders: data || [],
    isLoading,
    isValidating,
    isError: !!error,
    error
  }
}