/**
 * Permission System V2 Types
 * 
 * Production-ready type definitions following CLAUDE.md guidelines:
 * - Strict TypeScript typing
 * - Readonly for immutable data
 * - Discriminated unions for complex state
 * - No 'any' types
 */

import type { ReactNode } from 'react';

// ============================================================================
// PERMISSION CONSTANTS
// ============================================================================

export const PERMISSIONS = {
  // Orders Resource
  'orders.view': 'View Orders',
  'orders.create': 'Create Orders', 
  'orders.edit': 'Edit Orders',
  'orders.delete': 'Delete Orders',
  
  // Products Resource
  'products.view': 'View Products',
  'products.create': 'Create Products',
  'products.edit': 'Edit Products', 
  'products.delete': 'Delete Products',
  
  // Clients Resource
  'clients.view': 'View Clients',
  'clients.create': 'Create Clients',
  'clients.edit': 'Edit Clients',
  'clients.delete': 'Delete Clients',
  
  // Analytics Resource
  'analytics.view': 'View Analytics',
  'analytics.export': 'Export Analytics',
  
  // Settings Resource
  'settings.view': 'View Settings',
  'settings.edit': 'Edit Settings',
  
  // Admin Resource
  'admin.users': 'Manage Users',
  'admin.permissions': 'Assign Permissions',
  'system.admin': 'System Administrator'
} as const;

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export type PermissionKey = keyof typeof PERMISSIONS;

// All permissions as an array for utility functions
export const ALL_PERMISSIONS = Object.keys(PERMISSIONS) as PermissionKey[];

export type ResourceType = 'orders' | 'products' | 'clients' | 'analytics' | 'settings' | 'admin' | 'system';

export type ActionType = 'view' | 'create' | 'edit' | 'delete' | 'export' | 'users' | 'permissions' | 'admin';

// ============================================================================
// ROLE DEFINITIONS
// ============================================================================

export const ROLES = {
  viewer: {
    id: 'viewer',
    name: 'Viewer',
    description: 'Read-only access to business data',
    permissions: ['orders.view', 'products.view', 'clients.view', 'analytics.view'] as const
  },
  operator: {
    id: 'operator', 
    name: 'Operator',
    description: 'Daily operations - create and edit records',
    permissions: [
      'orders.view', 'orders.create', 'orders.edit',
      'products.view',
      'clients.view', 'clients.create', 'clients.edit',
      'analytics.view'
    ] as const
  },
  manager: {
    id: 'manager',
    name: 'Manager', 
    description: 'Management access - full business operations',
    permissions: [
      'orders.view', 'orders.create', 'orders.edit', 'orders.delete',
      'products.view', 'products.create', 'products.edit',
      'clients.view', 'clients.create', 'clients.edit', 'clients.delete',
      'analytics.view', 'analytics.export',
      'settings.view'
    ] as const
  },
  admin: {
    id: 'admin',
    name: 'Administrator',
    description: 'Full system administration',
    permissions: ['system.admin'] as const
  }
} as const;

export type RoleId = keyof typeof ROLES;

export type Role = typeof ROLES[RoleId];

// ============================================================================
// USER PERMISSION TYPES
// ============================================================================

export interface UserPermissions {
  readonly userId: string;
  readonly role: RoleId;
  readonly permissions: readonly PermissionKey[];
  readonly overrides: readonly PermissionKey[];
}

// ============================================================================
// PERMISSION CHECK TYPES
// ============================================================================

export type PermissionCheckResult = 
  | { readonly hasPermission: true; readonly reason?: never }
  | { readonly hasPermission: false; readonly reason: string };

export interface BulkPermissionCheckResult {
  readonly [key: string]: boolean;
}

// ============================================================================
// PERMISSION SERVICE TYPES
// ============================================================================

export interface PermissionCache {
  readonly permissions: readonly PermissionKey[];
  readonly timestamp: number;
  readonly ttl: number;
}

export interface PermissionServiceConfig {
  readonly cacheTTL: number;
  readonly maxCacheSize: number;
  readonly enableLogging: boolean;
}

// ============================================================================
// HOOK RETURN TYPES
// ============================================================================

export interface UsePermissionsResult {
  readonly permissions: readonly PermissionKey[];
  readonly role: RoleId | null;
  readonly loading: boolean;
  readonly error: string | null;
  readonly isAdmin: boolean;
  readonly hasPermission: (required: PermissionKey | readonly PermissionKey[]) => boolean;
  readonly hasAllPermissions: (required: readonly PermissionKey[]) => boolean;
  readonly refreshPermissions: () => Promise<void>;
  // Legacy compatibility methods
  readonly checkPermission: (permission: PermissionKey) => boolean;
  readonly checkMultiplePermissions: (permissions: readonly PermissionKey[]) => BulkPermissionCheckResult;
}

// ============================================================================
// COMPONENT TYPES
// ============================================================================

export interface PermissionWrapperProps {
  readonly permissions: PermissionKey | readonly PermissionKey[];
  readonly requireAll?: boolean;
  readonly children: ReactNode;
  readonly fallback?: ReactNode;
}

// Database Permission Record
export interface Permission {
  readonly id: number;
  readonly key: PermissionKey;
  readonly name: string;
  readonly description: string | null;
  readonly category: string;
  readonly isActive: boolean;
  readonly requiresPermissions: readonly PermissionKey[];
  readonly createdAt: string;
  readonly updatedAt: string;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type PermissionsByResource = {
  readonly [K in ResourceType]: readonly PermissionKey[];
};

export const PERMISSIONS_BY_RESOURCE: PermissionsByResource = {
  orders: ['orders.view', 'orders.create', 'orders.edit', 'orders.delete'],
  products: ['products.view', 'products.create', 'products.edit', 'products.delete'],
  clients: ['clients.view', 'clients.create', 'clients.edit', 'clients.delete'],
  analytics: ['analytics.view', 'analytics.export'],
  settings: ['settings.view', 'settings.edit'],
  admin: ['admin.users', 'admin.permissions'],
  system: ['system.admin']
} as const;

// ============================================================================
// VALIDATION FUNCTIONS
// ============================================================================

export const isValidPermissionKey = (key: unknown): key is PermissionKey => {
  return typeof key === 'string' && key in PERMISSIONS;
};

export const isValidRoleId = (id: unknown): id is RoleId => {
  return typeof id === 'string' && id in ROLES;
};

export const getResourceFromPermission = (permission: PermissionKey): ResourceType => {
  const [resource] = permission.split('.') as [ResourceType];
  return resource;
};

export const getActionFromPermission = (permission: PermissionKey): ActionType => {
  const [, action] = permission.split('.') as [ResourceType, ActionType];
  return action;
};

// ============================================================================
// ERROR TYPES
// ============================================================================

export class PermissionError extends Error {
  constructor(
    message: string,
    public readonly code: 'INVALID_PERMISSION' | 'ACCESS_DENIED' | 'CACHE_ERROR' | 'VALIDATION_ERROR'
  ) {
    super(message);
    this.name = 'PermissionError';
  }
}