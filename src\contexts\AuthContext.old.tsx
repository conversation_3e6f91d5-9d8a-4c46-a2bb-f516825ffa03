import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import type { Session, User } from '@supabase/supabase-js';
import { AuthService } from '../services/auth/authService';
import { AuthorizationService } from '../services/auth/authorizationService';
import { getProfile, ProfileModel } from '../services/profileService';
import type { AuthorizedUser } from '../services/preAuthService';
import { logger } from '../utils/logger';
import authReadyState from '../utils/authReadyState';

const authLogger = logger.withPrefix('AuthContext');

interface AuthContextType {
  // Authentication State
  session: Session | null;
  user: User | null;
  profile: ProfileModel | null;
  
  // Authorization State
  authorized: boolean;
  permissions: string[];
  authorizedUser: AuthorizedUser | null;
  
  // Loading States
  loading: boolean;
  profileLoading: boolean;
  permissionsLoading: boolean;
  
  // Authentication Methods
  signInWithEmail: (email: string) => Promise<void>;
  verifyEmailOTP: (email: string, token: string) => Promise<void>;
  signOut: () => Promise<void>;
  
  // Utility Methods
  refreshSession: () => Promise<void>;
  refreshProfile: () => Promise<void>;
  refreshPermissions: () => Promise<void>;
  hasPermission: (permission: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

/**
 * Real AuthProvider implementation with Supabase integration
 * Following CLAUDE.md guidelines - focused functionality under 250 lines
 */
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Authentication state
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<ProfileModel | null>(null);
  
  // Authorization state
  const [authorized, setAuthorized] = useState<boolean>(false);
  const [permissions, setPermissions] = useState<string[]>([]);
  const [authorizedUser, setAuthorizedUser] = useState<AuthorizedUser | null>(null);
  
  // Loading states
  const [loading, setLoading] = useState(true);
  const [profileLoading, setProfileLoading] = useState(false);
  const [permissionsLoading, setPermissionsLoading] = useState(false);

  // Check authorization when user email is available
  const checkAuthorization = useCallback(async (email: string) => {
    setPermissionsLoading(true);
    try {
      const authUser = await AuthorizationService.checkUserAuthorization(email);
      
      if (authUser) {
        setAuthorized(true);
        setPermissions(authUser.permissions);
        setAuthorizedUser(authUser);
        
        // Update last login
        await AuthorizationService.updateLastLogin(email);
        
        authLogger.info('User authorized successfully', { email });
      } else {
        setAuthorized(false);
        setPermissions([]);
        setAuthorizedUser(null);
        authLogger.warn('User not authorized', { email });
      }
    } catch (error) {
      authLogger.error('Error checking authorization:', error);
      setAuthorized(false);
      setPermissions([]);
      setAuthorizedUser(null);
    } finally {
      setPermissionsLoading(false);
    }
  }, []);

  // Load user profile with fallback creation
  const loadProfile = useCallback(async (userId: string) => {
    setProfileLoading(true);
    try {
      // First try to get existing profile
      let userProfile = await getProfile(userId);
      
      // If profile doesn't exist, try to ensure it's created
      if (!userProfile) {
        authLogger.debug('Profile not found, attempting to ensure creation for user:', userId);
        const { ensureProfileExists } = await import('../services/profileService');
        userProfile = await ensureProfileExists(userId);
      }
      
      setProfile(userProfile);
    } catch (error) {
      authLogger.error('Error loading profile:', error);
      setProfile(null);
    } finally {
      setProfileLoading(false);
    }
  }, []);

  // Initialize auth state
  useEffect(() => {
    let mounted = true;
    let initializationTimeout: NodeJS.Timeout;

    const initializeAuth = async () => {
      try {
        authLogger.debug('Starting auth initialization...');
        
        // Set a failsafe timeout to prevent infinite loading
        initializationTimeout = setTimeout(() => {
          if (mounted) {
            authLogger.warn('Auth initialization timeout - proceeding with no user');
            setLoading(false);
            authReadyState.setReady(true, 'timeout-fallback');
          }
        }, 15000); // 15 second absolute timeout

        const currentSession = await AuthService.getCurrentSession();
        
        if (!mounted) return;
        
        setSession(currentSession);
        setUser(currentSession?.user || null);
        
        if (currentSession?.user?.email) {
          authLogger.debug('User session found, checking authorization and loading profile...');
          
          // Run authorization and profile loading in parallel with timeouts
          const authPromise = checkAuthorization(currentSession.user.email).catch(error => {
            authLogger.error('Authorization check failed:', error);
            return null; // Continue even if authorization fails
          });
          
          const profilePromise = loadProfile(currentSession.user.id).catch(error => {
            authLogger.error('Profile loading failed:', error);
            return null; // Continue even if profile loading fails
          });
          
          // Add timeout to prevent hanging
          const timeoutPromise = new Promise((resolve) => {
            setTimeout(() => {
              authLogger.warn('Auth initialization taking too long, proceeding anyway');
              resolve(null);
            }, 8000); // 8 second timeout
          });
          
          try {
            await Promise.race([
              Promise.all([authPromise, profilePromise]),
              timeoutPromise
            ]);
          } catch (error) {
            authLogger.error('Error during parallel auth operations:', error);
          }
        }
        
        if (mounted) {
          clearTimeout(initializationTimeout);
          setLoading(false);
          authReadyState.setReady(true, 'real-auth-context');
          authLogger.debug('Auth initialization completed successfully');
        }
      } catch (error) {
        authLogger.error('Critical error initializing auth:', error);
        if (mounted) {
          clearTimeout(initializationTimeout);
          setLoading(false);
          authReadyState.setReady(true, 'real-auth-context-error');
        }
      }
    };

    initializeAuth();

    // Listen to auth changes
    const { data: { subscription } } = AuthService.onAuthStateChange(async (event, session) => {
      if (!mounted) return;
      
      authLogger.debug('Auth state changed:', { event });
      
      setSession(session);
      setUser(session?.user || null);
      
      if (session?.user?.email) {
        authLogger.debug('Auth state change - user found, checking authorization and loading profile...');
        
        // Run authorization and profile loading in parallel with error handling
        const authPromise = checkAuthorization(session.user.email).catch(error => {
          authLogger.error('Authorization check failed during auth change:', error);
          return null;
        });
        
        const profilePromise = loadProfile(session.user.id).catch(error => {
          authLogger.error('Profile loading failed during auth change:', error);
          return null;
        });
        
        // Add timeout to prevent hanging during auth changes
        const timeoutPromise = new Promise((resolve) => {
          setTimeout(() => {
            authLogger.warn('Auth change operations taking too long, continuing anyway');
            resolve(null);
          }, 5000); // 5 second timeout for auth changes
        });
        
        try {
          await Promise.race([
            Promise.all([authPromise, profilePromise]),
            timeoutPromise
          ]);
        } catch (error) {
          authLogger.error('Error during auth change operations:', error);
        }
      } else {
        authLogger.debug('No user in session, clearing auth state');
        setAuthorized(false);
        setPermissions([]);
        setAuthorizedUser(null);
        setProfile(null);
        setPermissionsLoading(false);
      }
    });

    return () => {
      mounted = false;
      clearTimeout(initializationTimeout);
      subscription.unsubscribe();
    };
  }, [checkAuthorization, loadProfile]);

  // Authentication methods
  const signInWithEmail = useCallback(async (email: string) => {
    await AuthService.signInWithEmail(email);
  }, []);

  const verifyEmailOTP = useCallback(async (email: string, token: string) => {
    const result = await AuthService.verifyEmailOTP(email, token);
    
    if (result.user?.email) {
      authLogger.debug('Email OTP verified, checking authorization and loading profile...');
      
      // Run authorization and profile loading with error handling
      const authPromise = checkAuthorization(result.user.email).catch(error => {
        authLogger.error('Authorization check failed after OTP verification:', error);
        return null;
      });
      
      const profilePromise = result.user.id ? loadProfile(result.user.id).catch(error => {
        authLogger.error('Profile loading failed after OTP verification:', error);
        return null;
      }) : Promise.resolve(null);
      
      try {
        await Promise.all([authPromise, profilePromise]);
        authLogger.debug('Post-OTP verification setup completed');
      } catch (error) {
        authLogger.error('Error during post-OTP verification setup:', error);
      }
    }
  }, [checkAuthorization, loadProfile]);

  const signOut = useCallback(async () => {
    try {
      authLogger.info('Initiating user sign out...');
      
      // Clear auth state immediately for better UX
      setSession(null);
      setUser(null);
      setAuthorized(false);
      setPermissions([]);
      setAuthorizedUser(null);
      setProfile(null);
      setProfileLoading(false);
      setPermissionsLoading(false);
      
      // Call Supabase signOut
      await AuthService.signOut();
      
      // Reset auth ready state for next login
      authReadyState.reset('sign-out');
      
      authLogger.info('User signed out successfully');
    } catch (error) {
      authLogger.error('Error during sign out:', error);
      
      // Even if signOut fails, clear local state to prevent hanging
      setSession(null);
      setUser(null);
      setAuthorized(false);
      setPermissions([]);
      setAuthorizedUser(null);
      setProfile(null);
      setProfileLoading(false);
      setPermissionsLoading(false);
      
      // Still reset auth ready state
      authReadyState.reset('sign-out-error');
      
      // Re-throw error so calling component can handle it
      throw error;
    }
  }, []);

  // Utility methods
  const refreshSession = useCallback(async () => {
    const newSession = await AuthService.refreshSession();
    setSession(newSession);
    setUser(newSession?.user || null);
  }, []);

  const refreshProfile = useCallback(async () => {
    if (user?.id) {
      await loadProfile(user.id);
    }
  }, [user?.id, loadProfile]);

  const refreshPermissions = useCallback(async () => {
    if (user?.email) {
      await checkAuthorization(user.email);
    }
  }, [user?.email, checkAuthorization]);

  const hasPermission = useCallback((permission: string): boolean => {
    return permissions.includes(permission) || permissions.includes('system.full_access');
  }, [permissions]);

  const value: AuthContextType = {
    session,
    user,
    profile,
    authorized,
    permissions,
    authorizedUser,
    loading,
    profileLoading,
    permissionsLoading,
    signInWithEmail,
    verifyEmailOTP,
    signOut,
    refreshSession,
    refreshProfile,
    refreshPermissions,
    hasPermission
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

/**
 * Hook to use the auth context
 */
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};