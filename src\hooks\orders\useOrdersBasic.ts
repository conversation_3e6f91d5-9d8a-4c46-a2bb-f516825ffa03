import useSWR from 'swr'
import { fetchOrders, fetchOrdersMetrics } from '../../services'
import type { Order } from '../../pages/Orders/types'
import type { OrdersMetricsData } from '../../types/metrics.types'

// Key generator functions for SWR
export const getOrdersKey = () => '/orders'
export const getMetricsKey = () => '/orders/metrics'

/**
 * Hook for fetching all orders with basic functionality
 */
export function useOrdersBasic() {
  const { data, error, isLoading, isValidating, mutate } = useSWR(
    getOrdersKey(),
    async () => {
      const data = await fetchOrders()
      return data
    },
    {
      revalidateOnFocus: true,
      revalidateOnReconnect: true,
      dedupingInterval: 5000, // 5 seconds
    }
  )

  // Custom mutate function that can accept options
  const customMutate = async (
    dataOrFn?: ((currentData: Order[] | undefined) => Promise<Order[]>) | Order[],
    options?: {
      dateRange?: { from?: Date; to?: Date }
      quarter?: string
    }
  ) => {
    if (options) {
      // If options are provided, fetch data with those options
      return mutate(async () => {
        const data = await fetchOrders(options)
        return data
      })
    } else {
      // Otherwise, use the standard mutate function
      return mutate(dataOrFn)
    }
  }

  return {
    orders: data || [],
    isLoading,
    isValidating,
    isError: !!error,
    error,
    mutate: customMutate
  }
}

/**
 * Hook for fetching only the metrics data (optimized version)
 */
export function useOrdersMetrics() {
  const { data, error, isLoading, isValidating } = useSWR(
    getMetricsKey(),
    async () => {
      const data = await fetchOrdersMetrics()
      return data
    },
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      dedupingInterval: 60000, // 1 minute
      // Keep showing stale data while revalidating
      keepPreviousData: true
    }
  )

  return {
    metrics: data || {
      totalOrders: 0,
      pendingOrders: 0,
      totalRevenue: 0,
      averageOrder: 0
    },
    isLoading,
    isValidating,
    isError: !!error,
    error
  }
}