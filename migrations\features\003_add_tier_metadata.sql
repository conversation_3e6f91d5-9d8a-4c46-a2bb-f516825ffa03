-- Migration: Add tier_metadata column for distribution tier support
-- This column stores base values and configuration for distribution tiers

-- Add tier_metadata column to calculation_templates
ALTER TABLE calculation_templates
ADD COLUMN IF NOT EXISTS tier_metadata jsonb DEFAULT '{}'::jsonb;

-- Add comment explaining the column
COMMENT ON COLUMN calculation_templates.tier_metadata IS 'Stores tier-specific configuration like base values for distribution tiers';

-- Update existing templates with empty metadata
UPDATE calculation_templates
SET tier_metadata = '{}'::jsonb
WHERE tier_metadata IS NULL;

-- Example tier_metadata structure for distribution tiers:
-- {
--   "base_value": 50000,  -- Base cost to distribute
--   "distribution_context": "number_of_sheets", -- or "quantity"
--   "minimum_divisor": 1  -- Prevent divide by zero
-- }