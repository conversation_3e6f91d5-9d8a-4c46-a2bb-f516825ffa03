import React from 'react'
import { But<PERSON> } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { Label } from '../../../components/ui/label'
import { Textarea } from '../../../components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/card'
import type { ProfileData } from '../../../types/profiles.types'

interface ProfileSectionProps {
  profile: ProfileData | null
  user: { email?: string; id: string } | null
  profileLoading: boolean
  profileForm: {
    firstName: string
    lastName: string
    bio: string
    phone: string
    avatar_url: string
  }
  isProfileSaving: boolean
  onProfileFormChange: (field: string, value: string) => void
  onSaveProfile: () => void
}

export const ProfileSection: React.FC<ProfileSectionProps> = ({
  profile,
  user,
  profileLoading,
  profileForm,
  isProfileSaving,
  onProfileFormChange,
  onSaveProfile
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Profile Information</CardTitle>
        <CardDescription>Update your personal information</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {profileLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="w-8 h-8 border-2 border-t-[#613AEB] border-r-[#613AEB] border-b-transparent border-l-transparent rounded-full animate-spin mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading profile...</p>
            </div>
          </div>
        ) : (
          <>
            <div className="flex items-center space-x-4">
              <div className="w-20 h-20 rounded-full bg-gray-200 overflow-hidden flex items-center justify-center">
                {profile?.avatar_url ? (
                  <img 
                    src={profile.avatar_url} 
                    alt={profile.full_name || 'User'} 
                    className="w-full h-full object-cover" 
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-[#F0ECFD] text-[#613AEB] text-2xl font-medium">
                    {profile?.full_name?.charAt(0).toUpperCase() || 
                     user?.email?.charAt(0).toUpperCase() || 'U'}
                  </div>
                )}
              </div>
              <div className="space-y-2">
                <Button variant="outline" size="sm">Change Photo</Button>
                <div className="space-y-1">
                  <Label htmlFor="avatar_url" className="text-xs text-gray-600">Avatar URL</Label>
                  <Input 
                    id="avatar_url" 
                    placeholder="Enter image URL"
                    value={profileForm.avatar_url}
                    onChange={(e) => onProfileFormChange('avatar_url', e.target.value)}
                    className="text-xs"
                  />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="firstName">First Name</Label>
                <Input 
                  id="firstName" 
                  value={profileForm.firstName}
                  onChange={(e) => onProfileFormChange('firstName', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="lastName">Last Name</Label>
                <Input 
                  id="lastName" 
                  value={profileForm.lastName}
                  onChange={(e) => onProfileFormChange('lastName', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="email">Email</Label>
                <Input 
                  id="email" 
                  type="email" 
                  value={user?.email || ''} 
                  disabled
                  className="bg-gray-50"
                />
                <p className="text-xs text-gray-500 mt-1">Email cannot be changed</p>
              </div>
              <div>
                <Label htmlFor="phone">Phone</Label>
                <Input 
                  id="phone" 
                  type="tel" 
                  value={profileForm.phone}
                  onChange={(e) => onProfileFormChange('phone', e.target.value)}
                  placeholder="Coming soon"
                  disabled
                  className="bg-gray-50"
                />
              </div>
              <div className="md:col-span-2">
                <Label htmlFor="bio">Bio</Label>
                <Textarea 
                  id="bio" 
                  rows={3} 
                  value={profileForm.bio}
                  onChange={(e) => onProfileFormChange('bio', e.target.value)}
                  placeholder="Tell us about yourself (coming soon)"
                  disabled
                  className="bg-gray-50"
                />
              </div>
            </div>

            <div className="flex justify-end">
              <Button 
                onClick={onSaveProfile}
                disabled={isProfileSaving}
              >
                {isProfileSaving ? 'Saving...' : 'Save Changes'}
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}