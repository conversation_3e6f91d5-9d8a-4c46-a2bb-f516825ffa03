-- =============================================================================
-- ADD DELETE PERMISSIONS FOR STRATEGIC FEATURE CONTROL
-- Migration: 029_add_delete_permissions.sql
-- 
-- Adds 3 new delete permissions for orders, products, and clients
-- Brings total permissions to 23 (10 page + 13 feature)
-- =============================================================================

-- Insert Delete Feature Permissions (3 permissions)
INSERT INTO permissions (key, name, description, category, is_active) VALUES
('orders.delete', 'Delete Orders', 'Permanently delete orders and all related data', 'Administrative Controls', TRUE),
('products.delete', 'Delete Products', 'Permanently delete products from the system', 'Administrative Controls', TRUE),
('clients.delete', 'Delete Clients', 'Permanently delete client records', 'Administrative Controls', TRUE)
ON CONFLICT (key) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  category = EXCLUDED.category,
  updated_at = NOW();

-- Verify all permissions were inserted
DO $$
DECLARE
    permission_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO permission_count FROM permissions WHERE is_active = TRUE;
    
    IF permission_count < 23 THEN
        RAISE EXCEPTION 'Expected 23 permissions but only found %', permission_count;
    END IF;
    
    RAISE NOTICE 'SUCCESS: % permissions now available (added 3 delete permissions)', permission_count;
END $$;

COMMENT ON TABLE permissions IS 'Registry of all available permissions - 23 total permissions (10 page + 13 feature including delete controls)';