-- ============================================================================
-- PERMISSIONS TABLE BACKUP - Created via MCP
-- Backup Date: 2025-01-05 12:42:44
-- Source: Supabase Project wheufegilqkbcsixkoka (aming-test)  
-- Total Records: 23 permissions
-- ============================================================================

-- Current permissions in mixed format (to be migrated)
INSERT INTO permissions (id, key, name, description, category, is_active, created_at, updated_at) VALUES
(6, 'pages.orders_access', 'Orders Page Access', 'Complete access to orders page and all order management features', 'Page Access', true, '2025-08-08 12:40:12.65348+00', '2025-08-08 13:14:04.581948+00'),
(7, 'pages.products_access', 'Products Page Access', 'Complete access to products page including product management', 'Page Access', true, '2025-08-08 12:40:12.65348+00', '2025-08-08 13:14:04.581948+00'),
(8, 'pages.clients_access', 'Clients Page Access', 'Complete access to clients page including client management', 'Page Access', true, '2025-08-08 12:40:12.65348+00', '2025-08-08 13:14:04.581948+00'),
(9, 'pages.production_cost_access', 'Production Cost System Access', 'Complete access to the entire production cost system', 'Page Access', true, '2025-08-08 12:40:12.65348+00', '2025-08-08 13:14:04.581948+00'),
(10, 'pages.analytics_sales_access', 'Sales Analytics Access', 'Complete access to sales analytics dashboard', 'Analytics Access', true, '2025-08-08 12:40:12.65348+00', '2025-08-08 13:14:04.581948+00'),
(11, 'pages.analytics_production_access', 'Production Analytics Access', 'Complete access to production analytics', 'Analytics Access', true, '2025-08-08 12:40:12.65348+00', '2025-08-08 13:14:04.581948+00'),
(12, 'pages.analytics_general_access', 'General Analytics Access', 'Complete access to general business analytics', 'Analytics Access', true, '2025-08-08 12:40:12.65348+00', '2025-08-08 13:14:04.581948+00'),
(13, 'pages.settings_access', 'Settings Access', 'Access to system settings and configuration', 'Administrative Controls', true, '2025-08-08 12:40:12.65348+00', '2025-08-08 13:14:04.581948+00'),
(14, 'pages.user_management_access', 'User Management Access', 'Access to user and permission management interface', 'Administrative Controls', true, '2025-08-08 12:40:12.65348+00', '2025-08-08 13:14:04.581948+00'),
(15, 'orders.create', 'Create Orders', 'Create new orders', 'Operational Controls', true, '2025-08-08 12:40:12.65348+00', '2025-08-08 13:14:04.581948+00'),
(16, 'orders.general_info_edit', 'Edit Order General Information', 'Modify order basic details (client, dates, status)', 'Order Component Controls', true, '2025-08-08 12:40:12.65348+00', '2025-08-08 13:14:04.581948+00'),
(17, 'orders.items_edit', 'Edit Order Items', 'Add, modify, or remove items from orders', 'Order Component Controls', true, '2025-08-08 12:40:12.65348+00', '2025-08-08 13:14:04.581948+00'),
(18, 'orders.payments_manage', 'Manage Order Payments', 'Handle payment transactions and financial data', 'Financial Controls', true, '2025-08-08 12:40:12.65348+00', '2025-08-08 13:14:04.581948+00'),
(19, 'products.pricing_edit', 'Edit Product Pricing', 'Modify product prices and pricing rules', 'Financial Controls', true, '2025-08-08 12:40:12.65348+00', '2025-08-08 13:14:04.581948+00'),
(20, 'clients.create', 'Create Clients', 'Add new client records', 'Operational Controls', true, '2025-08-08 12:40:12.65348+00', '2025-08-08 13:14:04.581948+00'),
(21, 'analytics.export', 'Export Analytics Data', 'Download and export analytics data', 'Data Security', true, '2025-08-08 12:40:12.65348+00', '2025-08-08 13:14:04.581948+00'),
(22, 'admin.users_create', 'Create Users', 'Add new users to the system', 'Administrative Controls', true, '2025-08-08 12:40:12.65348+00', '2025-08-08 13:14:04.581948+00'),
(23, 'admin.permissions_assign', 'Assign Permissions', 'Modify user permissions and roles', 'Administrative Controls', true, '2025-08-08 12:40:12.65348+00', '2025-08-08 13:14:04.581948+00'),
(24, 'system.full_access', 'Full System Access', 'Complete administrative access to all system functions', 'System Administration', true, '2025-08-08 12:40:12.65348+00', '2025-08-08 13:14:04.581948+00'),
(29, 'pages.analytics_overview_access', 'Analytics Overview Access', 'Complete access to main analytics overview dashboard', 'Analytics Access', true, '2025-08-08 13:14:04.581948+00', '2025-08-08 13:14:04.581948+00'),
(46, 'orders.delete', 'Delete Orders', 'Permanently delete orders and all related data', 'Administrative Controls', true, '2025-08-08 13:31:01.191737+00', '2025-08-08 13:31:01.191737+00'),
(47, 'products.delete', 'Delete Products', 'Permanently delete products from the system', 'Administrative Controls', true, '2025-08-08 13:31:01.191737+00', '2025-08-08 13:31:01.191737+00'),
(48, 'clients.delete', 'Delete Clients', 'Permanently delete client records', 'Administrative Controls', true, '2025-08-08 13:31:01.191737+00', '2025-08-08 13:31:01.191737+00');

-- ============================================================================
-- ANALYSIS OF CURRENT PERMISSIONS (Issues to be fixed by migration)
-- ============================================================================

/*
CRITICAL ISSUES IDENTIFIED:

1. MIXED PERMISSION FORMATS:
   - Page-level: 'pages.orders_access', 'pages.products_access' 
   - Resource-action: 'orders.create', 'orders.delete'
   - Admin-specific: 'admin.users_create', 'system.full_access'

2. INCONSISTENT NAMING:
   - Some use underscores: 'pages.orders_access'
   - Some use dots: 'orders.create'
   - Some are granular: 'orders.general_info_edit' 
   - Some are broad: 'pages.orders_access'

3. CODE EXPECTS DIFFERENT FORMAT:
   - Code expects: 'orders.view', 'products.create', 'clients.edit'
   - Database has: 'pages.orders_access', 'orders.create', 'clients.create'
   
4. OVER-GRANULAR PERMISSIONS:
   - 'orders.general_info_edit' vs 'orders.edit' (simpler)
   - 'orders.items_edit' vs 'orders.edit' (simpler)
   - 'orders.payments_manage' vs 'orders.edit' (simpler)

MIGRATION WILL FIX:
✅ Standardize to resource.action format (orders.view, orders.create, etc.)
✅ Reduce from 23 mixed permissions to 19 standardized permissions  
✅ Align database format with code expectations
✅ Simplify granular permissions to business-aligned actions
*/