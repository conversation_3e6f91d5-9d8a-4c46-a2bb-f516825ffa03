/**
 * Core Permissions Service - Permission checking functionality
 * 
 * Focused service for basic permission checking operations
 * Following CLAUDE.md guidelines: <250 lines, single responsibility
 */

import { supabase } from '../../lib/supabase';
import type { PermissionKey } from '../../types/permissions.types';
import { isValidPermissionKey } from '../../types/permissions.types';
import type { PermissionCheckResult, BulkPermissionCheckResult } from './permissions.types';

// Mutable version for service operations
type MutableBulkPermissionCheckResult = {
  [permissionKey: string]: boolean;
};

// ============================================================================
// CORE PERMISSIONS SERVICE
// ============================================================================

export class PermissionsService {
  /**
   * Check if a specific permission is valid and available
   * Phase 1: Always returns true (open access mode)
   */
  static async checkPermission(permission: PermissionKey): Promise<PermissionCheckResult> {
    try {
      // Validate permission key format first
      if (!isValidPermissionKey(permission)) {
        return {
          hasPermission: false,
          reason: 'Invalid permission key'
        };
      }

      // Phase 1: Open access mode - always allow during development
      if (process.env.NODE_ENV === 'development') {
        return {
          hasPermission: true,
          reason: 'Development mode - open access'
        };
      }

      // Phase 2: Check if permission exists and is active in database
      const { data, error } = await supabase
        .rpc('validate_permission_key', {
          permission_key: permission
        });

      if (error) {
        console.error('Permission check error:', error);
        return {
          hasPermission: false,
          reason: 'System error'
        };
      }

      return {
        hasPermission: data || false,
        reason: data ? undefined : 'Permission not found or inactive'
      };

    } catch (error) {
      console.error('Permission check error:', error);
      return {
        hasPermission: false,
        reason: 'System error'
      };
    }
  }

  /**
   * Check multiple permissions efficiently in a single operation
   */
  static async checkMultiplePermissions(
    permissions: PermissionKey[]
  ): Promise<BulkPermissionCheckResult> {
    try {
      const validPermissions = permissions.filter(isValidPermissionKey);
      const results: MutableBulkPermissionCheckResult = {};

      // Mark invalid permissions as false
      permissions.forEach(perm => {
        if (!isValidPermissionKey(perm)) {
          results[perm] = false;
        }
      });

      if (validPermissions.length === 0) {
        return results;
      }

      // Phase 1: Open access mode - all valid permissions are true
      if (process.env.NODE_ENV === 'development') {
        validPermissions.forEach(perm => {
          results[perm] = true;
        });
        return results;
      }

      // Phase 2: Check permissions against database
      const checkPromises = validPermissions.map(async (permission) => {
        const result = await this.checkPermission(permission);
        return { permission, hasPermission: result.hasPermission };
      });

      const permissionResults = await Promise.all(checkPromises);
      
      permissionResults.forEach(({ permission, hasPermission }) => {
        results[permission] = hasPermission;
      });

      return results;

    } catch (error) {
      console.error('Bulk permission check error:', error);
      
      // Return all permissions as false on error (fail-safe)
      const results: MutableBulkPermissionCheckResult = {};
      permissions.forEach(perm => {
        results[perm] = false;
      });
      
      return results;
    }
  }

  /**
   * Validate that a permission key exists and is active
   */
  static async validatePermissionKey(permission: string): Promise<boolean> {
    try {
      if (!isValidPermissionKey(permission)) {
        return false;
      }

      const { data, error } = await supabase
        .rpc('validate_permission_key', {
          permission_key: permission
        });

      if (error) {
        console.error('Permission validation error:', error);
        return false;
      }

      return data || false;

    } catch (error) {
      console.error('Permission validation error:', error);
      return false;
    }
  }

  /**
   * Check if user has system-level full access
   */
  static async hasFullSystemAccess(): Promise<boolean> {
    try {
      const result = await this.checkPermission('system.full_access');
      return result.hasPermission;
    } catch (error) {
      console.error('Full access check error:', error);
      return false;
    }
  }
}