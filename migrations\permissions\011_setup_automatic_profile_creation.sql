-- Migration: Setup Automatic Profile Creation
-- Date: 2025-01-11
-- Purpose: Create database trigger to automatically create user profiles when auth users are created

-- First, ensure the profiles table exists with proper structure
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    role TEXT DEFAULT 'user',
    department TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS on profiles table
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for profiles table
-- Users can read their own profile
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

-- Users can update their own profile
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

-- Users can insert their own profile (for manual creation if needed)
DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;
CREATE POLICY "Users can insert own profile" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- System can insert profiles (for triggers)
DROP POLICY IF EXISTS "System can insert profiles" ON public.profiles;
CREATE POLICY "System can insert profiles" ON public.profiles
    FOR INSERT WITH CHECK (true);

-- Function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
BEGIN
    -- Extract names from user metadata
    DECLARE
        first_name TEXT := COALESCE(NEW.raw_user_meta_data->>'first_name', '');
        last_name TEXT := COALESCE(NEW.raw_user_meta_data->>'last_name', '');
        full_name_computed TEXT;
    BEGIN
        -- Construct full name
        IF first_name != '' AND last_name != '' THEN
            full_name_computed := first_name || ' ' || last_name;
        ELSIF first_name != '' THEN
            full_name_computed := first_name;
        ELSIF last_name != '' THEN
            full_name_computed := last_name;
        ELSE
            -- Fallback to email username
            full_name_computed := SPLIT_PART(NEW.email, '@', 1);
        END IF;

        -- Insert profile record
        INSERT INTO public.profiles (
            id,
            email,
            full_name,
            avatar_url,
            role,
            department,
            created_at,
            updated_at
        ) VALUES (
            NEW.id,
            NEW.email,
            full_name_computed,
            NEW.raw_user_meta_data->>'avatar_url',
            COALESCE(NEW.raw_user_meta_data->>'role', 'user'),
            NEW.raw_user_meta_data->>'department',
            NOW(),
            NOW()
        );

        RETURN NEW;
    END;
END;
$$;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Create trigger for automatic profile creation
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW 
    EXECUTE FUNCTION public.handle_new_user();

-- Function to handle user updates (sync profile when auth metadata changes)
CREATE OR REPLACE FUNCTION public.handle_user_update()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
BEGIN
    -- Only update if metadata has changed
    IF OLD.raw_user_meta_data IS DISTINCT FROM NEW.raw_user_meta_data THEN
        DECLARE
            first_name TEXT := COALESCE(NEW.raw_user_meta_data->>'first_name', '');
            last_name TEXT := COALESCE(NEW.raw_user_meta_data->>'last_name', '');
            full_name_computed TEXT;
        BEGIN
            -- Construct full name
            IF first_name != '' AND last_name != '' THEN
                full_name_computed := first_name || ' ' || last_name;
            ELSIF first_name != '' THEN
                full_name_computed := first_name;
            ELSIF last_name != '' THEN
                full_name_computed := last_name;
            ELSE
                -- Keep existing full_name if no names in metadata
                full_name_computed := (SELECT full_name FROM public.profiles WHERE id = NEW.id);
            END IF;

            -- Update profile record
            UPDATE public.profiles SET
                email = NEW.email,
                full_name = full_name_computed,
                avatar_url = NEW.raw_user_meta_data->>'avatar_url',
                role = COALESCE(NEW.raw_user_meta_data->>'role', role),
                department = COALESCE(NEW.raw_user_meta_data->>'department', department),
                updated_at = NOW()
            WHERE id = NEW.id;
        END;
    END IF;

    RETURN NEW;
END;
$$;

-- Drop existing update trigger if it exists
DROP TRIGGER IF EXISTS on_auth_user_updated ON auth.users;

-- Create trigger for profile sync on user update
CREATE TRIGGER on_auth_user_updated
    AFTER UPDATE ON auth.users
    FOR EACH ROW 
    EXECUTE FUNCTION public.handle_user_update();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS profiles_email_idx ON public.profiles(email);
CREATE INDEX IF NOT EXISTS profiles_role_idx ON public.profiles(role);
CREATE INDEX IF NOT EXISTS profiles_department_idx ON public.profiles(department);

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated, anon;
GRANT SELECT, INSERT, UPDATE ON public.profiles TO authenticated;

-- Comment the table and functions
COMMENT ON TABLE public.profiles IS 'User profiles automatically synced with auth.users';
COMMENT ON FUNCTION public.handle_new_user() IS 'Automatically creates user profile when auth user is created';
COMMENT ON FUNCTION public.handle_user_update() IS 'Automatically syncs user profile when auth user metadata is updated';