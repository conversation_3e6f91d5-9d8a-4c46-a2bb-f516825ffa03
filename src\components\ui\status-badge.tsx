import React from 'react'
import { cn } from '../../lib/utils'
import { 
  Shield, 
  Crown, 
  Users, 
  Edit3, 
  Eye, 
  User,
  CheckCircle,
  XCircle
} from 'lucide-react'

interface StatusBadgeProps {
  status: 'active' | 'inactive' | 'admin' | 'administrator' | 'supervisor' | 'manager' | 'viewer' | 'editor' | 'moderator' | 'user' | 'default'
  children: React.ReactNode
  className?: string
  withIcon?: boolean
  size?: 'sm' | 'md' | 'lg'
}

export function StatusBadge({ status, children, className, withIcon = false, size = 'md' }: StatusBadgeProps) {
  const getStatusClass = () => {
    const statusLower = (status || '').toLowerCase()
    
    // Role-based styling (matching nav-user.tsx exactly)
    switch (statusLower) {
      case 'admin':
      case 'administrator':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      case 'manager':
      case 'supervisor':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      case 'editor':
      case 'moderator':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
      case 'viewer':
      case 'user':
      case 'default':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      // Status-based styling
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'inactive':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
    }
  }

  const getIcon = () => {
    const statusLower = (status || '').toLowerCase()
    
    switch (statusLower) {
      case 'admin':
      case 'administrator':
        return Crown
      case 'manager':
        return Shield
      case 'supervisor':
        return Users
      case 'editor':
      case 'moderator':
        return Edit3
      case 'viewer':
        return Eye
      case 'user':
      case 'default':
        return User
      case 'active':
        return CheckCircle
      case 'inactive':
        return XCircle
      default:
        return User
    }
  }

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'text-xs px-2 py-0.5'
      case 'lg':
        return 'text-sm px-3 py-1.5'
      case 'md':
      default:
        return 'text-xs px-2.5 py-1'
    }
  }

  const getIconSize = () => {
    switch (size) {
      case 'sm':
        return 12
      case 'lg':
        return 16
      case 'md':
      default:
        return 14
    }
  }

  const IconComponent = withIcon ? getIcon() : null

  return (
    <span 
      className={cn(
        'inline-flex items-center rounded-full font-medium',
        getSizeClasses(),
        getStatusClass(),
        className
      )}
    >
      {IconComponent && (
        <IconComponent className="mr-1.5" size={getIconSize()} />
      )}
      {children}
    </span>
  )
}