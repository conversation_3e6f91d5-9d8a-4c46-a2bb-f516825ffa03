import { ALL_PERMISSIONS } from '../../../../../types/permissions.types'

export interface PermissionCategory {
  key: string
  title: string
  description: string
  permissions: string[]
  priority: number
  isActive: boolean
}

export const getPermissionsByCategory = (): PermissionCategory[] => {
  const categories: Record<string, string[]> = {}
  
  // ALL_PERMISSIONS is an object where keys are permission names and values are permission strings
  // We need to group them by their category (prefix before the dot)
  Object.values(ALL_PERMISSIONS).forEach((permission) => {
    const [category] = permission.split('.')
    if (!categories[category]) {
      categories[category] = []
    }
    categories[category].push(permission)
  })
  
  // Define category metadata with priorities for ordering
  const categoryMetadata: Record<string, { title: string; description: string; priority: number }> = {
    system: {
      title: 'Full System Access',
      description: 'Complete administrative control and system management',
      priority: 1
    },
    pages: {
      title: 'Page Access',
      description: 'Access to different sections and pages of the application',
      priority: 2
    },
    orders: {
      title: 'Order Management',
      description: 'Create, edit, and manage customer orders',
      priority: 3
    },
    products: {
      title: 'Product Management',
      description: 'Manage products, pricing, and inventory',
      priority: 4
    },
    clients: {
      title: 'Client Management',
      description: 'Manage customer information and relationships',
      priority: 5
    },
    analytics: {
      title: 'Analytics & Reporting',
      description: 'View reports, analytics, and export data',
      priority: 6
    },
    production_cost: {
      title: 'Production Costs',
      description: 'Manage production costs and pricing calculations',
      priority: 7
    }
  }
  
  // Convert to array and sort by priority
  // Only system and pages categories are active for now
  const activeCategories = ['system', 'pages']
  
  return Object.entries(categories)
    .map(([key, permissions]) => ({
      key,
      title: categoryMetadata[key]?.title || key.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
      description: categoryMetadata[key]?.description || `Permissions related to ${key}`,
      permissions: permissions.sort(),
      priority: categoryMetadata[key]?.priority || 999,
      isActive: activeCategories.includes(key)
    }))
    .sort((a, b) => a.priority - b.priority)
}