-- Migration: Update item notes validation for simplified note types
-- Date: 2025-12-06
-- Description: Updates the item notes validation to accept only the simplified note types (design, link, general)

-- Update the validation function to accept the new simplified note types
CREATE OR REPLACE FUNCTION validate_item_note(note_data jsonb)
RETURNS boolean AS $$
BEGIN
  -- Check if note_data is an object
  IF jsonb_typeof(note_data) != 'object' THEN
    RETURN false;
  END IF;
  
  -- Check required fields
  IF NOT (
    note_data ? 'content' AND
    note_data ? 'type' AND
    note_data ? 'category' AND
    note_data ? 'created_at'
  ) THEN
    RETURN false;
  END IF;
  
  -- Validate note type (updated for simplified types)
  IF NOT (note_data->>'type' = ANY(ARRAY['design', 'link', 'general'])) THEN
    RETURN false;
  END IF;
  
  -- Validate priority (if present)
  IF note_data ? 'priority' AND NOT (note_data->>'priority' = ANY(ARRAY['low', 'medium', 'high', 'urgent'])) THEN
    RETURN false;
  END IF;
  
  R<PERSON>UR<PERSON> true;
END;
$$ LANGUAGE plpgsql;

-- Update the add_item_note function to handle the simplified note types
CREATE OR REPLACE FUNCTION add_item_note(
  p_item_id uuid,
  p_content text,
  p_note_type text DEFAULT 'general',
  p_category text DEFAULT 'general',
  p_priority text DEFAULT 'medium',
  p_created_by text DEFAULT NULL
)
RETURNS jsonb AS $$
DECLARE
  v_new_note jsonb;
  v_current_notes jsonb;
  v_updated_notes jsonb;
BEGIN
  -- Validate note type
  IF NOT (p_note_type = ANY(ARRAY['design', 'link', 'general'])) THEN
    RAISE EXCEPTION 'Invalid note type. Must be one of: design, link, general';
  END IF;
  
  -- Validate priority
  IF NOT (p_priority = ANY(ARRAY['low', 'medium', 'high', 'urgent'])) THEN
    RAISE EXCEPTION 'Invalid priority. Must be one of: low, medium, high, urgent';
  END IF;
  
  -- Create the new note
  v_new_note := jsonb_build_object(
    'id', gen_random_uuid()::text,
    'content', p_content,
    'type', p_note_type,
    'category', p_category,
    'priority', p_priority,
    'created_at', NOW()::text,
    'created_by', p_created_by,
    'updated_at', NOW()::text
  );
  
  -- Get current notes
  SELECT COALESCE(item_notes, '[]'::jsonb)
  INTO v_current_notes
  FROM order_items
  WHERE item_id = p_item_id;
  
  -- Add the new note to the array
  v_updated_notes := v_current_notes || jsonb_build_array(v_new_note);
  
  -- Update the order item
  UPDATE order_items
  SET item_notes = v_updated_notes,
      updated_at = NOW()
  WHERE item_id = p_item_id;
  
  -- Return the new note
  RETURN v_new_note;
END;
$$ LANGUAGE plpgsql;

-- Add a comment to track the migration
COMMENT ON FUNCTION validate_item_note(jsonb) IS 'Validates item note structure for simplified note types (design, link, general) - Updated 2025-12-06';
COMMENT ON FUNCTION add_item_note(uuid, text, text, text, text, text) IS 'Adds a new note to an order item with simplified note types - Updated 2025-12-06';