-- Add Dual-ID System to authorized_users Table
-- 
-- Problem: authorized_users.id (admin-created UUID) doesn't match auth.users.id (signup UUID)
-- Solution: Add auth_user_id field to store the actual auth.users.id for RLS queries
--
-- This maintains backward compatibility while fixing RLS policy issues.

-- ============================================================================
-- STEP 1: ADD THE NEW auth_user_id COLUMN
-- ============================================================================

-- Add the new column to store auth.users.id
ALTER TABLE authorized_users 
ADD COLUMN IF NOT EXISTS auth_user_id UUID;

-- Create index for performance (this will be used heavily in RLS)
CREATE INDEX IF NOT EXISTS idx_authorized_users_auth_user_id 
ON authorized_users(auth_user_id);

-- Create compound index for common queries
CREATE INDEX IF NOT EXISTS idx_authorized_users_auth_user_id_active 
ON authorized_users(auth_user_id, is_active);

-- ============================================================================
-- STEP 2: POPULATE auth_user_id FOR EXISTING RECORDS
-- ============================================================================

-- Update existing records by matching email addresses
UPDATE authorized_users 
SET auth_user_id = auth_users.id,
    updated_at = NOW()
FROM auth.users AS auth_users
WHERE authorized_users.email = auth_users.email 
AND authorized_users.auth_user_id IS NULL;

-- Report how many records were updated
DO $$
DECLARE
  updated_count INTEGER;
BEGIN
  GET DIAGNOSTICS updated_count = ROW_COUNT;
  RAISE NOTICE 'Updated % existing authorized_users records with auth_user_id', updated_count;
END;
$$;

-- ============================================================================
-- STEP 3: UPDATE RLS FUNCTIONS TO USE auth_user_id
-- ============================================================================

-- Update user_has_permission function to use auth_user_id instead of id
CREATE OR REPLACE FUNCTION user_has_permission(required_permission text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
STABLE
AS $$
BEGIN
  -- Only authenticated users can have permissions
  IF NOT is_authenticated() THEN
    RETURN false;
  END IF;
  
  -- Query authorized_users using auth_user_id (matches auth.uid())
  -- This eliminates the circular dependency completely
  RETURN EXISTS (
    SELECT 1 
    FROM authorized_users au
    WHERE au.auth_user_id = auth.uid()
    AND au.is_active = true
    AND (au.permissions ? required_permission OR au.permissions ? 'system.full_access')
  );
END;
$$;

-- Update is_authorized_user function to use auth_user_id
CREATE OR REPLACE FUNCTION is_authorized_user()
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
STABLE
AS $$
BEGIN
  -- Check if user is authenticated and exists in authorized_users
  IF NOT is_authenticated() THEN
    RETURN false;
  END IF;
  
  -- Direct lookup by auth_user_id - no circular dependency
  RETURN EXISTS (
    SELECT 1 
    FROM authorized_users au
    WHERE au.auth_user_id = auth.uid()
    AND au.is_active = true
  );
END;
$$;

-- ============================================================================
-- STEP 4: UPDATE RLS POLICIES TO USE auth_user_id
-- ============================================================================

-- Drop existing policies
DROP POLICY IF EXISTS "authorized_users_self_read" ON authorized_users;

-- Create new policy using auth_user_id for self-access
CREATE POLICY "authorized_users_self_read" ON authorized_users
  FOR SELECT TO authenticated
  USING (auth_user_id = auth.uid());

-- Admin policies remain the same since they use is_admin() function
-- which now internally uses auth_user_id

-- ============================================================================
-- STEP 5: CREATE TRIGGER FOR AUTOMATIC auth_user_id ASSIGNMENT
-- ============================================================================

-- Function to automatically set auth_user_id when user signs up
CREATE OR REPLACE FUNCTION sync_auth_user_id_on_signup()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- When a new user signs up in auth.users, find their authorized_users record by email
  -- and update it with their actual auth.uid()
  UPDATE authorized_users 
  SET auth_user_id = NEW.id,
      first_login_at = COALESCE(first_login_at, NOW()),
      updated_at = NOW()
  WHERE email = NEW.email 
  AND auth_user_id IS NULL; -- Only update if not already set
  
  -- Log the sync if it happened
  IF FOUND THEN
    RAISE NOTICE 'Auto-synced auth_user_id for user: % (Auth ID: %)', NEW.email, NEW.id;
  END IF;
  
  RETURN NEW;
END;
$$;

-- Create the trigger
DROP TRIGGER IF EXISTS sync_auth_user_id_trigger ON auth.users;
CREATE TRIGGER sync_auth_user_id_trigger
  AFTER INSERT ON auth.users
  FOR EACH ROW 
  EXECUTE FUNCTION sync_auth_user_id_on_signup();

-- ============================================================================
-- STEP 6: CREATE HELPER FUNCTION FOR ADMIN USER CREATION
-- ============================================================================

-- Function to create authorized users properly (for admin panel)
CREATE OR REPLACE FUNCTION create_authorized_user(
  user_email TEXT,
  user_first_name TEXT,
  user_last_name TEXT,
  user_department TEXT DEFAULT NULL,
  user_permissions JSONB DEFAULT '[]'::JSONB,
  user_role_template TEXT DEFAULT NULL,
  user_notes TEXT DEFAULT NULL,
  invited_by_id UUID DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_record_id UUID;
  existing_auth_id UUID;
BEGIN
  -- Generate internal ID for authorized_users record
  new_record_id := gen_random_uuid();
  
  -- Check if user already exists in auth.users
  SELECT id INTO existing_auth_id
  FROM auth.users 
  WHERE email = user_email;
  
  -- Insert the authorized_users record
  INSERT INTO authorized_users (
    id,                    -- Internal reference ID
    auth_user_id,          -- Will be NULL until user signs up (unless they already have)
    email,
    first_name,
    last_name,
    department,
    permissions,
    role_template,
    is_active,
    notes,
    invited_by,
    invited_at
  ) VALUES (
    new_record_id,
    existing_auth_id,      -- Will be NULL if user hasn't signed up yet
    user_email,
    user_first_name,
    user_last_name,
    user_department,
    user_permissions,
    user_role_template,
    true,
    user_notes,
    invited_by_id,
    NOW()
  );
  
  -- Return the internal ID for admin panel references
  RETURN new_record_id;
END;
$$;

-- ============================================================================
-- STEP 7: ADD HELPFUL CONSTRAINTS AND COMMENTS
-- ============================================================================

-- Add foreign key constraint (optional, but good practice)
-- Note: This might fail if there are orphaned records, so we'll make it optional
DO $$
BEGIN
  -- Try to add the foreign key constraint
  ALTER TABLE authorized_users 
  ADD CONSTRAINT fk_authorized_users_auth_user_id 
  FOREIGN KEY (auth_user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
  
  RAISE NOTICE 'Added foreign key constraint for auth_user_id';
EXCEPTION WHEN others THEN
  RAISE NOTICE 'Could not add foreign key constraint (possibly due to orphaned records): %', SQLERRM;
END;
$$;

-- Add comments for documentation
COMMENT ON COLUMN authorized_users.id IS 
'Internal reference ID for admin operations and backward compatibility';

COMMENT ON COLUMN authorized_users.auth_user_id IS 
'References auth.users.id - used for RLS policies and permission checks. NULL until user signs up.';

COMMENT ON FUNCTION sync_auth_user_id_on_signup() IS 
'Trigger function that automatically populates auth_user_id when a user signs up';

COMMENT ON FUNCTION create_authorized_user(TEXT, TEXT, TEXT, TEXT, JSONB, TEXT, TEXT, UUID) IS 
'Proper function for admin to create authorized users with dual-ID system';

-- ============================================================================
-- STEP 8: VERIFICATION
-- ============================================================================

-- Check the current state
DO $$
DECLARE
  total_users INTEGER;
  synced_users INTEGER;
  pending_users INTEGER;
BEGIN
  SELECT COUNT(*) INTO total_users FROM authorized_users;
  SELECT COUNT(*) INTO synced_users FROM authorized_users WHERE auth_user_id IS NOT NULL;
  SELECT COUNT(*) INTO pending_users FROM authorized_users WHERE auth_user_id IS NULL;
  
  RAISE NOTICE '================================================================================';
  RAISE NOTICE 'DUAL-ID SYSTEM IMPLEMENTATION COMPLETED';
  RAISE NOTICE '================================================================================';
  RAISE NOTICE '';
  RAISE NOTICE '📊 Database Status:';
  RAISE NOTICE '   Total authorized users: %', total_users;
  RAISE NOTICE '   Synced with auth.users: %', synced_users;  
  RAISE NOTICE '   Pending signup: %', pending_users;
  RAISE NOTICE '';
  RAISE NOTICE '✅ Added auth_user_id column';
  RAISE NOTICE '✅ Updated RLS functions to use auth_user_id';
  RAISE NOTICE '✅ Updated RLS policies for proper access';
  RAISE NOTICE '✅ Created auto-sync trigger for new signups';
  RAISE NOTICE '✅ Created helper function for admin user creation';
  RAISE NOTICE '';
  RAISE NOTICE '🔧 Next Steps:';
  RAISE NOTICE '1. Update DirectAuthService to use auth_user_id';
  RAISE NOTICE '2. Update AdminService to use create_authorized_user()';
  RAISE NOTICE '3. Test user creation and authentication flows';
  RAISE NOTICE '';
  RAISE NOTICE '================================================================================';
END;
$$;