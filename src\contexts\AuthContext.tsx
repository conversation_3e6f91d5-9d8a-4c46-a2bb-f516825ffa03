/**
 * Auth Context - Unified Auth & Permissions
 * 
 * Solves re-render issues by consolidating auth and permissions into single state
 * Following CLAUDE.md guidelines: <250 lines, proper memoization
 */

import React, { createContext, useContext, useState, useEffect, useCallback, useMemo } from 'react';
import type { Session, User } from '@supabase/supabase-js';
import { AuthService } from '../services/auth/authService';
import { AuthorizationService } from '../services/auth/authorizationService';
import { getProfile, ProfileModel } from '../services/profileService';
import { PermissionService } from '../services/permissions/PermissionService';
import type { AuthorizedUser } from '../services/preAuthService';
import type { PermissionKey, RoleId } from '../types/permissions.types';
import { logger } from '../utils/logger';
import authReadyState from '../utils/authReadyState';

const authLogger = logger.withPrefix('AuthContext');

// ============================================================================
// TYPES
// ============================================================================

interface AuthState {
  // Authentication
  readonly session: Session | null;
  readonly user: User | null;
  readonly profile: ProfileModel | null;
  
  // Authorization  
  readonly authorized: boolean;
  readonly permissions: readonly PermissionKey[];
  readonly role: RoleId | null;
  readonly authorizedUser: AuthorizedUser | null;
  
  // Loading states
  readonly loading: boolean;
  readonly profileLoading: boolean;
  readonly error: string | null;
}

interface AuthContextType extends AuthState {
  // Authentication methods
  signInWithEmail: (email: string) => Promise<void>;
  verifyEmailOTP: (email: string, token: string) => Promise<void>;
  signOut: () => Promise<void>;
  
  // Utility methods
  refreshSession: () => Promise<void>;
  refreshProfile: () => Promise<void>;
  refreshPermissions: () => Promise<void>;
  
  // Permission methods (unified)
  hasPermission: (permission: PermissionKey | readonly PermissionKey[]) => boolean;
  hasAllPermissions: (permissions: readonly PermissionKey[]) => boolean;
  isAdmin: boolean;
}

// ============================================================================
// CONTEXT CREATION
// ============================================================================

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// ============================================================================
// AUTH PROVIDER 
// ============================================================================

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // ========================================================================
  // STATE MANAGEMENT
  // ========================================================================
  
  const [authState, setAuthState] = useState<AuthState>({
    session: null,
    user: null,
    profile: null,
    authorized: false,
    permissions: [],
    role: null,
    authorizedUser: null,
    loading: true,
    profileLoading: false,
    error: null
  });
  
  // ========================================================================
  // MEMOIZED FUNCTIONS (Prevent re-renders)
  // ========================================================================
  
  const updateAuthState = useCallback((updates: Partial<AuthState>) => {
    setAuthState(current => ({ ...current, ...updates }));
  }, []);
  
  const loadUserPermissions = useCallback(async (email: string): Promise<void> => {
    try {
      authLogger.debug('Loading permissions for:', { email });
      
      const authUser = await AuthorizationService.checkUserAuthorization(email);
      
      if (authUser) {
        // Get detailed permissions from service
        const { role, permissions } = await PermissionService.getUserRole(authUser.id);
        
        updateAuthState({
          authorized: true,
          permissions,
          role,
          authorizedUser: authUser,
          error: null
        });
        
        // Update last login
        await AuthorizationService.updateLastLogin(email);
        
        authLogger.info('User authorized with permissions:', { email, role, permissionCount: permissions.length });
        
      } else {
        updateAuthState({
          authorized: false,
          permissions: [],
          role: null,
          authorizedUser: null,
          error: 'User not authorized'
        });
        
        authLogger.warn('User not found in authorized_users:', { email });
      }
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Authorization failed';
      
      authLogger.error('Error loading permissions:', { error, email });
      
      updateAuthState({
        authorized: false,
        permissions: [],
        role: null,
        authorizedUser: null,
        error: errorMessage
      });
    }
  }, [updateAuthState]);
  
  const loadProfile = useCallback(async (userId: string): Promise<void> => {
    updateAuthState({ profileLoading: true });
    
    try {
      let userProfile = await getProfile(userId);
      
      // Create profile if it doesn't exist
      if (!userProfile) {
        authLogger.debug('Profile not found, creating for user:', userId);
        const { ensureProfileExists } = await import('../services/profileService');
        userProfile = await ensureProfileExists(userId);
      }
      
      updateAuthState({ 
        profile: userProfile,
        profileLoading: false,
        error: null
      });
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load profile';
      
      authLogger.error('Error loading profile:', error);
      updateAuthState({
        profile: null,
        profileLoading: false,
        error: errorMessage
      });
    }
  }, [updateAuthState]);
  
  // ========================================================================
  // AUTH INITIALIZATION
  // ========================================================================
  
  useEffect(() => {
    let mounted = true;
    let initTimeout: NodeJS.Timeout;
    
    const initializeAuth = async () => {
      try {
        authLogger.debug('Initializing auth...');
        
        // Failsafe timeout
        initTimeout = setTimeout(() => {
          if (mounted) {
            authLogger.warn('Auth initialization timeout');
            updateAuthState({ loading: false, error: 'Initialization timeout' });
            authReadyState.setReady(true, 'timeout-fallback');
          }
        }, 15000);
        
        // Get current session
        const currentSession = await AuthService.getCurrentSession();
        
        if (!mounted) return;
        
        updateAuthState({
          session: currentSession,
          user: currentSession?.user || null
        });
        
        // Load permissions and profile if user exists
        if (currentSession?.user?.email) {
          const userId = currentSession.user.id;
          const userEmail = currentSession.user.email;
          
          // Load in parallel with error handling
          const [permissionsResult, profileResult] = await Promise.allSettled([
            loadUserPermissions(userEmail),
            loadProfile(userId)
          ]);
          
          // Log any errors but don't fail the initialization
          if (permissionsResult.status === 'rejected') {
            authLogger.error('Permissions loading failed:', permissionsResult.reason);
          }
          
          if (profileResult.status === 'rejected') {
            authLogger.error('Profile loading failed:', profileResult.reason);
          }
        }
        
        if (mounted) {
          clearTimeout(initTimeout);
          updateAuthState({ loading: false, error: null });
          authReadyState.setReady(true, 'auth-v2-ready');
          authLogger.debug('Auth initialization completed');
        }
        
      } catch (error) {
        authLogger.error('Critical auth initialization error:', error);
        
        if (mounted) {
          clearTimeout(initTimeout);
          updateAuthState({ 
            loading: false,
            error: error instanceof Error ? error.message : 'Auth initialization failed'
          });
          authReadyState.setReady(true, 'auth-v2-error');
        }
      }
    };
    
    initializeAuth();
    
    // Listen for auth changes
    const { data: { subscription } } = AuthService.onAuthStateChange(async (event, session) => {
      if (!mounted) return;
      
      authLogger.debug('Auth state changed:', { event });
      
      updateAuthState({
        session,
        user: session?.user || null
      });
      
      if (session?.user?.email) {
        // Load permissions and profile for new session
        await Promise.allSettled([
          loadUserPermissions(session.user.email),
          loadProfile(session.user.id)
        ]);
      } else {
        // Clear state on sign out
        updateAuthState({
          authorized: false,
          permissions: [],
          role: null,
          authorizedUser: null,
          profile: null,
          error: null
        });
      }
    });
    
    return () => {
      mounted = false;
      clearTimeout(initTimeout);
      subscription.unsubscribe();
    };
  }, [loadUserPermissions, loadProfile, updateAuthState]);
  
  // ========================================================================
  // AUTH METHODS
  // ========================================================================
  
  const signInWithEmail = useCallback(async (email: string): Promise<void> => {
    await AuthService.signInWithEmail(email);
  }, []);
  
  const verifyEmailOTP = useCallback(async (email: string, token: string): Promise<void> => {
    const result = await AuthService.verifyEmailOTP(email, token);
    
    if (result.user?.email) {
      // Load permissions and profile after successful verification
      await Promise.allSettled([
        loadUserPermissions(result.user.email),
        result.user.id ? loadProfile(result.user.id) : Promise.resolve()
      ]);
    }
  }, [loadUserPermissions, loadProfile]);
  
  const signOut = useCallback(async (): Promise<void> => {
    try {
      authLogger.info('Signing out user');
      
      // Clear state immediately for better UX
      updateAuthState({
        session: null,
        user: null,
        authorized: false,
        permissions: [],
        role: null,
        authorizedUser: null,
        profile: null,
        loading: false,
        profileLoading: false,
        error: null
      });
      
      // Clear permission cache
      PermissionService.clearCache();
      
      await AuthService.signOut();
      authReadyState.reset('sign-out');
      
      authLogger.info('Sign out completed');
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Sign out failed';
      authLogger.error('Sign out error:', error);
      updateAuthState({ error: errorMessage });
      throw error;
    }
  }, [updateAuthState]);
  
  const refreshSession = useCallback(async (): Promise<void> => {
    const newSession = await AuthService.refreshSession();
    updateAuthState({
      session: newSession,
      user: newSession?.user || null
    });
  }, [updateAuthState]);
  
  const refreshProfile = useCallback(async (): Promise<void> => {
    if (authState.user?.id) {
      await loadProfile(authState.user.id);
    }
  }, [authState.user?.id, loadProfile]);
  
  const refreshPermissions = useCallback(async (): Promise<void> => {
    if (authState.user?.email) {
      // Clear cache and reload
      PermissionService.clearCache(authState.user.id);
      await loadUserPermissions(authState.user.email);
    }
  }, [authState.user?.email, authState.user?.id, loadUserPermissions]);
  
  // ========================================================================
  // MEMOIZED PERMISSION METHODS (Prevent re-renders)
  // ========================================================================
  
  const hasPermission = useCallback((
    permission: PermissionKey | readonly PermissionKey[]
  ): boolean => {
    if (!authState.authorized || !authState.permissions.length) {
      return false;
    }
    
    return PermissionService.hasPermission(authState.permissions, permission);
  }, [authState.authorized, authState.permissions]);
  
  const hasAllPermissions = useCallback((
    permissions: readonly PermissionKey[]
  ): boolean => {
    if (!authState.authorized || !authState.permissions.length) {
      return false;
    }
    
    return PermissionService.hasAllPermissions(authState.permissions, permissions);
  }, [authState.authorized, authState.permissions]);
  
  const isAdmin = useMemo((): boolean => {
    return PermissionService.isSystemAdmin(authState.permissions);
  }, [authState.permissions]);
  
  // ========================================================================
  // MEMOIZED CONTEXT VALUE (Critical for preventing re-renders)
  // ========================================================================
  
  const contextValue = useMemo((): AuthContextType => ({
    ...authState,
    signInWithEmail,
    verifyEmailOTP,
    signOut,
    refreshSession,
    refreshProfile,
    refreshPermissions,
    hasPermission,
    hasAllPermissions,
    isAdmin
  }), [
    authState,
    signInWithEmail,
    verifyEmailOTP,
    signOut,
    refreshSession,
    refreshProfile,
    refreshPermissions,
    hasPermission,
    hasAllPermissions,
    isAdmin
  ]);
  
  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// ============================================================================
// HOOK
// ============================================================================

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
};