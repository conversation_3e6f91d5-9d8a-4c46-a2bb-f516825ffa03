import type { EditUserFormData } from '../types'

export function useEditUserFormValidation() {
  const validateForm = (formData: EditUserFormData): string | null => {
    // Required field validation (NOT NULL constraints)
    if (!formData.first_name.trim()) return 'First name is required'
    if (!formData.last_name.trim()) return 'Last name is required'
    
    // Name length validation (matches database valid_names constraint)
    if (formData.first_name.trim().length === 0) {
      return 'First name cannot be empty or contain only spaces'
    }
    if (formData.last_name.trim().length === 0) {
      return 'Last name cannot be empty or contain only spaces'
    }
    
    // Name length practical limits
    if (formData.first_name.trim().length > 100) {
      return 'First name is too long (maximum 100 characters)'
    }
    if (formData.last_name.trim().length > 100) {
      return 'Last name is too long (maximum 100 characters)'
    }
    
    // Department length validation if provided
    if (formData.department && formData.department.trim().length > 100) {
      return 'Department name is too long (maximum 100 characters)'
    }
    
    // Notes length validation if provided
    if (formData.notes && formData.notes.trim().length > 1000) {
      return 'Notes are too long (maximum 1000 characters)'
    }
    
    // Permissions array validation (matches database valid_permissions constraint)
    if (formData.permissions && !Array.isArray(formData.permissions)) {
      return 'Permissions must be provided as a valid array'
    }
    
    // Require at least 1 permission per account (business rule)
    if (!formData.permissions || formData.permissions.length === 0) {
      return 'At least one permission must be assigned to each user account'
    }
    
    return null
  }

  return { validateForm }
}