-- ============================================================================
-- ROLES TABLE BACKUP - Created via MCP  
-- Backup Date: 2025-01-05 12:42:44
-- Source: Supabase Project wheufegilqkbcsixkoka (aming-test)
-- Total Records: 5 roles
-- ============================================================================

-- Current role structure (to be simplified in migration)
INSERT INTO roles (id, name, display_name, description, permissions, is_active, is_system_role, created_at, updated_at) VALUES
(1, 'viewer', 'Viewer', 'Read-only access to basic business pages', 
 '["pages.orders_access","pages.products_access","pages.clients_access"]', 
 true, false, '2025-08-08 12:40:12.65348+00', '2025-08-08 12:40:12.65348+00'),

(2, 'order_manager', 'Order Manager', 'Complete order management with component-level control', 
 '["pages.orders_access","pages.clients_access","pages.analytics_sales_access","orders.create","orders.general_info_edit","orders.items_edit","orders.payments_manage"]', 
 true, false, '2025-08-08 12:40:12.65348+00', '2025-08-08 12:40:12.65348+00'),

(3, 'product_manager', 'Product Manager', 'Complete product and production cost management', 
 '["pages.products_access","pages.production_cost_access","pages.analytics_production_access","products.pricing_edit"]', 
 true, false, '2025-08-08 12:40:12.65348+00', '2025-08-08 12:40:12.65348+00'),

(4, 'supervisor', 'Operations Supervisor', 'Advanced operational control including financial management', 
 '["pages.orders_access","pages.products_access","pages.clients_access","pages.analytics_sales_access","pages.analytics_general_access","orders.create","orders.general_info_edit","orders.items_edit","orders.payments_manage","products.pricing_edit","clients.create","analytics.export"]', 
 true, false, '2025-08-08 12:40:12.65348+00', '2025-08-08 12:40:12.65348+00'),

(5, 'admin', 'System Administrator', 'Full system administration and management', 
 '["system.full_access"]', 
 true, true, '2025-08-08 12:40:12.65348+00', '2025-08-08 12:40:12.65348+00');

-- ============================================================================ 
-- ANALYSIS OF CURRENT ROLES (Issues to be fixed by migration)
-- ============================================================================

/*
CURRENT ROLE ISSUES:

1. FRAGMENTED ROLES:
   - 'order_manager' - only orders
   - 'product_manager' - only products  
   - 'supervisor' - mixed permissions
   Result: Users need multiple roles for simple job functions

2. COMPLEX PERMISSIONS ARRAYS:
   - 'supervisor' has 12 permissions
   - Mix of page-level and granular permissions
   - Hard to understand what each role actually allows

3. INCONSISTENT PERMISSION REFERENCES:
   - Uses old format: 'pages.orders_access'
   - Code expects: 'orders.view'
   - Result: Role-based permission checks fail

4. NOT BUSINESS-ALIGNED:
   - Real users: viewer, operator, manager, admin
   - Current roles: order_manager, product_manager, supervisor
   - Mismatch with actual job functions

MIGRATION WILL CREATE:
✅ 4 Business-Aligned Roles:
   - viewer: Read-only access (3 permissions)
   - operator: Daily operations (8 permissions) 
   - manager: Full business operations (13 permissions)
   - admin: System administration (1 super permission)

✅ Simplified Permission Arrays:
   - Clear, standardized permission names
   - Logical groupings by business function
   - Easy to understand and audit

✅ Proper Format Alignment:
   - All permissions in resource.action format
   - Matches code expectations
   - Consistent across all roles
*/