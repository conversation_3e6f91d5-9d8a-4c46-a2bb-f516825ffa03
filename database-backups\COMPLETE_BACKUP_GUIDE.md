# Complete Database Backup Guide

## 🎯 Quick Start

**To create a complete backup of your entire database:**

```powershell
# Run this command in your project root
.\scripts\full-database-backup.ps1
```

**This will backup:**
- ✅ ALL 1,360 orders + order items + payments
- ✅ ALL 635 clients  
- ✅ ALL 100 products + product lines
- ✅ ALL 55+ tables including production costs, analytics, auth
- ✅ Complete database schema (functions, triggers, indexes)
- ✅ User permissions and roles
- ✅ Supabase auth system data

## 📋 Prerequisites

1. **PostgreSQL Client Tools** - Install from https://www.postgresql.org/download/windows/
2. **DATABASE_URL in .env** - Your Supabase connection string
3. **Disk Space** - Estimated backup size: 50-100 MB

## 🗂️ What Gets Created

The script creates a timestamped directory with multiple backup files:

```
database-backups/COMPLETE_BACKUP_20250105_124244/
├── COMPLETE_DATABASE.dump          ← PRIMARY BACKUP (everything)
├── PUBLIC_SCHEMA.dump              ← Your app data only
├── AUTH_SCHEMA.dump               ← Supabase auth data  
├── BUSINESS_DATA.sql              ← Orders, clients, products
├── PERMISSIONS_SYSTEM.sql         ← Users, roles, permissions
├── PRODUCTION_SYSTEM.sql          ← Production cost system
├── ANALYTICS_SYSTEM.sql           ← Analytics data
├── SCHEMA_ONLY.sql               ← Database structure
├── DATA_ONLY.sql                 ← All data (no structure)
├── RESTORE_COMPLETE_DATABASE.ps1  ← Full restore script
├── RESTORE_SELECTIVE.ps1         ← Selective restore script
└── BACKUP_INFO.txt               ← Backup details
```

## 🔄 How to Restore

### Complete Database Restore (Replaces Everything)
```powershell
cd database-backups/COMPLETE_BACKUP_20250105_124244/
.\RESTORE_COMPLETE_DATABASE.ps1
```

### Selective Restore (Specific Parts Only)
```powershell
cd database-backups/COMPLETE_BACKUP_20250105_124244/
.\RESTORE_SELECTIVE.ps1
```

### Manual Restore
```powershell
# Complete restore
pg_restore --clean --if-exists --verbose --dbname="$DATABASE_URL" COMPLETE_DATABASE.dump

# Business data only  
psql "$DATABASE_URL" -f BUSINESS_DATA.sql

# Permissions only
psql "$DATABASE_URL" -f PERMISSIONS_SYSTEM.sql
```

## ⚠️ Important Notes

1. **Complete Clone** - This creates an exact copy of your entire database
2. **Sensitive Data** - Backup contains user emails and auth data - store securely
3. **Test First** - Always test restores on non-production databases
4. **Before Migrations** - Create backup before running permission system migration

## 🚨 Emergency Restore

If you need to quickly restore everything after a failed migration:

```powershell
# 1. Navigate to latest backup
cd database-backups/COMPLETE_BACKUP_[timestamp]/

# 2. Run complete restore
.\RESTORE_COMPLETE_DATABASE.ps1

# 3. Type 'RESTORE' when prompted
```

---

**This backup includes EVERYTHING in your database - it's a complete clone that can fully restore your system if needed.**