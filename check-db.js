const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  'https://wheufegilqkbcsixkoka.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndoZXVmZWdpbHFrYmNzaXhrb2thIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzU3NzE5NzQsImV4cCI6MjA1MTM0Nzk3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'
);

async function checkPermissionsTables() {
  console.log('Checking for permissions-related tables...\n');
  
  try {
    // Check if permissions table exists
    const { data: permissions, error: permError } = await supabase
      .from('permissions')
      .select('*')
      .limit(5);
    
    if (permError) {
      console.log('❌ permissions table:', permError.message);
    } else {
      console.log('✅ permissions table exists with', permissions.length, 'sample records');
      if (permissions.length > 0) {
        console.log('Sample permission:', permissions[0]);
      }
    }
    
    // Check if roles table exists
    const { data: roles, error: rolesError } = await supabase
      .from('roles')
      .select('*')
      .limit(5);
    
    if (rolesError) {
      console.log('❌ roles table:', rolesError.message);
    } else {
      console.log('✅ roles table exists with', roles.length, 'sample records');
      if (roles.length > 0) {
        console.log('Sample role:', roles[0]);
      }
    }
    
    // Check if authorized_users table exists
    const { data: users, error: usersError } = await supabase
      .from('authorized_users')
      .select('*')
      .limit(5);
    
    if (usersError) {
      console.log('❌ authorized_users table:', usersError.message);
    } else {
      console.log('✅ authorized_users table exists with', users.length, 'sample records');
      if (users.length > 0) {
        console.log('Sample user:', users[0]);
      }
    }
    
  } catch (error) {
    console.log('Error checking tables:', error.message);
  }
}

checkPermissionsTables();
