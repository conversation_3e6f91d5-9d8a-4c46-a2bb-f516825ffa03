# Database Backups

Timestamped database backup files and related documentation.

## Directory Structure

### Backup Archives
- `20250905_124244_mcp_backup/` - MCP backup from September 5, 2025, 12:42:44
- `20250905_175807_direct_backup/` - Direct backup from September 5, 2025, 17:58:07

### Documentation
- `COMPLETE_BACKUP_GUIDE.md` - Comprehensive backup and restore procedures

## Backup Naming Convention

Backups follow the pattern: `YYYYMMDD_HHMMSS_backup_type/`

- **YYYY**: Year (4 digits)
- **MM**: Month (2 digits)
- **DD**: Day (2 digits)
- **HH**: Hour (24-hour format)
- **MM**: Minutes
- **SS**: Seconds
- **backup_type**: Description of backup method (mcp, direct, etc.)

## Backup Types

### MCP Backups
- Created using MCP (Model Context Protocol) tools
- Automated backup process
- Includes metadata and context information

### Direct Backups
- Direct database exports
- Manual or script-driven backups
- Raw SQL dump format

## Backup Contents

Each backup directory typically contains:
- `*.sql` files - Database schema and data dumps
- Table-specific backup files
- Metadata and configuration files
- Backup creation logs

## Usage

### Restore from Backup
1. Navigate to the appropriate backup directory
2. Review backup contents and documentation
3. Execute restore scripts or import SQL files
4. Verify data integrity after restore

### Backup Verification
- Check file sizes and timestamps
- Verify backup completeness
- Test restore procedures periodically

## Retention Policy

- Keep daily backups for 30 days
- Keep weekly backups for 3 months  
- Keep monthly backups for 1 year
- Archive yearly backups for long-term storage

## Security Considerations

- Encrypt backups containing sensitive data
- Store backups in secure, off-site locations
- Limit access to backup files
- Regular security audits of backup procedures

## Notes

- Always test restore procedures before relying on backups
- Monitor backup storage space and cleanup old backups
- Document any manual steps required for restoration
- Keep backup documentation updated with schema changes