-- Migration: Cleanup Unused Fields (Phase 2)
-- Date: 2025-05-30
-- Purpose: Remove unused fields after successful migration to simple_sum
-- Warning: Run this ONLY after validating the application still works correctly

-- =============================================================================
-- VALIDATION BEFORE CLEANUP
-- =============================================================================

-- Check that all templates are now simple_sum
SELECT 
    'PRE-CLEANUP CHECK' as status,
    calculation_method,
    COUNT(*) as count
FROM calculation_templates 
GROUP BY calculation_method;

-- Verify no active applications depend on removed fields
SELECT 
    'DEPENDENCY CHECK' as status,
    COUNT(*) as templates_with_weights,
    COUNT(CASE WHEN formula IS NOT NULL AND formula != '' THEN 1 END) as templates_with_formulas
FROM calculation_templates 
WHERE calculation_method = 'simple_sum';

-- =============================================================================
-- OPTIONAL CLEANUP (uncomment when ready)
-- =============================================================================

/*
-- WARNING: This will permanently remove the following fields:
-- - component_weights (JSONB column with weight multipliers)
-- - formula (TEXT column with custom formulas)  
-- - formula_builder (JSONB column with formula structure)

-- Step 1: Create final backup before field removal
CREATE TABLE calculation_templates_pre_cleanup_backup AS 
SELECT * FROM calculation_templates;

-- Step 2: Remove unused fields
ALTER TABLE calculation_templates 
DROP COLUMN IF EXISTS component_weights,
DROP COLUMN IF EXISTS formula,
DROP COLUMN IF EXISTS formula_builder;

-- Step 3: Verify cleanup
SELECT column_name, data_type
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'calculation_templates'
ORDER BY ordinal_position;
*/

-- =============================================================================
-- ROLLBACK FOR CLEANUP (if fields were removed and need restoration)
-- =============================================================================

/*
-- Restore removed fields from backup
ALTER TABLE calculation_templates 
ADD COLUMN IF NOT EXISTS component_weights JSONB DEFAULT '{}'::jsonb,
ADD COLUMN IF NOT EXISTS formula TEXT,
ADD COLUMN IF NOT EXISTS formula_builder JSONB DEFAULT '[]'::jsonb;

-- Restore data from backup
UPDATE calculation_templates 
SET 
    component_weights = backup.component_weights,
    formula = backup.formula,
    formula_builder = backup.formula_builder
FROM calculation_templates_pre_cleanup_backup backup
WHERE calculation_templates.id = backup.id;
*/