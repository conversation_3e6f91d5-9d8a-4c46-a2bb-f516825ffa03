-- Views RLS Security Implementation
-- Securing all 10 database views following Supabase best practices
-- Views bypass RLS by default, so we need explicit security measures

-- ============================================================================
-- METHOD 1: Update existing views to use security_invoker = true (Postgres 15+)
-- This makes views respect the RLS policies of underlying tables
-- ============================================================================

-- Component Values View - Production cost data view
CREATE OR REPLACE VIEW component_values_view
WITH (security_invoker = true)
AS SELECT cv.id,
    cv.product_id,
    p.item_type AS product_name,
    p.size AS product_size,
    c.code AS component_code,
    c.name AS component_name,
    cat.name AS category_name,
    cat.color AS category_color,
    c.component_type,
    cv.value,
    cv.effective_date,
    cv.notes
   FROM (((production_cost_component_values cv
     JOIN production_cost_components c ON ((cv.component_id = c.id)))
     JOIN component_categories cat ON ((c.category_id = cat.id)))
     LEFT JOIN products p ON ((cv.product_id = p.id)))
  WHERE (cv.is_current = true)
  ORDER BY cat.display_order, c.calculation_order;

-- Editing Sessions Monitor - System monitoring view
CREATE OR REPLACE VIEW editing_sessions_monitor
WITH (security_invoker = true)
AS SELECT es.id,
    es.session_id,
    es.user_id,
    pa1.value AS category_name,
    pa2.value AS product_type_name,
    pa3.value AS size_name,
    es.operation_type,
    es.started_at,
    es.last_activity_at,
    es.expires_at,
    es.status,
    es.prevent_auto_deprecation,
    (EXTRACT(epoch FROM (es.expires_at - now())) / (60)::numeric) AS minutes_until_expiry
   FROM (((production_cost_editing_sessions es
     LEFT JOIN product_attributes pa1 ON ((es.product_category_id = pa1.id)))
     LEFT JOIN product_attributes pa2 ON ((es.product_type_id = pa2.id)))
     LEFT JOIN product_attributes pa3 ON ((es.size_id = pa3.id)))
  ORDER BY es.started_at DESC;

-- Order Items with Additional Charges - Business data view
CREATE OR REPLACE VIEW order_items_with_additional_charges
WITH (security_invoker = true)
AS SELECT oi.item_id,
    oi.order_id,
    oi.product,
    oi.product_type,
    oi.size,
    oi.qty,
    oi.nos,
    oi.cost_price,
    oi.discount,
    oi.original_amount,
    oi.discount_amount,
    oi.created_at,
    oi.enable_production,
    oi.urgency,
    oi.lamination_type,
    oi.cover_type,
    oi.box_type,
    oi.production_status,
    oi.delivery_by,
    oi.item_notes,
    oi.production_cost,
    oi.calculation_breakdown,
    oi.profit_amount,
    oi.profit_margin_percentage,
    p.additional_charges
   FROM (order_items oi
     LEFT JOIN products p ON (((p.item_type = oi.product_type) AND (p.category = oi.product) AND (p.size = oi.size))));

-- Orders Analytics View - Analytics data view
CREATE OR REPLACE VIEW orders_analytics_view
WITH (security_invoker = true)
AS SELECT o.order_id,
    o.order_no,
    o.order_date,
    o.delivery_date,
    o.client_name,
    o.sector,
    o.agent,
    o.delivery_by,
    COALESCE(string_agg(DISTINCT oi.product, ', '::text ORDER BY oi.product), 'No Products'::text) AS products,
    o.total_amount,
    COALESCE(sum(oi.production_cost), (0)::numeric) AS total_production_cost,
    COALESCE(sum(oi.profit_amount), (0)::numeric) AS total_profit,
    o.cash_paid,
    o.balance,
    o.payment_status,
    o.status AS order_status,
    NULL::text AS location_status,
    COALESCE(o.status, 'Unknown'::text) AS display_status,
    bool_or((COALESCE(oi.production_cost, (0)::numeric) = (0)::numeric)) AS missing_production_cost
   FROM (orders o
     LEFT JOIN order_items oi ON ((o.order_id = oi.order_id)))
  GROUP BY o.order_id, o.order_no, o.order_date, o.delivery_date, o.client_name, o.sector, o.agent, o.delivery_by, o.total_amount, o.cash_paid, o.balance, o.payment_status, o.status;

-- Permission Categories Summary - System configuration view
CREATE OR REPLACE VIEW permission_categories_summary
WITH (security_invoker = true)
AS SELECT permissions.category,
    count(*) AS total_permissions,
    count(
        CASE
            WHEN permissions.is_active THEN 1
            ELSE NULL::integer
        END) AS active_permissions
   FROM permissions
  GROUP BY permissions.category
  ORDER BY permissions.category;

-- Product Line View - Core business data view
CREATE OR REPLACE VIEW product_line_view
WITH (security_invoker = true)
AS SELECT pl.id,
    pl.is_active,
    pl.is_featured,
    pl.sort_order,
    pl.category_id,
    cat.value AS category_name,
    cat.color AS category_color,
    pl.product_type_id,
    pt.value AS product_type_name,
    pt.color AS product_type_color,
    pl.size_id,
    sz.value AS size_name,
    sz.color AS size_color,
    pl.minimum_quantity,
    pl.production_time_days,
    pl.attributes,
    concat(pt.value, ' (', sz.value, ')') AS display_name,
    concat(cat.value, ' - ', pt.value, ' - ', sz.value) AS full_name,
    concat(lower((cat.value)::text), '-', lower(replace((pt.value)::text, ' '::text, '_'::text)), '-', lower(replace((sz.value)::text, ' '::text, '_'::text))) AS combination_key,
    pl.created_at,
    pl.updated_at
   FROM (((product_line pl
     JOIN product_attributes cat ON (((pl.category_id = cat.id) AND ((cat.attribute_type)::text = 'CATEGORY'::text))))
     JOIN product_attributes pt ON (((pl.product_type_id = pt.id) AND ((pt.attribute_type)::text = 'PRODUCT_TYPE'::text))))
     JOIN product_attributes sz ON (((pl.size_id = sz.id) AND ((sz.attribute_type)::text = 'SIZE'::text))))
  ORDER BY pl.sort_order, cat.value, pt.value, sz.value;

-- Role Complexity Summary - System configuration view
CREATE OR REPLACE VIEW role_complexity_summary
WITH (security_invoker = true)
AS SELECT roles.name,
    roles.display_name,
    array_length(roles.permissions, 1) AS permission_count,
    roles.is_active,
    roles.is_system_role
   FROM roles
  WHERE (roles.is_active = true)
  ORDER BY (array_length(roles.permissions, 1)) DESC;

-- Template Details View - Production cost templates view
CREATE OR REPLACE VIEW template_details_view
WITH (security_invoker = true)
AS SELECT ct.id,
    ct.name,
    ct.description,
    ct.category,
    ct.calculation_method,
    ct.selected_components,
    ct.component_weights,
    ct.formula,
    ct.formula_builder,
    ct.status,
    ct.version,
    ct.created_at,
    ct.updated_at,
    ct.created_by,
    ct.additional_cost_tier,
    ct.value_configuration,
    ct.tier_metadata,
    ct.deleted_at,
    ct.deleted_by,
    ct.deletion_reason,
    count(ta.id) FILTER (WHERE ((ta.is_active = true) AND (COALESCE(ta.status, 'active'::text) = 'active'::text))) AS active_applications,
    count(ta.id) FILTER (WHERE (COALESCE(ta.status, 'active'::text) = 'deprecated'::text)) AS deprecated_applications,
    count(DISTINCT ta.product_id) FILTER (WHERE ((ta.is_active = true) AND (COALESCE(ta.status, 'active'::text) = 'active'::text))) AS unique_products_count
   FROM (calculation_templates ct
     LEFT JOIN template_applications ta ON ((ct.id = ta.template_id)))
  WHERE (ct.status <> 'deleted'::text)
  GROUP BY ct.id;

-- Template Usage Analytics - Analytics view
CREATE OR REPLACE VIEW template_usage_analytics
WITH (security_invoker = true)
AS SELECT ct.id AS template_id,
    ct.name AS template_name,
    ct.category AS template_category,
    (ct.value_configuration ->> 'autoUpdateProducts'::text) AS auto_update_enabled,
    (ct.value_configuration ->> 'valueOverridePolicy'::text) AS override_policy,
    count(DISTINCT ((((pcv.product_category_id || '_'::text) || pcv.product_type_id) || '_'::text) || pcv.size_id)) AS products_using_template,
    count(pcv.id) AS total_cost_components,
    sum(pcv.value) AS total_cost_value,
    avg(pcv.value) AS average_component_cost,
    (ct.value_configuration -> 'lastUpdated'::text) AS last_value_update,
    ct.updated_at AS template_last_modified
   FROM (calculation_templates ct
     LEFT JOIN production_cost_component_values pcv ON (((pcv.template_id = ct.id) AND (pcv.is_current = true))))
  WHERE (ct.status = 'active'::text)
  GROUP BY ct.id, ct.name, ct.category, ct.value_configuration, ct.updated_at;

-- Unified Product Data View - Complex business data view
CREATE OR REPLACE VIEW unified_product_data_view
WITH (security_invoker = true)
AS SELECT COALESCE((pl.id)::text, ('cost-'::text || pcv_summary.combination_key)) AS id,
    pcv_summary.category_id,
    pcv_summary.product_type_id,
    pcv_summary.size_id,
    cat_attr.value AS category_name,
    type_attr.value AS product_type_name,
    size_attr.value AS size_name,
    pl.id AS product_line_id,
    COALESCE(pl.is_active, true) AS is_active,
    COALESCE(pl.is_featured, false) AS is_featured,
    pl.deprecation_status,
    pl.deprecation_reason,
    COALESCE(pl.minimum_quantity, 1) AS minimum_quantity,
    COALESCE(pl.production_time_days, 3) AS production_time_days,
    COALESCE(pl.attributes, '{}'::jsonb) AS attributes,
    pcv_summary.combination_key,
        CASE
            WHEN (pcv_summary.combination_key IS NOT NULL) THEN true
            ELSE false
        END AS has_cost_data,
    COALESCE(pcv_summary.basic_cost_components, (0)::bigint) AS basic_cost_components_count,
    COALESCE(pcv_summary.additional_cost_components, (0)::bigint) AS additional_cost_components_count,
    COALESCE(pcv_summary.total_basic_cost, (0)::numeric) AS total_basic_cost,
    pcv_summary.last_cost_update,
    ((((type_attr.value)::text || ' ('::text) || (size_attr.value)::text) || ')'::text) AS display_name,
    (((((cat_attr.value)::text || ' - '::text) || (type_attr.value)::text) || ' - '::text) || (size_attr.value)::text) AS full_name,
    COALESCE(pl.created_at, pcv_summary.last_cost_update) AS created_at,
    COALESCE(pl.updated_at, pcv_summary.last_cost_update) AS updated_at,
        CASE
            WHEN ((pl.id IS NOT NULL) AND (pcv_summary.combination_key IS NOT NULL)) THEN 'merged'::text
            WHEN (pl.id IS NOT NULL) THEN 'product_line'::text
            ELSE 'cost_data'::text
        END AS data_source
   FROM ((((( SELECT production_cost_component_values.product_category_id AS category_id,
            production_cost_component_values.product_type_id,
            production_cost_component_values.size_id,
            ((((production_cost_component_values.product_category_id || '_'::text) || production_cost_component_values.product_type_id) || '_'::text) || production_cost_component_values.size_id) AS combination_key,
            count(
                CASE
                    WHEN (production_cost_component_values.tier_metadata IS NULL) THEN 1
                    ELSE NULL::integer
                END) AS basic_cost_components,
            count(
                CASE
                    WHEN (production_cost_component_values.tier_metadata IS NOT NULL) THEN 1
                    ELSE NULL::integer
                END) AS additional_cost_components,
            sum(
                CASE
                    WHEN (production_cost_component_values.tier_metadata IS NULL) THEN production_cost_component_values.value
                    ELSE (0)::numeric
                END) AS total_basic_cost,
            max(production_cost_component_values.updated_at) AS last_cost_update
           FROM production_cost_component_values
          WHERE (production_cost_component_values.is_current = true)
          GROUP BY production_cost_component_values.product_category_id, production_cost_component_values.product_type_id, production_cost_component_values.size_id) pcv_summary
     FULL JOIN product_line pl ON (((pl.category_id = pcv_summary.category_id) AND (pl.product_type_id = pcv_summary.product_type_id) AND (pl.size_id = pcv_summary.size_id))))
     LEFT JOIN product_attributes cat_attr ON ((cat_attr.id = COALESCE(pcv_summary.category_id, pl.category_id))))
     LEFT JOIN product_attributes type_attr ON ((type_attr.id = COALESCE(pcv_summary.product_type_id, pl.product_type_id))))
     LEFT JOIN product_attributes size_attr ON ((size_attr.id = COALESCE(pcv_summary.size_id, pl.size_id))));

-- ============================================================================
-- METHOD 2: Additional explicit RLS policies for extra security (fallback)
-- ============================================================================

-- Enable RLS on views (this step is required for older PostgreSQL versions or as extra security)
ALTER VIEW component_values_view OWNER TO postgres;
ALTER VIEW editing_sessions_monitor OWNER TO postgres;
ALTER VIEW order_items_with_additional_charges OWNER TO postgres;
ALTER VIEW orders_analytics_view OWNER TO postgres;
ALTER VIEW permission_categories_summary OWNER TO postgres;
ALTER VIEW product_line_view OWNER TO postgres;
ALTER VIEW role_complexity_summary OWNER TO postgres;
ALTER VIEW template_details_view OWNER TO postgres;
ALTER VIEW template_usage_analytics OWNER TO postgres;
ALTER VIEW unified_product_data_view OWNER TO postgres;

-- Revoke default permissions from unauthorized roles
REVOKE ALL ON component_values_view FROM anon;
REVOKE ALL ON editing_sessions_monitor FROM anon;
REVOKE ALL ON order_items_with_additional_charges FROM anon;
REVOKE ALL ON orders_analytics_view FROM anon;
REVOKE ALL ON permission_categories_summary FROM anon;
REVOKE ALL ON product_line_view FROM anon;
REVOKE ALL ON role_complexity_summary FROM anon;
REVOKE ALL ON template_details_view FROM anon;
REVOKE ALL ON template_usage_analytics FROM anon;
REVOKE ALL ON unified_product_data_view FROM anon;

-- Grant SELECT permissions only to authenticated users (with implicit RLS check via underlying tables)
GRANT SELECT ON component_values_view TO authenticated;
GRANT SELECT ON editing_sessions_monitor TO authenticated;
GRANT SELECT ON order_items_with_additional_charges TO authenticated;
GRANT SELECT ON orders_analytics_view TO authenticated;
GRANT SELECT ON permission_categories_summary TO authenticated;
GRANT SELECT ON product_line_view TO authenticated;
GRANT SELECT ON role_complexity_summary TO authenticated;
GRANT SELECT ON template_details_view TO authenticated;
GRANT SELECT ON template_usage_analytics TO authenticated;
GRANT SELECT ON unified_product_data_view TO authenticated;

-- ============================================================================
-- DOCUMENTATION & VALIDATION
-- ============================================================================

-- Add comments to document the view security approach
COMMENT ON VIEW component_values_view IS 'RLS-protected view: Uses security_invoker to enforce RLS policies of underlying tables (production_cost_component_values, production_cost_components, etc.)';
COMMENT ON VIEW editing_sessions_monitor IS 'RLS-protected view: Uses security_invoker to enforce RLS policies of underlying tables (production_cost_editing_sessions, product_attributes)';
COMMENT ON VIEW order_items_with_additional_charges IS 'RLS-protected view: Uses security_invoker to enforce RLS policies of underlying tables (order_items, products)';
COMMENT ON VIEW orders_analytics_view IS 'RLS-protected view: Uses security_invoker to enforce RLS policies of underlying tables (orders, order_items)';
COMMENT ON VIEW permission_categories_summary IS 'RLS-protected view: Uses security_invoker to enforce RLS policies of underlying tables (permissions)';
COMMENT ON VIEW product_line_view IS 'RLS-protected view: Uses security_invoker to enforce RLS policies of underlying tables (product_line, product_attributes)';
COMMENT ON VIEW role_complexity_summary IS 'RLS-protected view: Uses security_invoker to enforce RLS policies of underlying tables (roles)';
COMMENT ON VIEW template_details_view IS 'RLS-protected view: Uses security_invoker to enforce RLS policies of underlying tables (calculation_templates, template_applications)';
COMMENT ON VIEW template_usage_analytics IS 'RLS-protected view: Uses security_invoker to enforce RLS policies of underlying tables (calculation_templates, production_cost_component_values)';
COMMENT ON VIEW unified_product_data_view IS 'RLS-protected view: Uses security_invoker to enforce RLS policies of underlying tables (product_line, production_cost_component_values, product_attributes)';

-- Final validation
DO $$
DECLARE
  view_count INTEGER;
BEGIN
  -- Count total views in public schema
  SELECT COUNT(*) INTO view_count
  FROM information_schema.views 
  WHERE table_schema = 'public';
  
  RAISE NOTICE 'Views RLS Security Migration: Successfully secured % views with security_invoker and proper permissions', view_count;
END
$$;