# Attribute System Migration Guide

## Overview
We are migrating from a triple-system chaos (Zustand + SWR + "Unified") to a single SWR-based system.

## Migration Pattern

### Before (Zustand):
```typescript
import { useAttributesStore } from '../../../../stores/attributesStore';

const Component = () => {
  const { attributesByType, getValuesByType } = useAttributesStore();
  const [coverTypes, setCoverTypes] = useState<string[]>([]);
  
  useEffect(() => {
    const loadValues = async () => {
      const values = await getValuesByType(AttributeType.COVER_TYPE);
      setCoverTypes(values);
    };
    loadValues();
  }, [getValuesByType]);
};
```

### After (SWR):
```typescript
import { useAttributesSWR } from '../../../../hooks/useAttributesSWR';

const Component = () => {
  const { attributesByType, isLoading } = useAttributesSWR();
  
  const coverTypes = useMemo(() => {
    const covers = attributesByType[AttributeType.COVER_TYPE] || [];
    return covers
      .filter(attr => attr.status === 'active' && attr.value)
      .map(attr => attr.value)
      .sort();
  }, [attributesByType]);
};
```

## Key Changes:
1. Import `useAttributesSWR` instead of `useAttributesStore`
2. Remove state variables and useEffects for loading values
3. Use `useMemo` to extract values directly from `attributesByType`
4. Add filtering for active status and sorting for consistency
5. Access `isLoading` state when needed

## Files Migrated:
- [x] App.tsx - Removed fetchAttributes initialization
- [x] ItemsSection.tsx - Migrated to SWR
- [x] BasicInfoSection.tsx - Migrated to SWR
- [ ] ProductCombinationSelector.tsx - TODO
- [ ] ... (23 more files)

## Testing:
After each migration, verify:
1. Attributes load correctly on component mount
2. Real-time updates work (when enabled)
3. No console errors about missing data