import { supabase } from '../../lib/supabase';
import { PreAuthService } from '../preAuthService';
import type { AuthorizedUser } from '../preAuthService';

/**
 * User Authentication State Detection Service
 * 
 * Determines the exact state of a user's authentication journey:
 * 1. UNAUTHORIZED - Not in authorized_users table
 * 2. AUTHORIZED_ONLY - In authorized_users, no auth.users record
 * 3. SIGNUP_STARTED - In authorized_users + auth.users but unconfirmed
 * 4. SIGNUP_INCOMPLETE - In authorized_users + confirmed auth.users but no profile
 * 5. ACCOUNT_COMPLETE - In all tables with complete profile
 */

export type UserAuthState = 
  | 'unauthorized'        // Not in authorized_users - reject
  | 'authorized_only'     // In authorized_users only, no profile - direct to signup
  | 'signup_started'      // In authorized_users + auth.users but unconfirmed - continue signup
  | 'signup_incomplete'   // In authorized_users + incomplete profile - complete signup
  | 'account_complete';   // All tables populated - direct to login

export interface UserStateResult {
  state: UserAuthState;
  authorizedUser?: AuthorizedUser;
  authUser?: {
    id: string;
    email: string;
    confirmed_at: string | null;
    created_at: string;
  };
  profile?: {
    id: string;
    email: string;
    full_name: string | null;
    role: string | null;
  };
  message: string;
  canSignup: boolean;
  canLogin: boolean;
  nextAction: string;
}

/**
 * Service for detecting user authentication state using Edge Function with SERVICE_ROLE_KEY
 * 
 * Uses comprehensive edge function approach for reliable state detection
 * Matches the successful pattern used in PreAuthService
 */
export class UserStateDetectionService {
  
  /**
   * Detect the current authentication state of a user by email via Edge Function
   * 
   * Uses SERVICE_ROLE_KEY via edge function to bypass RLS and get comprehensive state
   * Falls back to RLS-based detection if edge function fails
   */
  static async detectUserState(email: string): Promise<UserStateResult> {
    try {
      // Primary: Try edge function approach (reliable, uses SERVICE_ROLE_KEY)
      const edgeFunctionResult = await this.detectUserStateViaEdgeFunction(email);
      if (edgeFunctionResult) {
        console.log('User state detected via Edge Function (SERVICE_ROLE_KEY)');
        return edgeFunctionResult;
      }

      // Fallback: Use RLS-based detection
      console.log('Edge Function failed, falling back to RLS-based detection');
      return await this.detectUserStateFallback(email);

    } catch (error) {
      console.error('Error detecting user state:', error);
      throw new Error('Failed to determine user authentication state');
    }
  }

  /**
   * Primary method: Detect user state via Edge Function using SERVICE_ROLE_KEY
   */
  private static async detectUserStateViaEdgeFunction(email: string): Promise<UserStateResult | null> {
    try {
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      
      if (!supabaseUrl) {
        console.error('Missing VITE_SUPABASE_URL environment variable');
        return null;
      }

      console.log('Attempting user state detection via Edge Function:', email);

      // Call Edge Function for comprehensive state detection with SERVICE_ROLE_KEY
      const response = await fetch(`${supabaseUrl}/functions/v1/detect-user-state`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        console.error(`Edge Function HTTP error: ${response.status}: ${response.statusText}`);
        return null;
      }

      const result = await response.json();

      if (result.error) {
        console.error('Edge Function returned error:', result.error);
        return null;
      }

      return result as UserStateResult;
    } catch (error) {
      console.error('Error calling user state detection Edge Function:', error);
      return null;
    }
  }

  /**
   * Fallback method: RLS-based user state detection (original implementation)
   */
  private static async detectUserStateFallback(email: string): Promise<UserStateResult> {
    console.log('Using fallback RLS-based user state detection for:', email);
    
    // Step 1: Check authorized_users table (permission to participate)
    const authorizedUser = await PreAuthService.validateEmail(email);
    
    if (!authorizedUser) {
      return {
        state: 'unauthorized',
        message: 'Email not authorized for this system',
        canSignup: false,
        canLogin: false,
        nextAction: 'Contact administrator for access'
      };
    }

    // Step 2: Check profiles table to infer overall account state
    // We'll search by email since we may not have the auth user ID
    const { data: profiles, error: profileError } = await supabase
      .from('profiles')
      .select('id, email, full_name, role, created_at')
      .eq('email', email);

    if (profileError) {
      console.error('Error checking profiles:', profileError);
      // Don't throw - treat as no profile exists
    }

    const profile = profiles && profiles.length > 0 ? profiles[0] : null;

    // Step 3: Determine state based on profile existence and completeness
    if (!profile) {
      // No profile = no auth.users record (profiles are auto-created)
      return {
        state: 'authorized_only',
        authorizedUser,
        message: 'Ready to create account',
        canSignup: true,
        canLogin: false,
        nextAction: 'Start signup process'
      };
    }

    // Step 4: Check if profile is complete
    if (!profile.full_name || !profile.role) {
      // Profile exists but incomplete = auth.users confirmed but setup not finished
      return {
        state: 'signup_incomplete',
        authorizedUser,
        profile,
        message: 'Account exists but profile incomplete',
        canSignup: true, // Can complete signup
        canLogin: true,  // Allow login for admin roles (updated to match edge function)
        nextAction: 'Complete profile setup'
      };
    }

    // Step 5: Complete account - ready for login
    return {
      state: 'account_complete',
      authorizedUser,
      profile,
      message: 'Account fully configured',
      canSignup: false,
      canLogin: true,
      nextAction: 'Use login form'
    };
  }

  /**
   * Get appropriate route for user based on their current state
   */
  static getRouteForState(state: UserAuthState, currentRoute: string): string {
    switch (state) {
      case 'unauthorized':
        return '/login'; // Will show error about unauthorized

      case 'authorized_only':
      case 'signup_started':
      case 'signup_incomplete':
        // All incomplete states should go to signup flow
        if (currentRoute === '/login') {
          return '/pre-auth'; // Redirect login attempts to signup
        }
        return currentRoute; // Stay on current signup route

      case 'account_complete':
        // Complete accounts should use login
        if (currentRoute === '/pre-auth' || currentRoute === '/account-creation') {
          return '/login'; // Redirect signup attempts to login
        }
        return currentRoute; // Stay on current route

      default:
        return '/login';
    }
  }

  /**
   * Validate if user should be allowed on current route
   */
  static shouldAllowRoute(state: UserAuthState, route: string): {
    allowed: boolean;
    redirectTo?: string;
    reason?: string;
  } {
    switch (route) {
      case '/login':
        if (state === 'account_complete') {
          return { allowed: true };
        }
        return {
          allowed: true, // Allow but will show appropriate error
          reason: 'Account not complete - will redirect to signup'
        };

      case '/pre-auth':
        if (state === 'unauthorized') {
          return { allowed: true }; // Let them try, will show error
        }
        if (state === 'account_complete') {
          return {
            allowed: true, // Allow but will redirect to login
            reason: 'Account already exists - will redirect to login'
          };
        }
        return { allowed: true }; // All other states can access signup

      case '/account-creation':
        if (state === 'unauthorized') {
          return {
            allowed: false,
            redirectTo: '/pre-auth',
            reason: 'Must verify email first'
          };
        }
        return { allowed: true }; // All authorized states can access

      default:
        return { allowed: true }; // Allow other routes by default
    }
  }
}