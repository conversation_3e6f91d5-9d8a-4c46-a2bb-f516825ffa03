# Feature Migrations

New functionality implementations, analytics systems, and application enhancements.

## Files in this directory:

### Cost & Pricing Features
- `001_add_additional_cost_tier.sql` - Additional cost tier system
- `003_add_tier_metadata.sql` - Tier metadata functionality

### Template System
- `002_add_template_value_configuration.sql` - Template value configuration system
- `008_template_deletion_and_product_deprecation.sql` - Template lifecycle management

### Analytics & Reporting
- `005_analytics_general_overview_data.sql` - General analytics overview
- `006_analytics_get_client_performance.sql` - Client performance analytics
- `007_client_profit_analytics.sql` - Client profitability analysis
- `009_analytics_production_cost_breakdown_functions.sql` - Production cost analytics

### Product Management
- `004_add_product_component_support.sql` - Product component support system

## Execution Order

Execute feature migrations **last**, after all other categories:

1. Core features (`001_*` through `004_*`)
2. Analytics systems (`005_*`, `006_*`, `007_*`)
3. Advanced features (`008_*`, `009_*`)

## Dependencies

- Requires completed schema, data, and permission migrations
- Analytics features depend on existing data structure
- Template system requires product and order tables

## Impact Assessment

- **Performance**: Analytics functions may impact query performance
- **Storage**: Additional metadata and analytics tables increase storage
- **Complexity**: New features add application complexity

## Notes

- These are optional enhancements to core functionality
- Can be executed selectively based on business needs
- May require application code updates to utilize new features
- Consider monitoring performance after implementing analytics features