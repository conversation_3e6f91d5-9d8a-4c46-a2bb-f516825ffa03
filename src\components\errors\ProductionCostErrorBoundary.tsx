import React from 'react'
import { FeatureErrorBoundary } from '../ui/FeatureErrorBoundary'
import { Calculator } from 'lucide-react'

interface ProductionCostErrorBoundaryProps {
  children: React.ReactNode
}

/**
 * Error boundary specifically for the Production Cost features
 */
export function ProductionCostErrorBoundary({ children }: ProductionCostErrorBoundaryProps) {
  return (
    <FeatureErrorBoundary
      featureName="Production Cost Management"
      fallback={
        <div className="flex items-center justify-center min-h-[400px] p-4">
          <div className="text-center space-y-4">
            <div className="mx-auto p-3 bg-green-100 rounded-full w-fit">
              <Calculator className="h-8 w-8 text-green-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold mb-2">Production Cost System Error</h2>
              <p className="text-muted-foreground mb-4">
                The production cost management system is temporarily unavailable.
              </p>
              <button 
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Reload System
              </button>
            </div>
          </div>
        </div>
      }
    >
      {children}
    </FeatureErrorBoundary>
  )
}