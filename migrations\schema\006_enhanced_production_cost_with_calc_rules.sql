-- Migration: Enhanced Production Cost Calculation with Calculation Rules Integration
-- This migration updates the production cost calculation to respect calculation rules
-- and creates announcements when calculation rules are missing

-- ============================================
-- STEP 1: Helper Function to Get Calculation Rule Multiplier
-- ============================================

CREATE OR REPLACE FUNCTION get_calculation_rule_multiplier(
    p_product_category_name TEXT,
    p_quantity INTEGER,
    p_nos INTEGER DEFAULT 1,
    p_base_cost NUMERIC DEFAULT 0
) RETURNS TABLE (
    multiplied_cost NUMERIC,
    rule_found BOOLEAN,
    rule_type TEXT,
    error_message TEXT
) AS $$
DECLARE
    v_rule_record RECORD;
    v_multiplied_cost NUMERIC := 0;
    v_rule_found BOOLEAN := false;
    v_error_message TEXT := NULL;
BEGIN
    -- Get the calculation rule for this product category
    SELECT rule_type, applies_to_fields, product_category
    INTO v_rule_record
    FROM calculation_rules 
    WHERE rule_category = 'product_category' 
      AND (product_category = p_product_category_name OR product_category = 'Default')
      AND status = 'active'
    ORDER BY CASE WHEN product_category = p_product_category_name THEN 1 ELSE 2 END
    LIMIT 1;
    
    -- Check if rule was found
    IF v_rule_record.rule_type IS NOT NULL THEN
        v_rule_found := true;
        
        -- Apply the rule logic to base cost
        CASE v_rule_record.rule_type
            WHEN 'multiply_by_quantity' THEN
                v_multiplied_cost := p_base_cost * p_quantity;
            WHEN 'multiply_by_quantity_and_nos' THEN  
                v_multiplied_cost := p_base_cost * p_quantity * COALESCE(p_nos, 1);
            ELSE
                v_multiplied_cost := p_base_cost * p_quantity; -- fallback
                v_error_message := 'Unknown rule type: ' || v_rule_record.rule_type;
        END CASE;
    ELSE
        -- No rule found
        v_rule_found := false;
        v_multiplied_cost := p_base_cost * p_quantity; -- default fallback
        v_error_message := 'No calculation rule found for category: ' || p_product_category_name;
    END IF;
    
    RETURN QUERY
    SELECT 
        v_multiplied_cost,
        v_rule_found,
        COALESCE(v_rule_record.rule_type, 'default_fallback'),
        v_error_message;
END;
$$ LANGUAGE plpgsql;

-- ============================================
-- STEP 2: Enhanced Production Cost Calculation Function
-- ============================================

CREATE OR REPLACE FUNCTION calculate_item_production_cost_with_calc_rules(
    p_category_id UUID,
    p_product_type_id UUID,
    p_size_id UUID,
    p_quantity INTEGER,
    p_nos INTEGER DEFAULT 1,
    p_order_item_id UUID DEFAULT NULL,
    p_batch_operation_id UUID DEFAULT NULL
) RETURNS TABLE (
    base_cost NUMERIC,
    additional_cost NUMERIC,
    total_cost NUMERIC,
    calculation_success BOOLEAN,
    rule_applied BOOLEAN,
    rule_type TEXT,
    error_logged BOOLEAN,
    announcement_created BOOLEAN
) AS $$
DECLARE
    v_base_cost NUMERIC := 0;
    v_additional_cost NUMERIC := 0;
    v_total_cost NUMERIC := 0;
    v_component_record RECORD;
    v_tier_type TEXT;
    v_component_value NUMERIC;
    
    -- Product info for error reporting
    v_product_category_name TEXT;
    v_product_type_name TEXT;
    v_size_name TEXT;
    
    -- Calculation rule variables
    v_rule_result RECORD;
    v_rule_applied BOOLEAN := false;
    v_rule_type TEXT := 'none';
    
    -- Error handling variables
    v_error_logged BOOLEAN := false;
    v_announcement_created BOOLEAN := false;
    v_error_id UUID;
    v_announcement_id UUID;
BEGIN
    -- Get product names for error reporting and rule lookup
    SELECT pa1.value, pa2.value, pa3.value 
    INTO v_product_category_name, v_product_type_name, v_size_name
    FROM product_attributes pa1, product_attributes pa2, product_attributes pa3
    WHERE pa1.id = p_category_id 
      AND pa2.id = p_product_type_id 
      AND pa3.id = p_size_id;

    -- Calculate base costs from components
    FOR v_component_record IN
        SELECT 
            pcv.value,
            pcv.tier_metadata,
            pcc.name as component_name,
            pcc.category as component_category
        FROM production_cost_component_values pcv
        INNER JOIN production_cost_components pcc ON pcv.component_id = pcc.id
        WHERE pcv.product_category_id = p_category_id
          AND pcv.product_type_id = p_product_type_id
          AND pcv.size_id = p_size_id
          AND pcv.is_current = true
          AND pcc.status = 'active'
    LOOP
        v_component_value := v_component_record.value;
        
        -- Check if this component has tier metadata (additional cost)
        IF v_component_record.tier_metadata IS NOT NULL AND 
           jsonb_typeof(v_component_record.tier_metadata) = 'object' AND
           v_component_record.tier_metadata ? 'tier' THEN
          
            -- This is an additional cost with tier logic
            v_tier_type := v_component_record.tier_metadata->>'tier';
            
            CASE v_tier_type
                WHEN 'per_unit' THEN
                    v_additional_cost := v_additional_cost + (v_component_value * p_quantity);
                WHEN 'per_order' THEN
                    v_additional_cost := v_additional_cost + v_component_value;
                WHEN 'distributed_by_nos' THEN
                    v_additional_cost := v_additional_cost + v_component_value;
                WHEN 'distributed_by_qty' THEN
                    v_additional_cost := v_additional_cost + v_component_value;
                ELSE
                    v_additional_cost := v_additional_cost + v_component_value;
            END CASE;
        ELSE
            -- This is a base cost (no tier metadata)
            v_base_cost := v_base_cost + v_component_value;
        END IF;
    END LOOP;

    -- Apply calculation rule to base cost
    SELECT * INTO v_rule_result
    FROM get_calculation_rule_multiplier(
        v_product_category_name,
        p_quantity,
        p_nos,
        v_base_cost
    );

    -- Check if calculation rule was found and applied
    IF v_rule_result.rule_found THEN
        v_total_cost := v_rule_result.multiplied_cost + v_additional_cost;
        v_rule_applied := true;
        v_rule_type := v_rule_result.rule_type;
    ELSE
        -- No calculation rule found - log error and create announcement
        INSERT INTO calculation_rules_errors (
            error_type,
            error_message,
            severity,
            product_category,
            expected_rule_type,
            available_rules,
            order_item_id,
            quantity,
            nos,
            batch_operation_id
        ) VALUES (
            'missing_calculation_rule',
            v_rule_result.error_message,
            'high',
            v_product_category_name,
            'product_category',
            (
                SELECT jsonb_agg(
                    jsonb_build_object(
                        'product_category', product_category,
                        'rule_type', rule_type
                    )
                )
                FROM calculation_rules 
                WHERE rule_category = 'product_category' 
                  AND status = 'active'
            ),
            p_order_item_id,
            p_quantity,
            p_nos,
            p_batch_operation_id
        ) RETURNING id INTO v_error_id;
        
        v_error_logged := true;

        -- Create announcement for missing calculation rule
        SELECT create_calculation_rules_error_announcement(
            v_error_id,
            v_product_category_name,
            'missing_calculation_rule',
            'Create a calculation rule for "' || v_product_category_name || '" category in Product settings'
        ) INTO v_announcement_id;
        
        v_announcement_created := (v_announcement_id IS NOT NULL);

        -- Use fallback calculation
        v_total_cost := v_rule_result.multiplied_cost + v_additional_cost;
        v_rule_type := 'default_fallback';
    END IF;

    RETURN QUERY
    SELECT 
        v_base_cost,
        v_additional_cost,
        v_total_cost,
        true as calculation_success,
        v_rule_applied,
        v_rule_type,
        v_error_logged,
        v_announcement_created;
END;
$$ LANGUAGE plpgsql;

-- ============================================
-- STEP 3: Update Main Order Production Cost Function
-- ============================================

-- Update the existing function to use the new enhanced calculation
CREATE OR REPLACE FUNCTION calculate_order_production_costs_enhanced(
    p_order_ids UUID[] DEFAULT NULL,
    p_date_from DATE DEFAULT NULL,
    p_date_to DATE DEFAULT NULL,
    p_force_recalculate BOOLEAN DEFAULT FALSE,
    p_dry_run BOOLEAN DEFAULT FALSE
) RETURNS JSONB AS $$
DECLARE
    v_processed_count INTEGER := 0;
    v_updated_count INTEGER := 0;
    v_error_count INTEGER := 0;
    v_rule_errors_count INTEGER := 0;
    v_announcements_created INTEGER := 0;
    v_total_production_cost NUMERIC := 0;
    v_errors JSONB := '[]'::JSONB;
    v_item_record RECORD;
    v_mapping_result RECORD;
    v_cost_result RECORD;
    v_calculated_cost NUMERIC;
    v_error_message TEXT;
    v_batch_id UUID := gen_random_uuid();
BEGIN
    -- Process order items with enhanced calculation
    FOR v_item_record IN
        SELECT 
            oi.item_id,
            oi.order_id,
            oi.product,
            oi.product_type,
            oi.size,
            oi.qty,
            oi.nos,
            oi.discount_amount,
            oi.production_cost as current_production_cost,
            o.created_at
        FROM order_items oi
        INNER JOIN orders o ON oi.order_id = o.order_id
        WHERE 
            (p_order_ids IS NULL OR oi.order_id = ANY(p_order_ids))
            AND (p_date_from IS NULL OR o.created_at::DATE >= p_date_from)
            AND (p_date_to IS NULL OR o.created_at::DATE <= p_date_to)
            AND oi.product IS NOT NULL 
            AND oi.product != ''
            AND oi.product_type IS NOT NULL
            AND oi.size IS NOT NULL
            AND (p_force_recalculate = TRUE OR oi.production_cost = 0 OR oi.production_cost IS NULL)
    LOOP
        v_processed_count := v_processed_count + 1;
        v_error_message := NULL;
        v_calculated_cost := 0;

        BEGIN
            -- Map order item attributes to IDs
            SELECT * INTO v_mapping_result
            FROM map_order_item_to_attributes(
                v_item_record.product,
                v_item_record.product_type,
                v_item_record.size
            );

            IF NOT v_mapping_result.mapping_success THEN
                v_error_message := 'Failed to map attributes to IDs';
                RAISE EXCEPTION '%', v_error_message;
            END IF;

            -- Calculate production cost with enhanced calculation rules support
            SELECT * INTO v_cost_result
            FROM calculate_item_production_cost_with_calc_rules(
                v_mapping_result.category_id,
                v_mapping_result.product_type_id,
                v_mapping_result.size_id,
                COALESCE(v_item_record.qty, 1),
                COALESCE(v_item_record.nos, 1),
                v_item_record.item_id,
                v_batch_id
            );

            IF NOT v_cost_result.calculation_success THEN
                v_error_message := 'Production cost calculation failed';
                RAISE EXCEPTION '%', v_error_message;
            END IF;

            v_calculated_cost := v_cost_result.total_cost;

            -- Track calculation rule errors
            IF v_cost_result.error_logged THEN
                v_rule_errors_count := v_rule_errors_count + 1;
            END IF;

            IF v_cost_result.announcement_created THEN
                v_announcements_created := v_announcements_created + 1;
            END IF;

            -- Update production cost if not dry run
            IF NOT p_dry_run THEN
                UPDATE order_items
                SET production_cost = v_calculated_cost
                WHERE item_id = v_item_record.item_id;
            END IF;

            v_updated_count := v_updated_count + 1;
            v_total_production_cost := v_total_production_cost + v_calculated_cost;

        EXCEPTION WHEN OTHERS THEN
            v_error_count := v_error_count + 1;
            v_error_message := COALESCE(v_error_message, SQLERRM);
            
            -- Add error to error list
            v_errors := v_errors || jsonb_build_object(
                'item_id', v_item_record.item_id,
                'order_id', v_item_record.order_id,
                'product', v_item_record.product,
                'product_type', v_item_record.product_type,
                'size', v_item_record.size,
                'error', v_error_message
            );
        END;
    END LOOP;

    -- Return enhanced summary results
    RETURN jsonb_build_object(
        'processed_count', v_processed_count,
        'updated_count', v_updated_count,
        'error_count', v_error_count,
        'calculation_rule_errors', v_rule_errors_count,
        'announcements_created', v_announcements_created,
        'total_production_cost', v_total_production_cost,
        'average_production_cost', CASE 
            WHEN v_updated_count > 0 THEN v_total_production_cost / v_updated_count 
            ELSE 0 
        END,
        'batch_id', v_batch_id,
        'dry_run', p_dry_run,
        'errors', v_errors,
        'summary', jsonb_build_object(
            'success_rate', CASE 
                WHEN v_processed_count > 0 THEN (v_updated_count::NUMERIC / v_processed_count) * 100
                ELSE 0
            END,
            'calculation_rules_health', CASE
                WHEN v_rule_errors_count = 0 THEN 'healthy'
                WHEN v_rule_errors_count < (v_processed_count * 0.1) THEN 'minor_issues'
                ELSE 'needs_attention'
            END
        )
    );
END;
$$ LANGUAGE plpgsql;