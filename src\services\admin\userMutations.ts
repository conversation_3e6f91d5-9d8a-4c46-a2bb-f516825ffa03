import { supabase } from '../../lib/supabase'
import type { AuthorizedUser, CreateUserRequest, UpdateUserRequest } from './types'

export class UserMutations {
  /**
   * Create a new authorized user via Edge Function (primary) with RLS fallback
   */
  static async createUser(userData: CreateUserRequest, invitedBy?: string): Promise<AuthorizedUser> {
    try {
      // Primary: Try Edge Function approach (reliable, uses SERVICE_ROLE_KEY)
      const edgeFunctionResult = await this.createUserViaEdgeFunction(userData, invitedBy);
      if (edgeFunctionResult) {
        console.log('User created via Edge Function (SERVICE_ROLE_KEY)');
        return edgeFunctionResult;
      }

      // Fallback: Use RLS-based creation
      console.log('Edge Function failed, falling back to RLS-based creation');
      return await this.createUserFallback(userData, invitedBy);
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  /**
   * Primary method: Create user via Edge Function using SERVICE_ROLE_KEY
   */
  private static async createUserViaEdgeFunction(userData: CreateUserRequest, invitedBy?: string): Promise<AuthorizedUser | null> {
    try {
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      
      if (!supabaseUrl) {
        console.error('Missing VITE_SUPABASE_URL environment variable');
        return null;
      }

      console.log('Attempting user creation via Edge Function');

      // Call Edge Function for user creation with SERVICE_ROLE_KEY
      const response = await fetch(`${supabaseUrl}/functions/v1/admin-user-operations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          action: 'create',
          userData,
          invitedBy
        }),
      });

      if (!response.ok) {
        console.error(`Edge Function HTTP error: ${response.status}: ${response.statusText}`);
        return null;
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to create user via Edge Function');
      }

      return result.user as AuthorizedUser;
    } catch (error) {
      console.error('Error calling user creation Edge Function:', error);
      // Don't re-throw here, let fallback handle it
      return null;
    }
  }

  /**
   * Fallback method: Create user using RLS (original implementation)
   */
  private static async createUserFallback(userData: CreateUserRequest, invitedBy?: string): Promise<AuthorizedUser> {
    console.log('Using fallback RLS-based user creation');
    
    // Ensure permissions is always an array
    const permissions = Array.isArray(userData.permissions) ? userData.permissions : []
    
    const { data, error } = await supabase
      .from('authorized_users')
      .insert({
        email: userData.email,
        first_name: userData.first_name,
        last_name: userData.last_name,
        department: userData.department || null,
        permissions: permissions,
        role_template: userData.role_template || null,
        notes: userData.notes || null,
        invited_by: invitedBy || null,
        is_active: true
      })
      .select()
      .single()

    if (error) {
      // Handle specific PostgreSQL error codes
      if (error.code === '23505') { // Unique constraint violation
        if (error.message.includes('authorized_users_email_key')) {
          throw new Error('A user with this email already exists')
        }
        throw new Error('This value already exists in the system')
      }
      if (error.code === '23514') { // Check constraint violation
        if (error.message.includes('valid_email')) {
          throw new Error('Invalid email format provided')
        }
        if (error.message.includes('valid_names')) {
          throw new Error('First name and last name cannot be empty')
        }
        if (error.message.includes('valid_permissions')) {
          throw new Error('Permissions must be provided as a valid array')
        }
        throw new Error('Data validation failed - please check your input')
      }
      if (error.code === '23502') { // NOT NULL constraint violation
        throw new Error('Required fields are missing - email, first name, and last name are required')
      }
      throw new Error(`Failed to create user: ${error.message}`)
    }

    return data as AuthorizedUser
  }

  /**
   * Update an existing user via Edge Function (primary) with RLS fallback
   */
  static async updateUser(userId: string, updates: UpdateUserRequest): Promise<AuthorizedUser> {
    try {
      // Primary: Try Edge Function approach (reliable, uses SERVICE_ROLE_KEY)
      const edgeFunctionResult = await this.updateUserViaEdgeFunction(userId, updates);
      if (edgeFunctionResult) {
        console.log('User updated via Edge Function (SERVICE_ROLE_KEY)');
        return edgeFunctionResult;
      }

      // Fallback: Use RLS-based update
      console.log('Edge Function failed, falling back to RLS-based update');
      return await this.updateUserFallback(userId, updates);
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  /**
   * Primary method: Update user via Edge Function using SERVICE_ROLE_KEY
   */
  private static async updateUserViaEdgeFunction(userId: string, updates: UpdateUserRequest): Promise<AuthorizedUser | null> {
    try {
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      
      if (!supabaseUrl) {
        console.error('Missing VITE_SUPABASE_URL environment variable');
        return null;
      }

      console.log('Attempting user update via Edge Function');

      // Call Edge Function for user update with SERVICE_ROLE_KEY
      const response = await fetch(`${supabaseUrl}/functions/v1/admin-user-operations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          action: 'update',
          userId,
          userData: updates
        }),
      });

      if (!response.ok) {
        console.error(`Edge Function HTTP error: ${response.status}: ${response.statusText}`);
        return null;
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to update user via Edge Function');
      }

      return result.user as AuthorizedUser;
    } catch (error) {
      console.error('Error calling user update Edge Function:', error);
      // Don't re-throw here, let fallback handle it
      return null;
    }
  }

  /**
   * Fallback method: Update user using RLS (original implementation)
   */
  private static async updateUserFallback(userId: string, updates: UpdateUserRequest): Promise<AuthorizedUser> {
    console.log('Using fallback RLS-based user update');
    
    const updateData: any = { ...updates }
    
    // Handle permissions array serialization
    if (updates.permissions !== undefined) {
      const permissions = Array.isArray(updates.permissions) ? updates.permissions : []
      updateData.permissions = permissions
    }

    const { data, error } = await supabase
      .from('authorized_users')
      .update(updateData)
      .eq('id', userId)
      .select()
      .single()

    if (error) {
      // Handle specific PostgreSQL error codes for updates
      if (error.code === '23505') { // Unique constraint violation
        if (error.message.includes('authorized_users_email_key')) {
          throw new Error('A user with this email already exists')
        }
        throw new Error('This value already exists in the system')
      }
      if (error.code === '23514') { // Check constraint violation
        if (error.message.includes('valid_email')) {
          throw new Error('Invalid email format provided')
        }
        if (error.message.includes('valid_names')) {
          throw new Error('First name and last name cannot be empty')
        }
        if (error.message.includes('valid_permissions')) {
          throw new Error('Permissions must be provided as a valid array')
        }
        throw new Error('Data validation failed - please check your input')
      }
      if (error.code === '23502') { // NOT NULL constraint violation
        throw new Error('Required fields cannot be removed - email, first name, and last name are required')
      }
      throw new Error(`Failed to update user: ${error.message}`)
    }

    return data as AuthorizedUser
  }

  /**
   * Delete a user (soft delete by deactivating)
   */
  static async deactivateUser(userId: string): Promise<void> {
    const { error } = await supabase
      .from('authorized_users')
      .update({ is_active: false })
      .eq('id', userId)

    if (error) {
      throw new Error(`Failed to deactivate user: ${error.message}`)
    }
  }

  /**
   * Reactivate a user
   */
  static async reactivateUser(userId: string): Promise<void> {
    const { error } = await supabase
      .from('authorized_users')
      .update({ is_active: true })
      .eq('id', userId)

    if (error) {
      throw new Error(`Failed to reactivate user: ${error.message}`)
    }
  }
}