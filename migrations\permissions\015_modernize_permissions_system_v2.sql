-- Migration: Modernize Permissions System V2
-- Aligns database with frontend architecture
-- Date: 2025-09-12
-- Description: Simple fix - remove blocking constraint and update permissions

-- ============================================================================
-- SIMPLE FIX: Remove blocking constraint and modernize permissions
-- ============================================================================

-- Create backup tables
CREATE TABLE IF NOT EXISTS permissions_backup AS SELECT * FROM permissions;
CREATE TABLE IF NOT EXISTS roles_backup AS SELECT * FROM roles;
CREATE TABLE IF NOT EXISTS authorized_users_backup AS SELECT * FROM authorized_users;

-- Remove the constraint that's blocking new categories
ALTER TABLE permissions DROP CONSTRAINT IF EXISTS permissions_category_valid;
ALTER TABLE permissions DROP CONSTRAINT IF EXISTS permissions_key_format_check;
ALTER TABLE permissions DROP CONSTRAINT IF EXISTS permissions_valid_category;

-- Clear existing permissions and roles
DELETE FROM permissions;
DELETE FROM roles;

-- Insert modern permission structure
INSERT INTO permissions (key, name, description, category, is_active) VALUES
-- Orders Resource
('orders.view', 'View Orders', 'Access to view order information', 'orders', true),
('orders.create', 'Create Orders', 'Create new orders', 'orders', true),
('orders.edit', 'Edit Orders', 'Modify existing orders', 'orders', true),
('orders.delete', 'Delete Orders', 'Remove orders from system', 'orders', true),

-- Products Resource
('products.view', 'View Products', 'Access to view product catalog', 'products', true),
('products.create', 'Create Products', 'Add new products', 'products', true),
('products.edit', 'Edit Products', 'Modify product information and pricing', 'products', true),
('products.delete', 'Delete Products', 'Remove products from catalog', 'products', true),

-- Clients Resource
('clients.view', 'View Clients', 'Access to view client information', 'clients', true),
('clients.create', 'Create Clients', 'Add new clients', 'clients', true),
('clients.edit', 'Edit Clients', 'Modify client information', 'clients', true),
('clients.delete', 'Delete Clients', 'Remove clients from system', 'clients', true),

-- Analytics Resource
('analytics.view', 'View Analytics', 'Access to business analytics', 'analytics', true),
('analytics.export', 'Export Analytics', 'Export analytics data', 'analytics', true),

-- Settings Resource
('settings.view', 'View Settings', 'Access to system settings', 'settings', true),
('settings.edit', 'Edit Settings', 'Modify system settings', 'settings', true),

-- Administration
('admin.users', 'Manage Users', 'Create and manage user accounts', 'admin', true),
('admin.permissions', 'Assign Permissions', 'Assign permissions to users', 'admin', true),
('system.admin', 'System Administrator', 'Full system access', 'system', true);

-- Insert modern role templates
INSERT INTO roles (name, display_name, description, permissions, is_active, is_system_role) VALUES
-- Viewer Role - Read-only access
('viewer', 'Viewer', 'Read-only access to business data',
 ARRAY['orders.view', 'products.view', 'clients.view', 'analytics.view'], true, false),

-- Operator Role - Daily operations
('operator', 'Operator', 'Daily operations - create and edit records',
 ARRAY['orders.view', 'orders.create', 'orders.edit', 'products.view', 'clients.view', 'clients.create', 'clients.edit', 'analytics.view'], true, false),

-- Manager Role - Full business operations
('manager', 'Manager', 'Management access - full business operations',
 ARRAY['orders.view', 'orders.create', 'orders.edit', 'orders.delete', 'products.view', 'products.create', 'products.edit', 'clients.view', 'clients.create', 'clients.edit', 'clients.delete', 'analytics.view', 'analytics.export', 'settings.view'], true, false),

-- Admin Role - System administration
('admin', 'Administrator', 'Full system administration',
 ARRAY['system.admin'], true, true);

-- ============================================================================
-- MIGRATE USER PERMISSIONS
-- ============================================================================

-- Create temporary mapping table for permission migration
CREATE TEMP TABLE permission_mapping AS
WITH old_to_new_permissions AS (
  SELECT 
    'pages.orders_access' as old_key, 'orders.view' as new_key
  UNION ALL SELECT 'orders.create', 'orders.create'
  UNION ALL SELECT 'orders.general_info_edit', 'orders.edit'
  UNION ALL SELECT 'orders.items_edit', 'orders.edit'
  UNION ALL SELECT 'orders.payments_manage', 'orders.edit'
  UNION ALL SELECT 'orders.delete', 'orders.delete'
  
  UNION ALL SELECT 'pages.products_access', 'products.view'
  UNION ALL SELECT 'products.pricing_edit', 'products.edit'
  UNION ALL SELECT 'products.delete', 'products.delete'
  
  UNION ALL SELECT 'pages.clients_access', 'clients.view'
  UNION ALL SELECT 'clients.create', 'clients.create'
  UNION ALL SELECT 'clients.delete', 'clients.delete'
  
  UNION ALL SELECT 'pages.analytics_overview_access', 'analytics.view'
  UNION ALL SELECT 'pages.analytics_general_access', 'analytics.view'
  UNION ALL SELECT 'pages.analytics_production_access', 'analytics.view'
  UNION ALL SELECT 'pages.analytics_sales_access', 'analytics.view'
  UNION ALL SELECT 'analytics.export', 'analytics.export'
  
  UNION ALL SELECT 'pages.settings_access', 'settings.view'
  UNION ALL SELECT 'pages.user_management_access', 'admin.users'
  UNION ALL SELECT 'admin.users_create', 'admin.users'
  UNION ALL SELECT 'admin.permissions_assign', 'admin.permissions'
  
  UNION ALL SELECT 'system.full_access', 'system.admin'
  UNION ALL SELECT 'pages.production_cost_access', 'products.view'
)
SELECT * FROM old_to_new_permissions;

-- Migrate user role templates
UPDATE authorized_users 
SET role_template = CASE 
  WHEN role_template = 'order_manager' THEN 'operator'
  WHEN role_template = 'product_manager' THEN 'operator'  
  WHEN role_template = 'supervisor' THEN 'manager'
  WHEN role_template = 'admin' THEN 'admin'
  WHEN role_template = 'viewer' THEN 'viewer'
  ELSE 'viewer' -- Default fallback
END;

-- Migrate individual user permissions (handle both text[] and jsonb)
UPDATE authorized_users 
SET permissions = COALESCE((
  SELECT CASE 
    WHEN COUNT(pm.new_key) = 0 THEN 
      CASE WHEN pg_typeof(permissions) = 'jsonb'::regtype 
           THEN '[]'::jsonb 
           ELSE ARRAY[]::text[] END
    ELSE 
      CASE WHEN pg_typeof(permissions) = 'jsonb'::regtype
           THEN to_jsonb(array_agg(DISTINCT pm.new_key))
           ELSE array_agg(DISTINCT pm.new_key) END
  END
  FROM unnest(
    CASE 
      WHEN pg_typeof(permissions) = 'jsonb'::regtype 
      THEN ARRAY(SELECT jsonb_array_elements_text(permissions))
      ELSE COALESCE(permissions, ARRAY[]::text[])
    END
  ) AS old_perm
  LEFT JOIN permission_mapping pm ON pm.old_key = old_perm
  WHERE pm.new_key IS NOT NULL
), CASE WHEN pg_typeof(permissions) = 'jsonb'::regtype 
         THEN '[]'::jsonb 
         ELSE ARRAY[]::text[] END);

-- Drop temporary mapping table
DROP TABLE permission_mapping;

-- Ensure all users have at least viewer access if they have no permissions
UPDATE authorized_users 
SET role_template = 'viewer'
WHERE (role_template IS NULL OR role_template = '') 
  AND is_active = true;

-- Update timestamps
UPDATE authorized_users SET updated_at = NOW();
UPDATE permissions SET updated_at = NOW();
UPDATE roles SET updated_at = NOW();

-- ============================================================================
-- VERIFICATION
-- ============================================================================

-- Verify migration results
SELECT 'Permissions Count' as check_type, COUNT(*) as count FROM permissions
UNION ALL
SELECT 'Roles Count', COUNT(*) FROM roles  
UNION ALL
SELECT 'Users with Roles', COUNT(*) FROM authorized_users WHERE role_template IS NOT NULL
UNION ALL
SELECT 'Active Users', COUNT(*) FROM authorized_users WHERE is_active = true;

-- ============================================================================
-- MIGRATION COMPLETE
-- ============================================================================