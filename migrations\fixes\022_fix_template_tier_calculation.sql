-- Migration 022: Fix Template-Based Tier Calculation
-- Problem: The calculation function ignores templates and their tier logic entirely
-- Solution: Update function to use template applications and apply proper tier calculations

-- Drop the old function
DROP FUNCTION IF EXISTS calculate_item_production_cost_with_breakdown(UUID, UUID, UUI<PERSON>, UUID, INTEGER, INTEGER);

-- Create new template-aware calculation function
CREATE OR REPLACE FUNCTION calculate_item_production_cost_with_breakdown(
    p_order_item_id UUID,
    p_category_id UUID,
    p_product_type_id UUID,
    p_size_id UUID,
    p_quantity INTEGER DEFAULT 1,
    p_nos INTEGER DEFAULT 1
)
RETURNS TABLE (
    base_cost NUMERIC,
    additional_cost NUMERIC,
    total_cost NUMERIC,
    calculation_breakdown JSONB,
    calculation_success BOOLEAN,
    rule_applied BOOLEAN,
    rule_type TEXT,
    error_logged BOOLEAN,
    announcement_created BOOLEAN
) 
LANGUAGE plpgsql
AS $$
DECLARE
    v_base_cost NUMERIC := 0;
    v_additional_cost NUMERIC := 0;
    v_total_cost NUMERIC := 0;
    v_template_record RECORD;
    v_component_record RECORD;
    v_component_value NUMERIC;
    
    -- Product info for breakdown
    v_product_category_name TEXT;
    v_product_type_name TEXT;
    v_size_name TEXT;
    
    -- Template tracking
    v_templates_applied INTEGER := 0;
    v_rule_applied BOOLEAN := false;
    v_rule_type TEXT := 'template_based';
    
    -- Breakdown data collection
    v_component_calculations JSONB := '[]'::JSONB;
    v_applied_templates JSONB := '[]'::JSONB;
    v_tier_distributions JSONB := '[]'::JSONB;
    v_calculation_breakdown JSONB;
    v_component_breakdown JSONB;
    v_template_breakdown JSONB;
    v_tier_breakdown JSONB;
    v_component_count INTEGER := 0;
BEGIN
    -- Get product names for breakdown display
    SELECT pa1.value, pa2.value, pa3.value 
    INTO v_product_category_name, v_product_type_name, v_size_name
    FROM product_attributes pa1, product_attributes pa2, product_attributes pa3
    WHERE pa1.id = p_category_id 
      AND pa2.id = p_product_type_id 
      AND pa3.id = p_size_id;

    -- Process applied templates for this product combination
    FOR v_template_record IN
        SELECT 
            ta.id as application_id,
            ct.id as template_id,
            ct.name as template_name,
            ct.category as template_category,
            ct.additional_cost_tier,
            ct.tier_metadata,
            ct.selected_components,
            ct.value_configuration
        FROM template_applications ta
        INNER JOIN calculation_templates ct ON ta.template_id = ct.id
        WHERE ta.product_category_id = p_category_id
          AND ta.product_type_id = p_product_type_id
          AND ta.size_id = p_size_id
          AND ct.deleted_at IS NULL
        ORDER BY ct.category DESC, ct.created_at ASC
    LOOP
        v_templates_applied := v_templates_applied + 1;
        v_rule_applied := true;
        
        -- Add template to applied templates list
        v_template_breakdown := jsonb_build_object(
            'id', v_template_record.template_id,
            'name', v_template_record.template_name,
            'type', CASE 
                WHEN v_template_record.template_category = 'basic_cost' THEN 'foundation'
                ELSE 'additional'
            END,
            'tier', v_template_record.additional_cost_tier,
            'value', 0  -- Will be calculated as we process components
        );
        
        -- Process components from this template
        FOR v_component_record IN
            SELECT 
                pcc.id as component_id,
                pcc.name as component_name,
                pcc.category as component_category,
                pcv.value as component_value
            FROM production_cost_component_values pcv
            INNER JOIN production_cost_components pcc ON pcv.component_id = pcc.id
            WHERE pcv.product_category_id = p_category_id
              AND pcv.product_type_id = p_product_type_id
              AND pcv.size_id = p_size_id
              AND (
                  -- If template has selected components, use only those
                  v_template_record.selected_components IS NULL 
                  OR 
                  v_template_record.selected_components @> jsonb_build_array(pcc.id::TEXT)
                  OR
                  jsonb_array_length(v_template_record.selected_components) = 0
              )
        LOOP
            v_component_count := v_component_count + 1;
            
            -- Apply tier-based calculation logic
            CASE v_template_record.additional_cost_tier
                WHEN 'per_unit' THEN
                    -- Per unit: multiply by quantity only
                    v_component_value := v_component_record.component_value * p_quantity;
                    
                WHEN 'per_order' THEN
                    -- Per order: fixed amount regardless of quantity
                    v_component_value := v_component_record.component_value;
                    
                WHEN 'distributed_by_qty' THEN
                    -- Distributed by quantity: divide base value by quantity
                    v_component_value := v_component_record.component_value / 
                                       GREATEST(p_quantity, 
                                               COALESCE((v_template_record.tier_metadata->>'minimum_divisor')::INTEGER, 1));
                    
                WHEN 'distributed_by_nos' THEN
                    -- Distributed by number of sheets: divide base value by nos
                    v_component_value := v_component_record.component_value / 
                                       GREATEST(p_nos, 
                                               COALESCE((v_template_record.tier_metadata->>'minimum_divisor')::INTEGER, 1));
                                               
                ELSE
                    -- Default/fallback: multiply by quantity (backward compatibility)
                    v_component_value := v_component_record.component_value * p_quantity;
            END CASE;
            
            -- Build component breakdown with tier information
            v_component_breakdown := jsonb_build_object(
                'id', gen_random_uuid(),
                'name', v_component_record.component_name,
                'base_value', v_component_record.component_value,
                'transformation', CASE v_template_record.additional_cost_tier
                    WHEN 'per_unit' THEN 'multiply_qty'
                    WHEN 'per_order' THEN 'none'
                    WHEN 'distributed_by_qty' THEN 'divide_qty'
                    WHEN 'distributed_by_nos' THEN 'divide_nos'
                    ELSE 'multiply_qty'
                END,
                'final_value', v_component_value,
                'formula', CASE v_template_record.additional_cost_tier
                    WHEN 'per_unit' THEN 
                        v_component_record.component_value::TEXT || ' × ' || p_quantity::TEXT
                    WHEN 'per_order' THEN 
                        v_component_record.component_value::TEXT || ' (fixed per order)'
                    WHEN 'distributed_by_qty' THEN 
                        v_component_record.component_value::TEXT || ' ÷ ' || p_quantity::TEXT
                    WHEN 'distributed_by_nos' THEN 
                        v_component_record.component_value::TEXT || ' ÷ ' || p_nos::TEXT
                    ELSE 
                        v_component_record.component_value::TEXT || ' × ' || p_quantity::TEXT
                END,
                'template_source', v_template_record.template_name,
                'template_id', v_template_record.template_id
            );

            -- Add to component calculations array
            v_component_calculations := v_component_calculations || v_component_breakdown;

            -- Create tier distribution entry if applicable
            IF v_template_record.additional_cost_tier IN ('distributed_by_qty', 'distributed_by_nos') THEN
                v_tier_breakdown := jsonb_build_object(
                    'component_name', v_component_record.component_name,
                    'tier_type', v_template_record.additional_cost_tier,
                    'original_value', v_component_record.component_value,
                    'distributed_value', v_component_value,
                    'distribution_factor', CASE 
                        WHEN v_template_record.additional_cost_tier = 'distributed_by_qty' THEN p_quantity
                        ELSE p_nos
                    END
                );
                v_tier_distributions := v_tier_distributions || v_tier_breakdown;
            END IF;

            -- Accumulate costs by category
            IF v_component_record.component_category IN ('materials', 'labor') THEN
                v_base_cost := v_base_cost + v_component_value;
            ELSE
                v_additional_cost := v_additional_cost + v_component_value;
            END IF;

        END LOOP;
        
        -- Update template value with calculated total for this template's components
        v_template_breakdown := jsonb_set(
            v_template_breakdown,
            '{value}',
            to_jsonb(COALESCE((
                SELECT SUM((comp->>'final_value')::NUMERIC)
                FROM jsonb_array_elements(v_component_calculations) AS comp
                WHERE comp->>'template_id' = v_template_record.template_id::TEXT
            ), 0))
        );
        
        -- Add template to applied templates list
        v_applied_templates := v_applied_templates || v_template_breakdown;

    END LOOP;

    -- If no templates were found, fall back to old component-based calculation
    IF v_templates_applied = 0 THEN
        -- Fallback: Use direct component values (old behavior)
        FOR v_component_record IN
            SELECT 
                pcv.value as component_value,
                pcc.name as component_name,
                pcc.category as component_category
            FROM production_cost_component_values pcv
            INNER JOIN production_cost_components pcc ON pcv.component_id = pcc.id
            WHERE pcv.product_category_id = p_category_id
              AND pcv.product_type_id = p_product_type_id
              AND pcv.size_id = p_size_id
        LOOP
            v_component_count := v_component_count + 1;
            v_component_value := v_component_record.component_value * p_quantity;
            
            -- Build component breakdown (fallback format)
            v_component_breakdown := jsonb_build_object(
                'id', gen_random_uuid(),
                'name', v_component_record.component_name,
                'base_value', v_component_record.component_value,
                'transformation', 'multiply_qty',
                'final_value', v_component_value,
                'formula', v_component_record.component_value::TEXT || ' × ' || p_quantity::TEXT,
                'template_source', 'Legacy Direct Components',
                'template_id', null
            );

            v_component_calculations := v_component_calculations || v_component_breakdown;

            -- Accumulate costs by category
            IF v_component_record.component_category IN ('materials', 'labor') THEN
                v_base_cost := v_base_cost + v_component_value;
            ELSE
                v_additional_cost := v_additional_cost + v_component_value;
            END IF;
        END LOOP;
        
        v_rule_type := 'fallback_direct_components';
    END IF;

    -- Calculate total cost
    v_total_cost := v_base_cost + v_additional_cost;

    -- Build final calculation breakdown with comprehensive data
    v_calculation_breakdown := jsonb_build_object(
        'calculation_timestamp', NOW(),
        'total_cost', v_total_cost,
        'base_cost', v_base_cost,
        'additional_cost', v_additional_cost,
        'rule_applied', v_rule_applied,
        'rule_type', v_rule_type,
        'applied_templates', v_applied_templates,
        'component_calculations', v_component_calculations,
        'tier_distributions', v_tier_distributions,
        'calculation_context', jsonb_build_object(
            'product_category', v_product_category_name,
            'product_type', v_product_type_name,
            'size', v_size_name,
            'quantity', p_quantity,
            'nos', p_nos
        ),
        'data_quality', jsonb_build_object(
            'component_count', v_component_count,
            'templates_applied', v_templates_applied,
            'has_production_data', v_component_count > 0,
            'calculation_complete', v_total_cost > 0,
            'uses_template_tiers', v_templates_applied > 0
        )
    );

    -- Update order_items with both production cost and breakdown
    IF p_order_item_id IS NOT NULL THEN
        UPDATE order_items 
        SET 
            production_cost = v_total_cost,
            calculation_breakdown = v_calculation_breakdown
        WHERE item_id = p_order_item_id;
    END IF;

    -- Return results
    RETURN QUERY SELECT 
        v_base_cost,
        v_additional_cost, 
        v_total_cost,
        v_calculation_breakdown,
        v_component_count > 0 as calculation_success,
        v_rule_applied,
        v_rule_type,
        false::BOOLEAN as error_logged,
        false::BOOLEAN as announcement_created;

EXCEPTION WHEN OTHERS THEN
    -- Error handling
    RAISE WARNING 'Error in calculate_item_production_cost_with_breakdown: %', SQLERRM;
    
    RETURN QUERY SELECT 
        0::NUMERIC as base_cost,
        0::NUMERIC as additional_cost,
        0::NUMERIC as total_cost,
        jsonb_build_object('error', SQLERRM) as calculation_breakdown,
        false::BOOLEAN as calculation_success,
        false::BOOLEAN as rule_applied,
        'error'::TEXT as rule_type,
        false::BOOLEAN as error_logged,
        false::BOOLEAN as announcement_created;
END;
$$;

-- Create a helper function to update template tier metadata for empty ones
CREATE OR REPLACE FUNCTION update_empty_tier_metadata()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    -- Update templates with empty tier_metadata based on their additional_cost_tier
    UPDATE calculation_templates 
    SET tier_metadata = CASE additional_cost_tier
        WHEN 'per_unit' THEN '{"scaling_factor": "quantity", "description": "Cost scales with quantity"}'::JSONB
        WHEN 'per_order' THEN '{"scaling_factor": "none", "description": "Fixed cost per order"}'::JSONB
        WHEN 'distributed_by_qty' THEN '{"minimum_divisor": 1, "distribution_context": "quantity", "description": "Cost distributed across units"}'::JSONB
        WHEN 'distributed_by_nos' THEN '{"minimum_divisor": 1, "distribution_context": "nos", "description": "Cost distributed across sheets"}'::JSONB
        ELSE tier_metadata
    END
    WHERE tier_metadata = '{}'::JSONB 
      OR tier_metadata IS NULL
      AND additional_cost_tier IS NOT NULL
      AND deleted_at IS NULL;
      
    RAISE NOTICE 'Updated tier metadata for templates with empty metadata';
END;
$$;

-- Apply the tier metadata fix
SELECT update_empty_tier_metadata();

-- Drop the helper function
DROP FUNCTION update_empty_tier_metadata();

COMMENT ON FUNCTION calculate_item_production_cost_with_breakdown IS 
'Template-aware production cost calculation function that applies proper tier logic from templates instead of ignoring them. Fixes the core issue where tier calculations (per_unit, distributed_by_qty, etc.) were not being applied.';