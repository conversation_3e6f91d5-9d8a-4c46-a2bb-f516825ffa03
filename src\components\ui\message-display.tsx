import React from 'react';
import { AlertCircle, CheckCircle, Info, AlertTriangle } from 'lucide-react';

type MessageType = 'error' | 'success' | 'info' | 'warning';

interface MessageDisplayProps {
  type: MessageType;
  title?: string;
  message: string;
  subtitle?: string;
  className?: string;
  showIcon?: boolean;
  onDismiss?: () => void;
}

/**
 * Reusable MessageDisplay component for consistent user feedback
 * Following CLAUDE.md guidelines - focused functionality under 250 lines
 */
export const MessageDisplay: React.FC<MessageDisplayProps> = ({
  type,
  title,
  message,
  subtitle,
  className = '',
  showIcon = true,
  onDismiss
}) => {
  const config = {
    error: {
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
      textColor: 'text-red-600',
      subtitleColor: 'text-red-500',
      icon: AlertCircle
    },
    success: {
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      textColor: 'text-green-600',
      subtitleColor: 'text-green-500',
      icon: CheckCircle
    },
    info: {
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      textColor: 'text-blue-600',
      subtitleColor: 'text-blue-500',
      icon: Info
    },
    warning: {
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200',
      textColor: 'text-yellow-600',
      subtitleColor: 'text-yellow-500',
      icon: AlertTriangle
    }
  };

  const { bgColor, borderColor, textColor, subtitleColor, icon: Icon } = config[type];

  return (
    <div className={`p-3 rounded-lg ${bgColor} border ${borderColor} ${className}`}>
      <div className="flex items-start gap-2">
        {showIcon && (
          <Icon className={`h-4 w-4 ${textColor} mt-0.5 flex-shrink-0`} />
        )}
        <div className="flex-1">
          {title && (
            <p className={`${textColor} text-sm font-medium mb-1`}>{title}</p>
          )}
          <p className={`${textColor} text-sm mb-0`}>{message}</p>
          {subtitle && (
            <p className={`${subtitleColor} text-xs mt-1 mb-0`}>{subtitle}</p>
          )}
        </div>
        {onDismiss && (
          <button
            onClick={onDismiss}
            className={`${textColor} hover:opacity-70 ml-2`}
            aria-label="Dismiss message"
          >
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>
    </div>
  );
};

/**
 * Specialized ErrorMessage component
 */
interface ErrorMessageProps {
  message: string;
  subtitle?: string;
  className?: string;
  showIcon?: boolean;
  onDismiss?: () => void;
}

export const ErrorMessage: React.FC<ErrorMessageProps> = (props) => (
  <MessageDisplay type="error" {...props} />
);

/**
 * Specialized SuccessMessage component
 */
interface SuccessMessageProps {
  message: string;
  subtitle?: string;
  className?: string;
  showIcon?: boolean;
  onDismiss?: () => void;
}

export const SuccessMessage: React.FC<SuccessMessageProps> = (props) => (
  <MessageDisplay type="success" {...props} />
);

/**
 * Specialized InfoMessage component
 */
interface InfoMessageProps {
  message: string;
  subtitle?: string;
  className?: string;
  showIcon?: boolean;
  onDismiss?: () => void;
}

export const InfoMessage: React.FC<InfoMessageProps> = (props) => (
  <MessageDisplay type="info" {...props} />
);

/**
 * Specialized WarningMessage component
 */
interface WarningMessageProps {
  message: string;
  subtitle?: string;
  className?: string;
  showIcon?: boolean;
  onDismiss?: () => void;
}

export const WarningMessage: React.FC<WarningMessageProps> = (props) => (
  <MessageDisplay type="warning" {...props} />
);