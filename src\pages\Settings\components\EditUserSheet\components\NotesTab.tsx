import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../../../components/ui/card'
import { Textarea } from '../../../../../components/ui/textarea'
import { Label } from '../../../../../components/ui/label'
import { Badge } from '../../../../../components/ui/badge'
import type { EditUserFormData } from '../types'

interface NotesTabProps {
  formData: EditUserFormData
  onInputChange: (field: string, value: string) => void
}

export function NotesTab({ formData, onInputChange }: NotesTabProps) {
  const notesLength = formData.notes?.length || 0
  const maxLength = 1000

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Additional Information</CardTitle>
        <CardDescription>
          Add any additional notes or comments about this user account.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="notes">Notes</Label>
            <Badge variant={notesLength > maxLength * 0.8 ? "destructive" : "secondary"}>
              {notesLength}/{maxLength}
            </Badge>
          </div>
          <Textarea
            id="notes"
            placeholder="Enter any additional notes about this user..."
            value={formData.notes}
            onChange={(e) => onInputChange('notes', e.target.value)}
            rows={6}
            maxLength={maxLength}
            className="resize-none"
          />
          <p className="text-sm text-muted-foreground">
            These notes are visible to administrators and can include information about the user's role, 
            responsibilities, or any special considerations.
          </p>
        </div>
      </CardContent>
    </Card>
  )
}