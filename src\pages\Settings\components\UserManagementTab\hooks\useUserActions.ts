import { useState } from 'react'
import { AdminService } from '../../../../../services/admin'
import { useToast } from '../../../../../hooks/use-toast'
import type { AuthorizedUser } from '../../../../../services/admin'

export function useUserActions(onRefresh?: () => Promise<void>) {
  const { toast } = useToast()
  const [processingUserId, setProcessingUserId] = useState<string | null>(null)

  const handleToggleUserStatus = async (user: AuthorizedUser) => {
    try {
      setProcessingUserId(user.id)
      
      await AdminService.updateUser(user.id, { 
        is_active: !user.is_active 
      })

      toast({
        title: user.is_active ? 'User Deactivated' : 'User Activated',
        description: `${user.first_name} ${user.last_name} has been ${user.is_active ? 'deactivated' : 'activated'}`,
      })

      // Refresh data from parent
      if (onRefresh) {
        await onRefresh()
      }
    } catch (error) {
      console.error('Failed to toggle user status:', error)
      toast({
        title: 'Update Failed',
        description: error instanceof Error ? error.message : 'Failed to update user status',
        variant: 'destructive'
      })
    } finally {
      setProcessingUserId(null)
    }
  }

  const handlePermissionUpdate = async (userId: string, permissions: string[]) => {
    try {
      setProcessingUserId(userId)
      
      await AdminService.updateUser(userId, { permissions })

      toast({
        title: 'Permissions Updated',
        description: 'User permissions have been updated successfully',
      })

      // Refresh data from parent
      if (onRefresh) {
        await onRefresh()
      }
    } catch (error) {
      console.error('Failed to update user permissions:', error)
      toast({
        title: 'Update Failed',
        description: error instanceof Error ? error.message : 'Failed to update user permissions',
        variant: 'destructive'
      })
      throw error // Re-throw so QuickPermissionsSelect can handle the error display
    } finally {
      setProcessingUserId(null)
    }
  }

  return {
    processingUserId,
    handleToggleUserStatus,
    handlePermissionUpdate
  }
}