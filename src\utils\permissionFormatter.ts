/**
 * Permission formatting utilities
 * Formats permission strings for better display in UI
 */

/**
 * Comprehensive mapping of permission codes to human-readable labels
 */
export const PERMISSION_LABELS: Record<string, string> = {
  // Page Access Permissions (Full Control)
  'pages.orders_access': 'Full Orders Management',
  'pages.products_access': 'Full Products Management',
  'pages.clients_access': 'Full Clients Management',
  'pages.analytics_overview_access': 'Full Analytics Overview',
  'pages.analytics_sales_access': 'Full Sales Analytics',
  'pages.analytics_production_access': 'Full Production Analytics',
  'pages.settings_access': 'Full Settings Access',
  'pages.production_cost_access': 'Full Production Cost Management',
  'pages.user_management_access': 'Full User Management',
  
  // Order Management Permissions
  'orders.create': 'Create Orders',
  'orders.edit': 'Edit Orders',
  'orders.delete': 'Delete Orders',
  'orders.general_info_edit': 'Edit Order Details',
  'orders.items_edit': 'Edit Order Items',
  'orders.status_update': 'Update Order Status',
  'orders.notes_edit': 'Edit Order Notes',
  
  // Product Management Permissions
  'products.create': 'Create Products',
  'products.edit': 'Edit Products',
  'products.delete': 'Delete Products',
  'products.pricing_edit': 'Edit Product Pricing',
  'products.view_costs': 'View Product Costs',
  
  // Client Management Permissions
  'clients.create': 'Create Clients',
  'clients.edit': 'Edit Clients',
  'clients.delete': 'Delete Clients',
  'clients.view_analytics': 'View Client Analytics',
  
  // Analytics Permissions
  'analytics.export': 'Export Analytics Data',
  'analytics.advanced_metrics': 'View Advanced Metrics',
  'analytics.custom_reports': 'Create Custom Reports',
  
  // System Administration Permissions
  'admin.user_management': 'Manage Users',
  'admin.system_settings': 'Manage System Settings',
  'admin.backup_restore': 'Backup & Restore Data',
  'admin.audit_logs': 'View Audit Logs',
  
  // Production Cost Permissions
  'production_costs.view': 'View Production Costs',
  'production_costs.edit': 'Edit Production Costs',
  'production_costs.templates': 'Manage Cost Templates',
  
  // View-Only Override Permissions (Restrict full page access to view only)
  'orders.view_only': 'View Orders Only',
  'products.view_only': 'View Products Only',  
  'clients.view_only': 'View Clients Only',
  'analytics.view_only': 'View Analytics Only',
}

/**
 * Format a permission string to human-readable label
 * @param permission - The permission string (e.g., "pages.orders_access", "orders.create")
 * @returns Human-readable permission label
 */
export function formatPermissionLabel(permission: string): string {
  // Check if we have a predefined label
  if (PERMISSION_LABELS[permission]) {
    return PERMISSION_LABELS[permission]
  }
  
  // Fallback: Split by dot and format the last part
  const parts = permission.split('.')
  const lastPart = parts[parts.length - 1]
  
  // Replace underscores with spaces and capitalize
  return lastPart
    .replace(/_/g, ' ')
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

/**
 * Get badge color classes based on permission type
 * @param permission - The permission string
 * @returns Tailwind CSS classes for styling the badge
 */
export function getPermissionBadgeStyle(permission: string): string {
  if (permission.startsWith('pages.')) {
    return 'bg-green-50 text-green-700 border-green-200'
  }
  
  if (permission.includes('.create') || permission.includes('.edit')) {
    return 'bg-blue-50 text-blue-700 border-blue-200'
  }
  
  if (permission.includes('.delete') || permission.includes('.remove')) {
    return 'bg-red-50 text-red-700 border-red-200'
  }
  
  if (permission.includes('.export') || permission.includes('.analytics')) {
    return 'bg-purple-50 text-purple-700 border-purple-200'
  }
  
  // Default style
  return 'bg-gray-50 text-gray-700 border-gray-200'
}

/**
 * Get permission category for grouping
 * @param permission - The permission string
 * @returns Permission category
 */
export function getPermissionCategory(permission: string): string {
  const parts = permission.split('.')
  return parts[0] || 'other'
}

/**
 * Get permission type icon
 * @param permission - The permission string  
 * @returns Icon name or null
 */
export function getPermissionIcon(permission: string): string | null {
  if (permission.startsWith('pages.')) return '👁️'
  if (permission.includes('.create')) return '➕'
  if (permission.includes('.edit')) return '✏️'
  if (permission.includes('.delete')) return '🗑️'
  if (permission.includes('.export')) return '📊'
  return null
}