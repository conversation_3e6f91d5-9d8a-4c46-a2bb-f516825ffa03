/**
 * Password validation utilities
 * Implements strong password requirements for admin users
 * Following CLAUDE.md guidelines - focused, under 250 lines
 */

export interface PasswordRequirement {
  id: string;
  label: string;
  test: (password: string) => boolean;
  required: boolean;
}

export interface PasswordStrength {
  score: number; // 0-100
  level: 'very-weak' | 'weak' | 'fair' | 'good' | 'strong';
  feedback: string[];
  requirements: PasswordRequirementResult[];
}

export interface PasswordRequirementResult extends PasswordRequirement {
  passed: boolean;
}

/**
 * Password requirements configuration
 * Based on OWASP guidelines for business applications
 */
export const PASSWORD_REQUIREMENTS: PasswordRequirement[] = [
  {
    id: 'length',
    label: 'At least 8 characters long',
    test: (password: string) => password.length >= 8,
    required: true
  },
  {
    id: 'uppercase',
    label: 'Contains at least one uppercase letter (A-Z)',
    test: (password: string) => /[A-Z]/.test(password),
    required: true
  },
  {
    id: 'lowercase',
    label: 'Contains at least one lowercase letter (a-z)',
    test: (password: string) => /[a-z]/.test(password),
    required: true
  },
  {
    id: 'number',
    label: 'Contains at least one number (0-9)',
    test: (password: string) => /[0-9]/.test(password),
    required: true
  },
  {
    id: 'special',
    label: 'Contains at least one special character (!@#$%^&*)',
    test: (password: string) => /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~`]/.test(password),
    required: true
  },
  {
    id: 'no-spaces',
    label: 'Contains no spaces',
    test: (password: string) => !/\s/.test(password),
    required: true
  },
  {
    id: 'no-common',
    label: 'Not a commonly used password',
    test: (password: string) => !isCommonPassword(password),
    required: true
  }
];

/**
 * Common passwords to reject (basic list)
 * In production, this would be a much larger list or API call
 */
const COMMON_PASSWORDS = [
  'password', 'password123', '123456', '123456789', 'qwerty',
  'abc123', 'password1', '12345678', '111111', '123123',
  'admin', 'administrator', 'root', 'user', 'guest',
  'welcome', 'hello', 'test', 'temp', 'temporary'
];

/**
 * Check if password is in common passwords list
 */
function isCommonPassword(password: string): boolean {
  return COMMON_PASSWORDS.includes(password.toLowerCase());
}

/**
 * Validate password against requirements
 */
export function validatePassword(password: string): PasswordStrength {
  const requirements = PASSWORD_REQUIREMENTS.map(req => ({
    ...req,
    passed: req.test(password)
  }));

  const passedRequired = requirements.filter(req => req.required && req.passed).length;
  const totalRequired = requirements.filter(req => req.required).length;
  
  // Calculate score based on requirements and additional factors
  let score = (passedRequired / totalRequired) * 80; // Base score from requirements
  
  // Bonus points for length
  if (password.length >= 12) score += 10;
  else if (password.length >= 10) score += 5;
  
  // Bonus for character diversity
  const uniqueChars = new Set(password).size;
  if (uniqueChars >= password.length * 0.7) score += 10;
  
  // Cap at 100
  score = Math.min(100, Math.round(score));
  
  // Determine strength level
  let level: PasswordStrength['level'];
  if (score >= 90) level = 'strong';
  else if (score >= 70) level = 'good';
  else if (score >= 50) level = 'fair';
  else if (score >= 30) level = 'weak';
  else level = 'very-weak';
  
  // Generate feedback
  const feedback: string[] = [];
  const failedRequired = requirements.filter(req => req.required && !req.passed);
  
  if (failedRequired.length > 0) {
    feedback.push(`Missing: ${failedRequired.map(req => req.label.toLowerCase()).join(', ')}`);
  }
  
  if (password.length < 8) {
    feedback.push('Password is too short');
  } else if (password.length >= 12) {
    feedback.push('Good length');
  }
  
  if (score >= 80) {
    feedback.push('Strong password!');
  } else if (score >= 60) {
    feedback.push('Good password, could be stronger');
  }
  
  return {
    score,
    level,
    feedback,
    requirements
  };
}

/**
 * Check if password meets minimum requirements for admin accounts
 */
export function isPasswordValid(password: string): boolean {
  const result = validatePassword(password);
  return result.requirements.filter(req => req.required).every(req => req.passed);
}

/**
 * Get password strength color for UI
 */
export function getPasswordStrengthColor(level: PasswordStrength['level']): string {
  switch (level) {
    case 'very-weak': return 'text-red-600';
    case 'weak': return 'text-red-500';
    case 'fair': return 'text-yellow-500';
    case 'good': return 'text-blue-500';
    case 'strong': return 'text-green-600';
    default: return 'text-gray-400';
  }
}

/**
 * Get password strength progress color for UI
 */
export function getPasswordStrengthProgressColor(level: PasswordStrength['level']): string {
  switch (level) {
    case 'very-weak': return 'bg-red-600';
    case 'weak': return 'bg-red-500';
    case 'fair': return 'bg-yellow-500';
    case 'good': return 'bg-blue-500';
    case 'strong': return 'bg-green-600';
    default: return 'bg-gray-300';
  }
}