// Barrel export file for orders hooks
export { useOrdersBasic, useOrdersMetrics } from './useOrdersBasic'
export { useOrdersProgressive } from './useOrdersProgressive'
export { useOrdersMutations } from './useOrdersMutations'
export { useOrderMutations } from './useOrdersMutationsSWR' // Modern SWR mutation patterns
export { useOrderById } from './useOrderById'
export { useYearOrders } from './useYearOrders'

// Legacy exports for backward compatibility
export { useOrdersBasic as useOrdersSWR } from './useOrdersBasic'
export { useOrderById as useOrderByIdSWR } from './useOrderById'
export { useYearOrders as useYearOrdersSWR } from './useYearOrders'
export { useOrdersMetrics as useOrdersMetricsSWR } from './useOrdersBasic'

// Legacy mutation functions - these now need to be used within components that call the hooks
// Note: These are now deprecated - use the hooks directly in components
export const getLegacyMutations = () => {
  console.warn('Legacy mutation functions are deprecated. Use useOrderMutations() hook directly in components.')
  return {
    addOrderSWR: () => { throw new Error('Use useOrderMutations() hook in component') },
    updateOrderSWR: () => { throw new Error('Use useOrderMutations() hook in component') },
    deleteOrderSWR: () => { throw new Error('Use useOrderMutations() hook in component') }
  }
}

// Export key generators
export { getOrdersKey, getMetricsKey } from './useOrdersBasic'
export { getOrdersEssentialKey } from './useOrdersProgressive'
export { getOrderByIdKey } from './useOrderById'
export { getYearOrdersKey } from './useYearOrders'