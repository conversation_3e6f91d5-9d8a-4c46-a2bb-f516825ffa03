# Attribute System Complete Audit

## Summary
Total files using attribute systems: 40+ files
- Zustand (useAttributesStore): 24 files
- SWR hooks (useAttributesSWR): 10 files  
- Unified system (useUnifiedAttributes): 4 files
- AttributesContext (useAttributes): 7 files

## File Categories

### 1. CRITICAL - Order Processing Components (High Priority)
These directly affect order creation and editing:

#### Using Zustand:
- `src/components/orders/OrderItemEditDialog.tsx` - Order item editing dialog
- `src/components/orders/OrderItemForm.tsx` - Order item form component
- `src/components/orders/edit-forms/OrderDetailsEditForm.tsx` - Order details editing
- `src/components/orders/edit-forms/hooks/useOrderDetailsForm.ts` - Order details form logic
- `src/components/orders/edit-forms/hooks/useOrderItemForm.ts` - Order item form logic (DEPRECATED but may still be used)
- `src/components/orders/tabs/GeneralInfoTab.tsx` - Order general info tab

#### Using SWR:
- `src/components/orders/add-order-panels/sections/ItemsSection.tsx` - ✅ MIGRATED
- `src/components/orders/add-order-panels/sections/BasicInfoSection.tsx` - ✅ MIGRATED

#### Using Unified:
- `src/components/orders/edit-forms/hooks/useAttributeLoading.ts` - Attribute loading for forms

### 2. CRITICAL - Product Selection Components (High Priority)
These affect product selection in orders:

#### Using Zustand:
- `src/components/ui/ProductCombinationSelector/ProductCombinationSelector.tsx` - Product selection widget
- `src/components/ui/CategorySelectorDialog/index.tsx` - Category selection dialog
- `src/components/ui/category-selector-input.tsx` - Category input component

### 3. CRITICAL - Production Cost Components (High Priority)
These affect production cost calculations:

#### Using Zustand:
- `src/pages/ProductionCost/components/ProductCostTab/IntelligentProductMatrix.tsx`
- `src/pages/ProductionCost/components/ProductCostTab/ProductMatrix.tsx`
- `src/pages/ProductionCost/components/ProductCostTab/ProductionCostTemplateSheet.tsx`
- `src/pages/ProductionCost/hooks/useTemplateSheetData.ts`

#### Using SWR:
- `src/pages/ProductionCost/components/CalculationRulesTab/ProductionCostRuleForm.tsx`
- `src/pages/ProductionCost/hooks/useTemplateComponents.ts`

### 4. MEDIUM - Product Management Components
These affect product catalog management:

#### Using Zustand:
- `src/pages/Products/components/AttributesTab/AttributeForm.tsx` - Attribute form
- `src/pages/Products/components/AttributesTab/AttributesList.tsx` - Attributes list
- `src/pages/Products/components/AttributesTab/index.tsx` - Attributes tab main
- `src/pages/Products/components/ProductFilters.tsx` - Product filters

#### Using AttributesContext:
- `src/pages/Products/components/ProductForm.tsx` - Product form
- `src/pages/Products/components/CalculationRulesTab/CalculationRuleForm.tsx`
- `src/pages/Products/components/CalculationRulesTab/ProductCategoryRuleForm.tsx`

### 5. MEDIUM - Order Filtering Components
These affect order viewing/filtering:

#### Using Zustand:
- `src/pages/Orders/components/ProductFilterDialog.tsx` - Product filter dialog
- `src/pages/Orders/components/SizeFilterDialog.tsx` - Size filter dialog

### 6. LOW - Display/Badge Components
These are mainly for display:

#### Using Zustand:
- `src/components/badges/AttributeBadge.tsx` - Attribute display badge

#### Using AttributesContext:
- `src/components/products/ProductCalculator.tsx` - Product calculator

### 7. INFRASTRUCTURE - System Components
Core infrastructure files:

#### Using Zustand:
- `src/App.tsx` - ✅ MIGRATED (removed initialization)
- `src/stores/attributesStore.ts` - The Zustand store itself

#### Using SWR:
- `src/hooks/useAttributesSWR.ts` - SWR hooks
- `src/components/RealtimeSubscriptions.tsx` - Real-time subscriptions
- `src/utils/cacheUtils.ts` - Cache utilities
- `src/utils/preloadUtils.ts` - Preload utilities

#### Using Unified:
- `src/hooks/useUnifiedAttributes.ts` - Unified abstraction
- `src/contexts/AttributesContext.tsx` - React context

### 8. TEST/DEMO - Test Files (Can be ignored)
- `src/__tests__/OrderItemEditForm.test.tsx`
- `src/pages/AttributeTestPage.tsx`
- `src/pages/SWRTestPage.tsx`  
- `src/pages/ZustandTestPage.tsx`

## Migration Order Recommendation

### Phase 1: Critical Order Components (Prevent data inconsistency)
1. ProductCombinationSelector - Used in order forms
2. OrderItemEditDialog & OrderItemForm - Direct order editing
3. OrderDetailsEditForm & hooks - Order details editing
4. CategorySelectorDialog - Category selection in orders

### Phase 2: Production Cost Components (Prevent calculation errors)
1. ProductMatrix components - Cost calculations
2. ProductionCostTemplateSheet - Template management
3. useTemplateSheetData - Template data hook

### Phase 3: Infrastructure Cleanup
1. Remove useUnifiedAttributes - Unnecessary abstraction
2. Simplify AttributesContext - Use SWR directly
3. Update RealtimeSubscriptions - Single system updates

### Phase 4: Product Management
1. Products AttributesTab components
2. ProductForm and related components
3. ProductFilters

### Phase 5: Low Priority & Cleanup
1. Badge components
2. Filter dialogs
3. Delete attributesStore.ts
4. Remove all test pages

## Key Insights

1. **Order forms are split**: Some use Zustand (older), some use SWR (newer)
2. **Production cost heavily uses Zustand**: Critical for calculations
3. **Real-time only updates SWR**: Major source of inconsistency
4. **Many files import but don't heavily use**: May be easier to migrate than expected
5. **Test files can be ignored**: 4 test/demo files don't affect production

## Next Steps
1. Start with ProductCombinationSelector - it's used everywhere
2. Then tackle order editing components systematically
3. Test each component after migration
4. Only delete attributesStore.ts after ALL migrations complete