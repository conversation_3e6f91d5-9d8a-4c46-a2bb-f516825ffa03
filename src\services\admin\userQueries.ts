import { supabase } from '../../lib/supabase'
import type { AuthorizedUser, UserFilters, UserStats } from './types'

export class UserQueries {
  /**
   * Get all authorized users with filtering and pagination
   */
  static async getUsers(filters: UserFilters = {}): Promise<{ users: AuthorizedUser[]; totalCount: number }> {
    const { 
      limit = 50, 
      offset = 0, 
      search, 
      role_template, 
      is_active, 
      department 
    } = filters

    let query = supabase
      .from('authorized_users')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false })

    // Apply filters
    if (search) {
      query = query.or(`email.ilike.%${search}%,first_name.ilike.%${search}%,last_name.ilike.%${search}%`)
    }
    
    if (role_template) {
      query = query.eq('role_template', role_template)
    }
    
    if (typeof is_active === 'boolean') {
      query = query.eq('is_active', is_active)
    }
    
    if (department) {
      query = query.eq('department', department)
    }

    // Apply pagination
    if (limit && offset !== undefined) {
      query = query.range(offset, offset + limit - 1)
    }

    const { data, error, count } = await query

    if (error) {
      throw new Error(`Failed to fetch users: ${error.message}`)
    }

    return {
      users: (data || []) as AuthorizedUser[],
      totalCount: count || 0
    }
  }

  /**
   * Get user statistics for dashboard
   */
  static async getUserStats(): Promise<UserStats> {
    const [totalResult, activeResult, recentResult] = await Promise.all([
      supabase
        .from('authorized_users')
        .select('id', { count: 'exact', head: true }),
      
      supabase
        .from('authorized_users')
        .select('id', { count: 'exact', head: true })
        .eq('is_active', true),
      
      supabase
        .from('authorized_users')
        .select('id', { count: 'exact', head: true })
        .gte('last_login_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
    ])

    if (totalResult.error || activeResult.error || recentResult.error) {
      throw new Error('Failed to fetch user statistics')
    }

    const total = totalResult.count || 0
    const active = activeResult.count || 0

    return {
      total_users: total,
      active_users: active,
      inactive_users: total - active,
      recent_logins: recentResult.count || 0
    }
  }
}