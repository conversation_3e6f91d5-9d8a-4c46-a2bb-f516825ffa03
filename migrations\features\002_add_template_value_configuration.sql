-- Migration: Add Template Value Configuration
-- Adds value configuration support to templates for effective batch operations

-- Step 1: Add value_configuration column to calculation_templates
ALTER TABLE calculation_templates 
ADD COLUMN value_configuration JSONB;

-- Step 2: Add comments for documentation
COMMENT ON COLUMN calculation_templates.value_configuration IS 
'JSON structure containing default values, ranges, and batch operation settings for template components.
Structure: {
  "defaultValues": {"labor": 15.00, "materials": 2.50},
  "valueRanges": {"labor": {"min": 12, "max": 20, "recommended": 15, "reason": "Market rate"}},
  "autoUpdateProducts": true,
  "valueOverridePolicy": "template_priority",
  "lastUpdated": "2024-01-01T00:00:00Z"
}';

-- Step 3: Create indexes for efficient queries
CREATE INDEX idx_calculation_templates_value_config 
ON calculation_templates USING GIN (value_configuration);

-- Step 4: Add template_id to production_cost_component_values for tracking
-- (This enables batch updates when template values change)
ALTER TABLE production_cost_component_values 
ADD COLUMN template_id UUID REFERENCES calculation_templates(id);

CREATE INDEX idx_production_cost_component_values_template_id 
ON production_cost_component_values(template_id);

COMMENT ON COLUMN production_cost_component_values.template_id IS 
'References the template used to create this cost component. 
Enables batch updates when template values are modified.';

-- Step 5: Create function to validate value configuration structure
CREATE OR REPLACE FUNCTION validate_template_value_configuration(config JSONB)
RETURNS BOOLEAN AS $$
BEGIN
  -- Must have defaultValues
  IF config IS NULL OR NOT (config ? 'defaultValues') THEN
    RETURN FALSE;
  END IF;
  
  -- defaultValues must be an object with numeric values
  IF jsonb_typeof(config->'defaultValues') != 'object' THEN
    RETURN FALSE;
  END IF;
  
  -- autoUpdateProducts must be boolean if present
  IF config ? 'autoUpdateProducts' AND jsonb_typeof(config->'autoUpdateProducts') != 'boolean' THEN
    RETURN FALSE;
  END IF;
  
  -- valueOverridePolicy must be valid enum if present
  IF config ? 'valueOverridePolicy' AND 
     NOT (config->>'valueOverridePolicy' IN ('template_priority', 'product_priority', 'hybrid')) THEN
    RETURN FALSE;
  END IF;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Step 6: Add check constraint for value configuration validation
ALTER TABLE calculation_templates 
ADD CONSTRAINT check_valid_value_configuration 
CHECK (value_configuration IS NULL OR validate_template_value_configuration(value_configuration));

-- Step 7: Create function for batch template value updates
CREATE OR REPLACE FUNCTION update_template_values_batch(
  p_template_id UUID,
  p_value_updates JSONB,
  p_override_policy TEXT DEFAULT 'template_priority'
)
RETURNS TABLE (
  updated_products INT,
  failed_updates INT,
  update_details JSONB
) AS $$
DECLARE
  updated_count INT := 0;
  failed_count INT := 0;
  details JSONB := '[]'::jsonb;
  product_record RECORD;
  component_record RECORD;
  should_update BOOLEAN;
  old_value NUMERIC;
  new_value NUMERIC;
BEGIN
  -- Loop through all products using this template
  FOR product_record IN 
    SELECT DISTINCT product_category_id, product_type_id, size_id
    FROM production_cost_component_values 
    WHERE template_id = p_template_id AND is_current = true
  LOOP
    BEGIN
      -- Loop through components that need updating
      FOR component_record IN 
        SELECT id, component_code, value
        FROM production_cost_component_values
        WHERE template_id = p_template_id 
          AND product_category_id = product_record.product_category_id
          AND product_type_id = product_record.product_type_id
          AND size_id = product_record.size_id
          AND is_current = true
          AND component_code = ANY(SELECT jsonb_object_keys(p_value_updates))
      LOOP
        old_value := component_record.value;
        new_value := (p_value_updates->>component_record.component_code)::numeric;
        should_update := false;
        
        -- Apply override policy
        CASE p_override_policy
          WHEN 'template_priority' THEN
            should_update := true;
          WHEN 'product_priority' THEN
            -- Only update if value hasn't been customized (still matches old template default)
            should_update := old_value = new_value; -- This would need more complex logic in real implementation
          WHEN 'hybrid' THEN
            -- Update if change is significant (>10%)
            should_update := ABS(new_value - old_value) / old_value > 0.1;
          ELSE
            should_update := true;
        END CASE;
        
        IF should_update THEN
          UPDATE production_cost_component_values 
          SET value = new_value, updated_at = NOW()
          WHERE id = component_record.id;
        END IF;
      END LOOP;
      
      updated_count := updated_count + 1;
      
    EXCEPTION WHEN OTHERS THEN
      failed_count := failed_count + 1;
      details := details || jsonb_build_object(
        'product', product_record.product_category_id || '_' || product_record.product_type_id || '_' || product_record.size_id,
        'error', SQLERRM
      );
    END;
  END LOOP;
  
  RETURN QUERY SELECT updated_count, failed_count, details;
END;
$$ LANGUAGE plpgsql;

-- Step 8: Create view for template usage analytics
CREATE OR REPLACE VIEW template_usage_analytics AS
SELECT 
  ct.id as template_id,
  ct.name as template_name,
  ct.category as template_category,
  ct.value_configuration->>'autoUpdateProducts' as auto_update_enabled,
  ct.value_configuration->>'valueOverridePolicy' as override_policy,
  COUNT(DISTINCT pcv.product_category_id || '_' || pcv.product_type_id || '_' || pcv.size_id) as products_using_template,
  COUNT(pcv.id) as total_cost_components,
  SUM(pcv.value) as total_cost_value,
  AVG(pcv.value) as average_component_cost,
  ct.value_configuration->'lastUpdated' as last_value_update,
  ct.updated_at as template_last_modified
FROM calculation_templates ct
LEFT JOIN production_cost_component_values pcv ON pcv.template_id = ct.id AND pcv.is_current = true
WHERE ct.status = 'active'
GROUP BY ct.id, ct.name, ct.category, ct.value_configuration, ct.updated_at;

COMMENT ON VIEW template_usage_analytics IS 
'Analytics view showing template usage, affected products, and batch operation potential.
Use this to understand which templates are most critical for batch operations.';

-- Step 9: Add helpful indexes for batch operations
CREATE INDEX idx_production_cost_component_values_template_product 
ON production_cost_component_values(template_id, product_category_id, product_type_id, size_id)
WHERE is_current = true;

-- Step 10: Insert example template with value configuration
-- (This shows the expected structure)
INSERT INTO calculation_templates (
  id,
  name,
  description,
  category,
  calculation_method,
  selected_components,
  component_weights,
  formula,
  formula_builder,
  value_configuration,
  status,
  version,
  created_at,
  updated_at
) VALUES (
  gen_random_uuid(),
  'Standard Photo Board Basic Cost',
  'Standard basic cost template for photo boards with market-rate defaults',
  'basic_cost',
  'simple_sum',
  ARRAY['labor', 'materials'],
  '{"labor": 1, "materials": 1}'::jsonb,
  'labor + materials',
  '[
    {"type": "component", "name": "labor"},
    {"type": "operation", "symbol": "+"},
    {"type": "component", "name": "materials"}
  ]'::jsonb,
  '{
    "defaultValues": {
      "labor": 15.00,
      "materials": 2.50
    },
    "valueRanges": {
      "labor": {
        "min": 12.00,
        "max": 20.00,
        "recommended": 15.00,
        "reason": "Market rate for skilled photo editing and processing"
      },
      "materials": {
        "min": 1.50,
        "max": 4.00,
        "recommended": 2.50,
        "reason": "Quality photo paper, inks, and protective materials"
      }
    },
    "autoUpdateProducts": true,
    "valueOverridePolicy": "template_priority",
    "lastUpdated": null,
    "updateReason": "Initial template creation",
    "updatedBy": "system"
  }'::jsonb,
  'active',
  1,
  NOW(),
  NOW()
) ON CONFLICT DO NOTHING;

-- Migration complete message
DO $$
BEGIN
  RAISE NOTICE 'Template value configuration migration completed successfully!';
  RAISE NOTICE 'Templates can now include default values for effective batch operations.';
  RAISE NOTICE 'Use the template_usage_analytics view to monitor template effectiveness.';
END
$$;