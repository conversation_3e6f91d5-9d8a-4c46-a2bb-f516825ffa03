import useSWR from 'swr'
import { fetchOrders } from '../../services'
import type { Order } from '../../pages/Orders/types'
import { getOrdersKey } from './useOrdersBasic'

// Key generator for single order
export const getOrderByIdKey = (id: string) => id ? `/orders/${id}` : null

/**
 * Hook for fetching a single order by ID
 * Uses the main orders cache for efficiency
 */
export function useOrderById(id: string) {
  const { data: allOrders, error, isLoading } = useSWR(
    getOrdersKey(),
    async () => {
      const data = await fetchOrders()
      return data
    }
  )

  // Find the specific order from all orders
  const order = allOrders?.find(order => order.id === id) || null

  return {
    order,
    isLoading,
    isError: !!error,
    error
  }
}