-- ROLLBACK: Restore Original Calculation Methods
-- Date: 2025-05-30
-- Purpose: Restore 'weighted' and 'custom_formula' calculation methods from backup

-- =============================================================================
-- ROLLBACK PROCEDURE
-- =============================================================================

-- Verify backup table exists
SELECT 
    'BACKUP CHECK' as status,
    COUNT(*) as backup_count
FROM calculation_templates_backup_20250530;

-- Restore original calculation methods and descriptions
UPDATE calculation_templates 
SET 
    calculation_method = backup.calculation_method,
    description = backup.description,
    updated_at = NOW()
FROM calculation_templates_backup_20250530 backup
WHERE calculation_templates.id = backup.id;

-- =============================================================================
-- VERIFICATION AFTER ROLLBACK
-- =============================================================================

-- Verify rollback results
SELECT 
    'AFTER ROLLBACK' as status,
    calculation_method,
    COUNT(*) as count,
    string_agg(name, ', ') as template_names
FROM calculation_templates 
GROUP BY calculation_method
ORDER BY calculation_method;

-- Show detailed info for restored templates
SELECT 
    id,
    name,
    calculation_method,
    description,
    updated_at
FROM calculation_templates 
WHERE id IN (
    'e2ae6f0c-7dfa-45f6-a397-6c48e429fed9',  -- Basic rule test 2
    'e63b0a5d-7869-42ed-a699-cf9f3a24ad26'   -- Basic rule test 3
);

-- =============================================================================
-- CLEANUP AFTER SUCCESSFUL ROLLBACK
-- =============================================================================

-- Drop backup table after successful rollback verification
-- DROP TABLE IF EXISTS calculation_templates_backup_20250530;