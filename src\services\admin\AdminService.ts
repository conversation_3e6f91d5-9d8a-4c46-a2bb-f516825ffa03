import { UserQueries } from './userQueries'
import { UserMutations } from './userMutations'
import { PermissionQueries } from './permissionQueries'
import type { 
  AuthorizedUser, 
  CreateUserRequest, 
  UpdateUserRequest, 
  UserStats, 
  UserFilters 
} from './types'

/**
 * Admin Service - Handles all user management operations
 * Following CLAUDE.md guidelines: Single responsibility, <250 lines, clean error handling
 */
export class AdminService {
  // User Query Operations
  static async getUsers(filters: UserFilters = {}): Promise<{ users: AuthorizedUser[]; totalCount: number }> {
    return UserQueries.getUsers(filters)
  }

  static async getUserStats(): Promise<UserStats> {
    return UserQueries.getUserStats()
  }

  // User Mutation Operations
  static async createUser(userData: CreateUserRequest, invitedBy?: string): Promise<AuthorizedUser> {
    return UserMutations.createUser(userData, invitedBy)
  }

  static async updateUser(userId: string, updates: UpdateUserRequest): Promise<AuthorizedUser> {
    return UserMutations.updateUser(userId, updates)
  }

  static async deactivateUser(userId: string): Promise<void> {
    return UserMutations.deactivateUser(userId)
  }

  static async reactivateUser(userId: string): Promise<void> {
    return UserMutations.reactivateUser(userId)
  }

  // Permission Operations
  static async getPermissions(): Promise<Array<{
    key: string
    name: string
    description: string | null
    category: string
  }>> {
    return PermissionQueries.getPermissions()
  }

  static async getUserActivity(userId?: string, limit = 50): Promise<any[]> {
    return PermissionQueries.getUserActivity(userId, limit)
  }
}