import React, { useState, useEffect } from 'react'
import { AdminService } from '../../../services/admin'
import { ALL_PERMISSIONS } from '../../../types/permissions.types'
import { useToast } from '../../../hooks/use-toast'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/card'
import { Button } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { Label } from '../../../components/ui/label'
import { Textarea } from '../../../components/ui/textarea'
import { StatusBadge } from '../../../components/ui/status-badge'
import { MetricBadge } from '../../../components/ui/metric-badge'
import { Checkbox } from '../../../components/ui/checkbox'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../components/ui/select'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '../../../components/ui/tabs'
import { ScrollArea } from '../../../components/ui/scroll-area'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../../../components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../../../components/ui/dropdown-menu'
import {
  Search,
  Shield,
  Plus,
  Settings,
  Save,
  Edit,
  Trash2,
  Copy,
  MoreHorizontal,
  Crown,
  Users,
  Eye
} from 'lucide-react'

interface PermissionsTabProps {
  canManageTemplates: boolean
  hasFullAccess: boolean
}

interface RoleTemplate {
  id: string
  name: string
  displayName: string
  description: string
  permissions: string[]
  isSystemRole: boolean
  usageCount: number
}

const BUILT_IN_TEMPLATES: RoleTemplate[] = [
  {
    id: 'viewer',
    name: 'viewer',
    displayName: 'Viewer',
    description: 'Read-only access to basic features',
    permissions: ['pages.orders_access', 'pages.products_access', 'pages.clients_access'],
    isSystemRole: true,
    usageCount: 0
  },
  {
    id: 'order_manager',
    name: 'order_manager',
    displayName: 'Order Manager',
    description: 'Manage orders and customer interactions',
    permissions: [
      'pages.orders_access', 'pages.products_access', 'pages.clients_access',
      'orders.create', 'orders.general_info_edit', 'orders.items_edit'
    ],
    isSystemRole: true,
    usageCount: 0
  },
  {
    id: 'product_manager',
    name: 'product_manager',
    displayName: 'Product Manager',
    description: 'Manage products, pricing, and inventory',
    permissions: [
      'pages.orders_access', 'pages.products_access', 'pages.clients_access',
      'products.pricing_edit', 'orders.create'
    ],
    isSystemRole: true,
    usageCount: 0
  },
  {
    id: 'supervisor',
    name: 'supervisor',
    displayName: 'Supervisor',
    description: 'Advanced access with analytics and oversight',
    permissions: [
      'pages.orders_access', 'pages.products_access', 'pages.clients_access',
      'pages.analytics_overview_access', 'pages.analytics_sales_access',
      'orders.create', 'orders.general_info_edit', 'orders.items_edit',
      'products.pricing_edit', 'clients.create', 'analytics.export'
    ],
    isSystemRole: true,
    usageCount: 0
  },
  {
    id: 'admin',
    name: 'admin',
    displayName: 'Administrator',
    description: 'Full system access and user management',
    permissions: Object.values(ALL_PERMISSIONS),
    isSystemRole: true,
    usageCount: 0
  }
]

export function PermissionsTab({ canManageTemplates, hasFullAccess }: PermissionsTabProps) {
  const { toast } = useToast()
  const [templates, setTemplates] = useState<RoleTemplate[]>(BUILT_IN_TEMPLATES)
  const [searchTerm, setSearchTerm] = useState('')
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [editingTemplate, setEditingTemplate] = useState<RoleTemplate | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    displayName: '',
    description: '',
    permissions: [] as string[]
  })

  const getPermissionsByCategory = () => {
    const categories: Record<string, string[]> = {}
    
    Object.values(ALL_PERMISSIONS).forEach((permission) => {
      const [category] = permission.split('.')
      if (!categories[category]) {
        categories[category] = []
      }
      categories[category].push(permission)
    })
    
    return categories
  }

  const permissionCategories = getPermissionsByCategory()

  const handleCreateTemplate = () => {
    setFormData({
      name: '',
      displayName: '',
      description: '',
      permissions: []
    })
    setEditingTemplate(null)
    setShowCreateDialog(true)
  }

  const handleEditTemplate = (template: RoleTemplate) => {
    setFormData({
      name: template.name,
      displayName: template.displayName,
      description: template.description,
      permissions: [...template.permissions]
    })
    setEditingTemplate(template)
    setShowCreateDialog(true)
  }

  const handleDuplicateTemplate = (template: RoleTemplate) => {
    setFormData({
      name: `${template.name}_copy`,
      displayName: `${template.displayName} (Copy)`,
      description: template.description,
      permissions: [...template.permissions]
    })
    setEditingTemplate(null)
    setShowCreateDialog(true)
  }

  const handleSaveTemplate = () => {
    if (!formData.name.trim() || !formData.displayName.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Template name and display name are required',
        variant: 'destructive'
      })
      return
    }

    const newTemplate: RoleTemplate = {
      id: editingTemplate?.id || `custom_${Date.now()}`,
      name: formData.name.trim(),
      displayName: formData.displayName.trim(),
      description: formData.description.trim(),
      permissions: formData.permissions,
      isSystemRole: editingTemplate?.isSystemRole || false,
      usageCount: editingTemplate?.usageCount || 0
    }

    if (editingTemplate) {
      setTemplates(prev => prev.map(t => t.id === editingTemplate.id ? newTemplate : t))
      toast({
        title: 'Template Updated',
        description: `Role template "${formData.displayName}" has been updated`,
      })
    } else {
      setTemplates(prev => [...prev, newTemplate])
      toast({
        title: 'Template Created',
        description: `Role template "${formData.displayName}" has been created`,
      })
    }

    setShowCreateDialog(false)
  }

  const handleDeleteTemplate = (template: RoleTemplate) => {
    if (template.isSystemRole) {
      toast({
        title: 'Cannot Delete',
        description: 'System role templates cannot be deleted',
        variant: 'destructive'
      })
      return
    }

    setTemplates(prev => prev.filter(t => t.id !== template.id))
    toast({
      title: 'Template Deleted',
      description: `Role template "${template.displayName}" has been deleted`,
    })
  }

  const handlePermissionToggle = (permission: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      permissions: checked
        ? [...prev.permissions, permission]
        : prev.permissions.filter(p => p !== permission)
    }))
  }

  const filteredTemplates = templates.filter(template =>
    !searchTerm || 
    template.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    template.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getTemplateIcon = (template: RoleTemplate) => {
    if (template.name === 'admin') return Crown
    if (template.name === 'supervisor') return Shield
    if (template.name === 'viewer') return Eye
    return Users
  }

  if (!canManageTemplates) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-muted-foreground">
            <Shield className="h-5 w-5" />
            Role Template Management
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            You don't have permission to manage role templates.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-semibold tracking-tight">Role Templates</h2>
          <p className="text-muted-foreground">
            Create and manage permission role templates for users
          </p>
        </div>
        <Button onClick={handleCreateTemplate} className="bg-blue-600 hover:bg-blue-700 text-white">
          <Plus className="h-4 w-4 mr-2" />
          Create Template
        </Button>
      </div>

      {/* Search */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search role templates..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <MetricBadge 
          value={filteredTemplates.length} 
          label="templates" 
          variant="info" 
          className="text-sm"
        />
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredTemplates.map((template) => {
          const IconComponent = getTemplateIcon(template)
          return (
            <Card key={template.id} className="relative">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                      <IconComponent className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{template.displayName}</CardTitle>
                      {template.isSystemRole && (
                        <StatusBadge status="admin" size="sm" className="mt-1">
                          System Role
                        </StatusBadge>
                      )}
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleEditTemplate(template)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit Template
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleDuplicateTemplate(template)}>
                        <Copy className="h-4 w-4 mr-2" />
                        Duplicate
                      </DropdownMenuItem>
                      {!template.isSystemRole && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => handleDeleteTemplate(template)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  {template.description}
                </p>
                <div className="flex items-center justify-between">
                  <MetricBadge 
                    value={template.permissions.length} 
                    label="permissions" 
                    variant="primary" 
                    className="text-xs"
                  />
                  <MetricBadge 
                    value={template.usageCount} 
                    label="users" 
                    variant="secondary" 
                    className="text-xs"
                  />
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Create/Edit Template Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog} key={editingTemplate?.id || 'new-template'}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>
              {editingTemplate ? 'Edit Role Template' : 'Create Role Template'}
            </DialogTitle>
            <DialogDescription>
              {editingTemplate 
                ? 'Modify the role template settings and permissions'
                : 'Create a new role template with specific permissions'
              }
            </DialogDescription>
          </DialogHeader>

          <Tabs defaultValue="details" className="w-full" key={editingTemplate?.id || 'new'}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="details">Template Details</TabsTrigger>
              <TabsTrigger value="permissions">
                Permissions <MetricBadge value={formData.permissions.length} variant="info" className="ml-1 text-xs" />
              </TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Template Name *</Label>
                  <Input
                    id="name"
                    placeholder="e.g., customer_service"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    disabled={editingTemplate?.isSystemRole}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="displayName">Display Name *</Label>
                  <Input
                    id="displayName"
                    placeholder="e.g., Customer Service"
                    value={formData.displayName}
                    onChange={(e) => setFormData(prev => ({ ...prev, displayName: e.target.value }))}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="Describe the role and its responsibilities..."
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                />
              </div>
            </TabsContent>

            <TabsContent value="permissions" className="space-y-4">
              <ScrollArea className="h-96">
                <div className="space-y-6">
                  {Object.entries(permissionCategories).map(([category, permissions]) => (
                    <div key={category}>
                      <h4 className="font-medium text-sm mb-3 text-gray-700 capitalize flex items-center gap-2">
                        {category.replace('_', ' ')} Permissions
                        <MetricBadge value={permissions.length} variant="secondary" className="text-xs" />
                      </h4>
                      <div className="grid grid-cols-1 gap-2 ml-2">
                        {permissions.map((permission) => (
                          <div key={permission} className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded">
                            <Checkbox
                              id={permission}
                              checked={formData.permissions.includes(permission)}
                              onCheckedChange={(checked) => 
                                handlePermissionToggle(permission, checked as boolean)
                              }
                            />
                            <label htmlFor={permission} className="text-sm cursor-pointer flex-1">
                              {permission.split('.').join(' → ').replace(/_/g, ' ')}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveTemplate} className="bg-blue-600 hover:bg-blue-700 text-white">
              <Save className="h-4 w-4 mr-2" />
              {editingTemplate ? 'Update Template' : 'Create Template'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}