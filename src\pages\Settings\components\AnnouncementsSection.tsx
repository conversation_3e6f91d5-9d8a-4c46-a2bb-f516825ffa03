import React from 'react'
import { Bell, Plus, RefreshCw, MessageSquare, CheckCircle, AlertTriangle, Eye, Edit3, Trash2 } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/card'
import { Button } from '../../../components/ui/button'
import { Badge } from '../../../components/ui/badge'
import { Switch } from '../../../components/ui/switch'
import { Label } from '../../../components/ui/label'
import type { DatabaseAnnouncement } from '../../../services/dynamicAnnouncements.service'

interface AnnouncementsSectionProps {
  announcements: DatabaseAnnouncement[]
  contextAnnouncements: any[]
  isLoading: boolean
  onCreateTestAnnouncement: () => void
  onLoadAnnouncements: () => void
}

export const AnnouncementsSection: React.FC<AnnouncementsSectionProps> = ({
  announcements,
  contextAnnouncements,
  isLoading,
  onCreateTestAnnouncement,
  onLoadAnnouncements
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="w-5 h-5" />
          System Announcements
        </CardTitle>
        <CardDescription>
          Manage system-wide announcements and notifications
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Announcements Status */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center gap-2 mb-2">
              <MessageSquare className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-900">Active Announcements</span>
            </div>
            <div className="text-2xl font-bold text-blue-900">{contextAnnouncements.length}</div>
          </div>
          <div className="p-4 bg-green-50 rounded-lg border border-green-200">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <span className="text-sm font-medium text-green-900">System Status</span>
            </div>
            <div className="text-lg font-semibold text-green-900">Operational</div>
          </div>
          <div className="p-4 bg-amber-50 rounded-lg border border-amber-200">
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="w-4 h-4 text-amber-600" />
              <span className="text-sm font-medium text-amber-900">Calculation Errors</span>
            </div>
            <div className="text-lg font-semibold text-amber-900">0 Unresolved</div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex flex-wrap gap-2">
          <Button 
            onClick={onCreateTestAnnouncement}
            variant="outline" 
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Create Test Announcement
          </Button>
          <Button 
            onClick={onLoadAnnouncements}
            variant="outline" 
            className="flex items-center gap-2"
            disabled={isLoading}
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        {/* Announcements List */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Current Announcements</h3>
            <Badge variant="secondary">{announcements.length} total</Badge>
          </div>
          
          {isLoading ? (
            <div className="text-center py-8 text-gray-500">Loading announcements...</div>
          ) : announcements.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Bell className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>No active announcements</p>
              <p className="text-sm">Create your first announcement using the button above</p>
            </div>
          ) : (
            <div className="space-y-3">
              {announcements.map((announcement) => (
                <div key={announcement.id} className="p-4 border rounded-lg bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant={
                          announcement.type === 'error' ? 'destructive' :
                          announcement.type === 'warning' ? 'default' :
                          announcement.type === 'success' ? 'default' : 'secondary'
                        }>
                          {announcement.type.toUpperCase()}
                        </Badge>
                        <Badge variant="outline">
                          {announcement.priority.toUpperCase()}
                        </Badge>
                        <span className="text-xs text-gray-500">
                          {new Date(announcement.created_at).toLocaleDateString()}
                        </span>
                      </div>
                      <h4 className="font-semibold text-gray-900 mb-1">{announcement.title}</h4>
                      <p className="text-sm text-gray-600 mb-2">{announcement.message}</p>
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <span className="flex items-center gap-1">
                          <Eye className="w-3 h-3" />
                          {announcement.view_count} views
                        </span>
                        <span>Target: {announcement.target_audience.join(', ')}</span>
                        {announcement.display_until && (
                          <span>Expires: {new Date(announcement.display_until).toLocaleDateString()}</span>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2 ml-4">
                      <Button variant="ghost" size="sm">
                        <Edit3 className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Announcement Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Announcement Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="auto-calc-errors">Auto-create calculation error announcements</Label>
                <p className="text-sm text-gray-500">Automatically create announcements when calculation rules errors occur</p>
              </div>
              <Switch id="auto-calc-errors" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="real-time-updates">Real-time announcement updates</Label>
                <p className="text-sm text-gray-500">Enable live updates for new announcements</p>
              </div>
              <Switch id="real-time-updates" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="auto-cleanup">Auto-cleanup expired announcements</Label>
                <p className="text-sm text-gray-500">Automatically remove announcements after they expire</p>
              </div>
              <Switch id="auto-cleanup" defaultChecked />
            </div>
          </CardContent>
        </Card>
      </CardContent>
    </Card>
  )
}