-- Fix User ID Mismatch Between auth.users and authorized_users
-- 
-- Problem: When admins create users in authorized_users, they get a random UUID.
-- When users sign up, they get a different UUID in auth.users, causing RLS failures.
--
-- Solution: Create a system that syncs IDs properly and handles future user creation correctly.

-- ============================================================================
-- STEP 1: CREATE FUNCTION TO SYNC EXISTING USER IDS BY EMAIL
-- ============================================================================

CREATE OR REPLACE FUNCTION sync_authorized_user_ids()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  auth_user RECORD;
  authorized_user RECORD;
  update_count INTEGER := 0;
BEGIN
  -- Loop through all auth.users and find matching authorized_users by email
  FOR auth_user IN 
    SELECT id as auth_id, email 
    FROM auth.users 
    WHERE email IS NOT NULL
  LOOP
    -- Find matching authorized_users record by email with different ID
    SELECT id as authorized_id, email 
    INTO authorized_user
    FROM authorized_users 
    WHERE email = auth_user.email 
    AND id != auth_user.auth_id;
    
    -- If found a mismatch, update the authorized_users ID
    IF authorized_user.authorized_id IS NOT NULL THEN
      RAISE NOTICE 'Syncing user: % | Auth ID: % | Old Authorized ID: %', 
        auth_user.email, auth_user.auth_id, authorized_user.authorized_id;
      
      -- Update authorized_users to use the auth.users ID
      UPDATE authorized_users 
      SET id = auth_user.auth_id,
          updated_at = NOW()
      WHERE email = auth_user.email;
      
      update_count := update_count + 1;
    END IF;
  END LOOP;
  
  RAISE NOTICE 'Sync complete. Updated % user records.', update_count;
END;
$$;

-- ============================================================================
-- STEP 2: CREATE TRIGGER FUNCTION FOR FUTURE USER CREATION
-- ============================================================================

CREATE OR REPLACE FUNCTION handle_auth_user_created()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- When a new user signs up, check if they have a pre-created authorized_users record
  -- If so, update it with their actual auth.uid()
  UPDATE authorized_users 
  SET id = NEW.id,
      first_login_at = COALESCE(first_login_at, NOW()),
      updated_at = NOW()
  WHERE email = NEW.email 
  AND id != NEW.id; -- Only update if IDs don't match
  
  -- Log the sync if it happened
  IF FOUND THEN
    RAISE NOTICE 'Auto-synced authorized_users for new auth user: % (ID: %)', NEW.email, NEW.id;
  END IF;
  
  RETURN NEW;
END;
$$;

-- ============================================================================
-- STEP 3: CREATE THE TRIGGER ON AUTH.USERS
-- ============================================================================

-- Drop trigger if it exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Create trigger to automatically sync IDs when users sign up
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_auth_user_created();

-- ============================================================================
-- STEP 4: RUN THE SYNC FOR EXISTING DATA
-- ============================================================================

-- Fix all existing ID mismatches
SELECT sync_authorized_user_ids();

-- ============================================================================
-- STEP 5: CREATE IMPROVED USER CREATION FUNCTION
-- ============================================================================

CREATE OR REPLACE FUNCTION create_authorized_user_properly(
  user_email TEXT,
  user_first_name TEXT,
  user_last_name TEXT,
  user_department TEXT DEFAULT NULL,
  user_permissions JSONB DEFAULT '[]'::JSONB,
  user_role_template TEXT DEFAULT NULL,
  user_notes TEXT DEFAULT NULL,
  invited_by_id UUID DEFAULT NULL
)
RETURNS TABLE(
  id UUID,
  email TEXT,
  first_name TEXT,
  last_name TEXT,
  department TEXT,
  permissions JSONB,
  role_template TEXT,
  is_active BOOLEAN,
  notes TEXT,
  invited_by UUID,
  invited_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  existing_auth_id UUID;
  new_user_id UUID;
BEGIN
  -- Check if user already exists in auth.users
  SELECT auth.users.id INTO existing_auth_id
  FROM auth.users 
  WHERE auth.users.email = user_email;
  
  -- If user exists in auth, use their ID. Otherwise, generate a temp ID that will be synced later
  IF existing_auth_id IS NOT NULL THEN
    new_user_id := existing_auth_id;
    RAISE NOTICE 'Using existing auth ID for user %: %', user_email, new_user_id;
  ELSE
    new_user_id := gen_random_uuid();
    RAISE NOTICE 'Generated temp ID for user % (will sync when they sign up): %', user_email, new_user_id;
  END IF;
  
  -- Insert the authorized_users record with proper ID
  RETURN QUERY
  INSERT INTO authorized_users (
    id,
    email,
    first_name,
    last_name,
    department,
    permissions,
    role_template,
    is_active,
    notes,
    invited_by,
    invited_at
  ) VALUES (
    new_user_id,
    user_email,
    user_first_name,
    user_last_name,
    user_department,
    user_permissions,
    user_role_template,
    true,
    user_notes,
    invited_by_id,
    NOW()
  )
  RETURNING 
    authorized_users.id,
    authorized_users.email,
    authorized_users.first_name,
    authorized_users.last_name,
    authorized_users.department,
    authorized_users.permissions,
    authorized_users.role_template,
    authorized_users.is_active,
    authorized_users.notes,
    authorized_users.invited_by,
    authorized_users.invited_at,
    authorized_users.created_at,
    authorized_users.updated_at;
END;
$$;

-- ============================================================================
-- STEP 6: ADD HELPFUL DOCUMENTATION
-- ============================================================================

COMMENT ON FUNCTION sync_authorized_user_ids() IS 
'Syncs existing authorized_users IDs to match auth.users IDs by email. Run once to fix historical data.';

COMMENT ON FUNCTION handle_auth_user_created() IS 
'Trigger function that automatically syncs authorized_users ID when a user signs up, preventing future ID mismatches.';

COMMENT ON FUNCTION create_authorized_user_properly(TEXT, TEXT, TEXT, TEXT, JSONB, TEXT, TEXT, UUID) IS 
'Improved user creation function that handles ID syncing properly. Use this instead of direct INSERT.';

-- ============================================================================
-- VERIFICATION AND TESTING
-- ============================================================================

DO $$
BEGIN
  RAISE NOTICE '================================================================================';
  RAISE NOTICE 'USER ID MISMATCH FIX COMPLETED';
  RAISE NOTICE '================================================================================';
  RAISE NOTICE '';
  RAISE NOTICE '✅ Created sync function for existing data';
  RAISE NOTICE '✅ Created trigger for future user signups';
  RAISE NOTICE '✅ Synced all existing user IDs';
  RAISE NOTICE '✅ Created improved user creation function';
  RAISE NOTICE '';
  RAISE NOTICE 'Next Steps:';
  RAISE NOTICE '1. Update AdminService to use create_authorized_user_properly()';
  RAISE NOTICE '2. Test user creation and signin flows';
  RAISE NOTICE '3. Verify RLS policies now work correctly';
  RAISE NOTICE '';
  RAISE NOTICE '================================================================================';
END;
$$;