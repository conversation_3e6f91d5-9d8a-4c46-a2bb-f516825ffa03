import React, { useState } from 'react'
import type { AuthorizedUser } from '../../../../services/admin'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../../components/ui/card'
import { 
  Table, 
  TableBody, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '../../../../components/ui/table'
import { CreateUserSheet } from '../CreateUserSheet'
import { EditUserSheet } from '../EditUserSheet'
import { UserTableHeader } from './components/UserTableHeader'
import { MemoizedUserTableRow } from './components/MemoizedUserTableRow'
import { useUserFilters } from './hooks/useUserFilters'
import { useUserActions } from './hooks/useUserActions'
import type { UserManagementTabProps } from './types'

export function UserManagementTab({ 
  users, 
  onRefresh, 
  canCreate, 
  canEdit, 
  canDelete 
}: UserManagementTabProps) {
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [editingUser, setEditingUser] = useState<AuthorizedUser | null>(null)

  const {
    filters,
    filteredUsers,
    updateSearch,
    updateActiveFilter
  } = useUserFilters(users)

  const {
    processingUserId,
    handleToggleUserStatus,
    handlePermissionUpdate
  } = useUserActions(onRefresh)

  const handleEditUser = (user: AuthorizedUser) => {
    setEditingUser(user)
  }

  const handleCloseEdit = () => {
    console.log('🔴 handleCloseEdit called - clearing editingUser')
    setEditingUser(null)
  }

  const handleDeleteUser = (user: AuthorizedUser) => {
    // TODO: Implement user deletion with confirmation
    console.log('Delete user:', user)
  }

  return (
    <div className="space-y-6">
      <UserTableHeader
        users={users}
        filters={filters}
        onSearchChange={updateSearch}
        onActiveFilterChange={updateActiveFilter}
        onCreateUser={() => setShowCreateDialog(true)}
        canCreate={canCreate}
      />

      <Card>
        <CardHeader>
          <CardTitle>Users</CardTitle>
          <CardDescription>
            Manage system users and their access permissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Permissions</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Department</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.map((user) => (
                <MemoizedUserTableRow
                  key={user.id}
                  user={user}
                  onEdit={handleEditUser}
                  onToggleStatus={handleToggleUserStatus}
                  onDelete={handleDeleteUser}
                  onPermissionUpdate={handlePermissionUpdate}
                  processingUserId={processingUserId}
                  canEdit={canEdit}
                  canDelete={canDelete}
                />
              ))}
            </TableBody>
          </Table>
          
          {filteredUsers.length === 0 && (
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-0">
                {filters.search || filters.activeFilter !== null 
                  ? 'No users found matching your criteria'
                  : 'No users found'
                }
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      <CreateUserSheet
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onSuccess={onRefresh}
      />
      
      {editingUser && (
        <EditUserSheet
          open={!!editingUser}
          onOpenChange={(open) => {
            console.log('🔵 Sheet onOpenChange called with:', open)
            if (!open) {
              handleCloseEdit()
            }
          }}
          user={editingUser}
          onSuccess={onRefresh}
        />
      )}
    </div>
  )
}