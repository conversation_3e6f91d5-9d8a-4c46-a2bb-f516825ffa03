/**
 * Permission Service - Production Grade
 * 
 * Following CLAUDE.md guidelines:
 * - <250 lines, single responsibility
 * - Pure functions and immutable data patterns
 * - Strict TypeScript typing
 * - Performance optimizations with caching
 */

import { supabase } from '../../lib/supabase';
import type {
  PermissionKey,
  RoleId,
  UserPermissions,
  PermissionCache,
  PermissionServiceConfig,
  PermissionError
} from '../../types/permissions.types';
import { ROLES, isValidPermissionKey } from '../../types/permissions.types';

// ============================================================================
// SERVICE CONFIGURATION
// ============================================================================

const DEFAULT_CONFIG: PermissionServiceConfig = {
  cacheTTL: 5 * 60 * 1000, // 5 minutes
  maxCacheSize: 1000,       // Max cached users
  enableLogging: process.env.NODE_ENV === 'development'
};

// ============================================================================
// PERMISSION SERVICE
// ============================================================================

export class PermissionService {
  private static readonly SUPER_ADMIN: PermissionKey = 'system.admin';
  private static cache = new Map<string, PermissionCache>();
  private static config = DEFAULT_CONFIG;

  // ==========================================================================
  // CORE PERMISSION CHECKING
  // ==========================================================================

  /**
   * Main permission checking method - replaces all existing methods
   */
  static hasPermission(
    userPermissions: readonly PermissionKey[], 
    required: PermissionKey | readonly PermissionKey[]
  ): boolean {
    // Fail-safe: deny if no permissions
    if (!userPermissions || userPermissions.length === 0) {
      return false;
    }

    // Super admin bypass (grants everything)
    if (userPermissions.includes(this.SUPER_ADMIN)) {
      return true;
    }

    // Array of permissions (OR logic - user needs ANY of these)
    if (Array.isArray(required)) {
      return required.some(perm => userPermissions.includes(perm));
    }

    // Single permission check
    return userPermissions.includes(required as PermissionKey);
  }

  /**
   * Check if user has ALL required permissions (AND logic)
   */
  static hasAllPermissions(
    userPermissions: readonly PermissionKey[], 
    required: readonly PermissionKey[]
  ): boolean {
    // Fail-safe: deny if no permissions
    if (!userPermissions || userPermissions.length === 0) {
      return false;
    }

    // Super admin bypass
    if (userPermissions.includes(this.SUPER_ADMIN)) {
      return true;
    }

    // Check that user has every required permission
    return required.every(perm => userPermissions.includes(perm));
  }

  // ==========================================================================
  // USER PERMISSION FETCHING
  // ==========================================================================

  /**
   * Get user permissions with intelligent caching
   */
  static async getUserPermissions(userId: string): Promise<readonly PermissionKey[]> {
    if (!userId) {
      return [];
    }

    // Check cache first
    const cached = this.getCachedPermissions(userId);
    if (cached) {
      return cached.permissions;
    }

    try {
      // Fetch user with role from database
      const userPermissions = await this.fetchUserPermissionsFromDB(userId);
      
      // Cache the result
      this.setCachedPermissions(userId, userPermissions);
      
      return userPermissions;

    } catch (error) {
      this.logError('Error fetching user permissions', { userId, error });
      return []; // Fail-safe: return empty permissions
    }
  }

  /**
   * Get user role and permissions together
   */
  static async getUserRole(userId: string): Promise<{ role: RoleId | null; permissions: readonly PermissionKey[] }> {
    if (!userId) {
      return { role: null, permissions: [] };
    }

    try {
      const { data: user } = await supabase
        .from('authorized_users')
        .select('role_template, permissions')
        .eq('id', userId)
        .eq('is_active', true)
        .single();

      if (!user) {
        return { role: null, permissions: [] };
      }

      const role = user.role_template as RoleId | null;
      const userOverrides = (user.permissions as PermissionKey[]) || [];
      
      // Get role permissions
      let rolePermissions: readonly PermissionKey[] = [];
      if (role && role in ROLES) {
        rolePermissions = ROLES[role].permissions;
      }

      // Combine role permissions with user overrides
      const allPermissions = [...rolePermissions, ...userOverrides];
      const uniquePermissions = Array.from(new Set(allPermissions));

      return {
        role,
        permissions: uniquePermissions as readonly PermissionKey[]
      };

    } catch (error) {
      this.logError('Error fetching user role', { userId, error });
      return { role: null, permissions: [] };
    }
  }

  // ==========================================================================
  // CACHE MANAGEMENT
  // ==========================================================================

  private static getCachedPermissions(userId: string): PermissionCache | null {
    const cached = this.cache.get(userId);
    
    if (!cached) {
      return null;
    }

    // Check if cache is expired
    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(userId);
      return null;
    }

    return cached;
  }

  private static setCachedPermissions(userId: string, permissions: readonly PermissionKey[]): void {
    // Prevent cache from growing too large
    if (this.cache.size >= this.config.maxCacheSize) {
      // Remove oldest entries (simple LRU)
      const oldestKey = this.cache.keys().next().value;
      if (oldestKey) {
        this.cache.delete(oldestKey);
      }
    }

    this.cache.set(userId, {
      permissions,
      timestamp: Date.now(),
      ttl: this.config.cacheTTL
    });
  }

  /**
   * Clear cache for specific user or all users
   */
  static clearCache(userId?: string): void {
    if (userId) {
      this.cache.delete(userId);
    } else {
      this.cache.clear();
    }
  }

  // ==========================================================================
  // DATABASE OPERATIONS
  // ==========================================================================

  private static async fetchUserPermissionsFromDB(userId: string): Promise<readonly PermissionKey[]> {
    const { data: user } = await supabase
      .from('authorized_users')
      .select('role_template, permissions')
      .eq('id', userId)
      .eq('is_active', true)
      .single();

    if (!user) {
      return [];
    }

    // Get base role permissions
    let rolePermissions: readonly PermissionKey[] = [];
    if (user.role_template && user.role_template in ROLES) {
      rolePermissions = ROLES[user.role_template as RoleId].permissions;
    }

    // Get user-specific permission overrides
    const userOverrides = (user.permissions as PermissionKey[]) || [];
    
    // Combine and deduplicate
    const allPermissions = [...rolePermissions, ...userOverrides];
    const validPermissions = allPermissions.filter(isValidPermissionKey);
    const uniquePermissions = Array.from(new Set(validPermissions));

    return uniquePermissions as readonly PermissionKey[];
  }

  // ==========================================================================
  // UTILITY METHODS
  // ==========================================================================

  /**
   * Check if user is system administrator
   */
  static isSystemAdmin(userPermissions: readonly PermissionKey[]): boolean {
    return userPermissions.includes(this.SUPER_ADMIN);
  }

  /**
   * Get permissions by resource category
   */
  static getResourcePermissions(
    userPermissions: readonly PermissionKey[], 
    resource: string
  ): readonly PermissionKey[] {
    return userPermissions.filter(perm => perm.startsWith(`${resource}.`));
  }

  /**
   * Validate permission key format
   */
  static validatePermissionKey(permission: unknown): permission is PermissionKey {
    return isValidPermissionKey(permission);
  }

  // ==========================================================================
  // CONFIGURATION & LOGGING
  // ==========================================================================

  static configure(config: Partial<PermissionServiceConfig>): void {
    this.config = { ...this.config, ...config };
  }

  private static logError(message: string, context?: Record<string, unknown>): void {
    if (this.config.enableLogging) {
      console.error(`[PermissionService] ${message}`, context);
    }
  }

  private static logDebug(message: string, context?: Record<string, unknown>): void {
    if (this.config.enableLogging) {
      console.debug(`[PermissionService] ${message}`, context);
    }
  }

  // ==========================================================================
  // CACHE STATISTICS (Development/Debugging)
  // ==========================================================================

  static getCacheStats(): {
    size: number;
    maxSize: number; 
    ttl: number;
    hitRate?: number;
  } {
    return {
      size: this.cache.size,
      maxSize: this.config.maxCacheSize,
      ttl: this.config.cacheTTL
    };
  }
}