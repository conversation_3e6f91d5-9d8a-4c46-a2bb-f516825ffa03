import { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { logger } from '../../../../utils/logger';
import { logAuthInfo } from '../../../../utils/authDebugUtils';
import { UserStateDetectionService } from '../../../../services/auth/userStateDetection.service';
import type { UserStateResult } from '../../../../services/auth/userStateDetection.service';
import { useAuthForm, validateEmail } from '../../../../hooks/auth/useAuthForm';
import type { AuthStep, UserType } from '../types';

const emailLogger = logger.withPrefix('EmailLogin');

export const useEmailLogin = () => {
  const navigate = useNavigate();
  
  // Form state
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const { loading, error, success, setLoading, setError, setSuccess, clearMessages } = useAuthForm();
  
  // Flow state
  const [currentStep, setCurrentStep] = useState<AuthStep>('email');
  const [userType, setUserType] = useState<UserType>('unknown');
  const [userState, setUserState] = useState<UserStateResult | null>(null);

  // Log auth info when component mounts to help with debugging
  useEffect(() => {
    logAuthInfo();
  }, []);

  // Detect user authentication state comprehensively
  const detectUserState = useCallback(async (email: string): Promise<UserStateResult | null> => {
    try {
      const stateResult = await UserStateDetectionService.detectUserState(email);
      emailLogger.debug('User state detected for login:', stateResult);
      return stateResult;
    } catch (error) {
      emailLogger.error('Error detecting user state:', error);
      return null;
    }
  }, []);

  // Handle email input change
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const emailValue = e.target.value;
    setEmail(emailValue);
    clearMessages();
    
    // Reset user type - will be determined when email is submitted
    setUserType('unknown');
  };

  // Handle email submission - check account exists and determine auth method by role
  const handleEmailSubmit = useCallback(async () => {
    const emailError = validateEmail(email);
    if (emailError) {
      setError(emailError);
      return;
    }

    setLoading(true);
    clearMessages();

    try {
      // Detect comprehensive user authentication state
      const stateResult = await detectUserState(email);
      
      if (!stateResult) {
        setError('Failed to verify account status. Please try again.');
        return;
      }

      setUserState(stateResult);

      // Handle different user states
      switch (stateResult.state) {
        case 'unauthorized':
          setError('Email not authorized for this system. Please contact your administrator.');
          return;

        case 'authorized_only':
          setError(`Account setup not complete. ${stateResult.message}. Please complete your signup first.`);
          // Add link to signup
          setTimeout(() => {
            setError(`Account setup not complete. Please complete your signup first.`);
            setSuccess(`Redirected to signup in 3 seconds...`);
            setTimeout(() => {
              navigate('/pre-auth?email=' + encodeURIComponent(email));
            }, 3000);
          }, 1000);
          return;

        case 'signup_incomplete':
          // For signup_incomplete, the user has an auth account but incomplete profile
          // Allow login and handle profile completion post-authentication
          const incompleteUserRole = stateResult.authorizedUser?.role_template || 'user';
          
          if (incompleteUserRole === 'admin' || incompleteUserRole === 'administrator') {
            // Admin users use password authentication
            setUserType('admin');
            setCurrentStep('password');
            emailLogger.debug('Admin role detected (incomplete profile), moving to password step');
          } else {
            // Regular users use PIN authentication
            setUserType('regular');
            setCurrentStep('password'); // Use same password step but with PIN UI
            emailLogger.debug(`Role "${incompleteUserRole}" detected (incomplete profile), moving to PIN step`);
          }
          return;

        case 'account_complete':
          // Proceed with login based on user role
          const userRole = stateResult.authorizedUser?.role_template || 'user';
          
          if (userRole === 'admin' || userRole === 'administrator') {
            // Admin users use password authentication
            setUserType('admin');
            setCurrentStep('password');
            emailLogger.debug('Admin role detected, moving to password step');
          } else {
            // Regular users use PIN authentication
            setUserType('regular');
            setCurrentStep('password'); // Use same password step but with PIN UI
            emailLogger.debug(`Role "${userRole}" detected, moving to PIN step`);
          }
          break;

        default:
          setError('Unknown account state. Please contact support.');
      }
    } catch (err: any) {
      emailLogger.error('Error in email submission:', err);
      setError(err.message || 'Failed to verify email. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [email, detectUserState, navigate, setLoading, setError, setSuccess, clearMessages]);

  // Handle password login for both admin and regular users
  const handlePasswordLogin = useCallback(async () => {
    if (!password) {
      setError('Please enter your password');
      return;
    }

    setLoading(true);
    clearMessages();

    try {
      // Use Supabase password authentication for both user types
      const { supabase } = await import('../../../../lib/supabase');
      
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        // Provide user-friendly error messages
        if (error.message?.includes('Invalid login credentials')) {
          setError('Invalid email or password. Please check your credentials.');
        } else if (error.message?.includes('User not found')) {
          setError('No account found with this email address. Please sign up first.');
        } else {
          setError(error.message);
        }
        return;
      }

      setSuccess('Login successful');
      emailLogger.debug(`${userType} login successful`);
    } catch (err: any) {
      emailLogger.error('Password login error:', err);
      setError('Login failed. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [email, password, userType, setLoading, setError, setSuccess, clearMessages]);

  return {
    // State
    email,
    password,
    currentStep,
    userType,
    userState,
    loading,
    error,
    success,

    // Actions
    setPassword,
    handleEmailChange,
    handleEmailSubmit,
    handlePasswordLogin,
    clearMessages
  };
};