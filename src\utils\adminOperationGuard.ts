/**
 * Admin Operation Guard
 * 
 * Global state manager to prevent auth re-validation during admin operations
 * This prevents Edge Function cascade chains that cause UI freezing
 */

let adminOperationActive = false

/**
 * Set admin operation status
 */
export const setAdminOperationActive = (active: boolean) => {
  adminOperationActive = active
}

/**
 * Check if admin operation is currently active
 */
export const isAdminOperationActive = () => adminOperationActive

/**
 * Execute a function while preventing auth re-validation
 */
export const withAdminOperationGuard = async <T>(fn: () => Promise<T>): Promise<T> => {
  setAdminOperationActive(true)
  try {
    const result = await fn()
    // Small delay to allow React to process updates
    await new Promise(resolve => setTimeout(resolve, 100))
    return result
  } finally {
    // Clear the flag after a safe delay
    setTimeout(() => {
      setAdminOperationActive(false)
    }, 500)
  }
}