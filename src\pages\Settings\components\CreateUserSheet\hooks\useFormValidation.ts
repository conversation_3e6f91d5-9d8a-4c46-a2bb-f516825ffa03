import type { CreateUserFormData } from '../types'

export function useFormValidation() {
  const validateForm = (formData: CreateUserFormData): string | null => {
    // Required field validation (NOT NULL constraints)
    if (!formData.email.trim()) return 'Email is required'
    if (!formData.first_name.trim()) return 'First name is required'
    if (!formData.last_name.trim()) return 'Last name is required'
    
    // Email format validation (matches database CHECK constraint)
    const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/i
    if (!emailRegex.test(formData.email.trim())) {
      return 'Please enter a valid email address (e.g., <EMAIL>)'
    }
    
    // Name length validation (matches database valid_names constraint)
    if (formData.first_name.trim().length === 0) {
      return 'First name cannot be empty or contain only spaces'
    }
    if (formData.last_name.trim().length === 0) {
      return 'Last name cannot be empty or contain only spaces'
    }
    
    // Email length practical limit (PostgreSQL text has no limit but practical consideration)
    if (formData.email.trim().length > 255) {
      return 'Email address is too long (maximum 255 characters)'
    }
    
    // Name length practical limits
    if (formData.first_name.trim().length > 100) {
      return 'First name is too long (maximum 100 characters)'
    }
    if (formData.last_name.trim().length > 100) {
      return 'Last name is too long (maximum 100 characters)'
    }
    
    // Department length validation if provided
    if (formData.department && formData.department.trim().length > 100) {
      return 'Department name is too long (maximum 100 characters)'
    }
    
    // Notes length validation if provided
    if (formData.notes && formData.notes.trim().length > 1000) {
      return 'Notes are too long (maximum 1000 characters)'
    }
    
    // Permissions array validation (matches database valid_permissions constraint)
    if (formData.permissions && !Array.isArray(formData.permissions)) {
      return 'Permissions must be provided as a valid array'
    }
    
    // Require at least 1 permission per account (business rule)
    if (!formData.permissions || formData.permissions.length === 0) {
      return 'At least one permission must be assigned to each user account'
    }
    
    return null
  }

  return { validateForm }
}