import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

// Types for admin operations
interface AdminUserRequest {
  action: 'create' | 'update' | 'deactivate' | 'reactivate';
  userId?: string;
  userData?: {
    email?: string;
    first_name?: string;
    last_name?: string;
    department?: string;
    permissions?: string[];
    role_template?: string;
    notes?: string;
    is_active?: boolean;
  };
  invitedBy?: string;
}

interface AuthorizedUser {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  department?: string;
  permissions: string[];
  role_template?: string;
  is_active: boolean;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

interface AdminUserResponse {
  success: boolean;
  user?: AuthorizedUser;
  message?: string;
  error?: string;
}

/**
 * Admin User Operations Edge Function
 * 
 * Handles all admin user CRUD operations using SERVICE_ROLE_KEY
 * Bypasses RLS for reliable admin operations, matching signup reliability
 * Following CLAUDE.md: <250 lines, single responsibility, production-grade
 */
serve(async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ success: false, error: 'Method not allowed' }),
      { 
        status: 405, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }

  try {
    // Get environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const serviceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

    if (!supabaseUrl || !serviceRoleKey) {
      console.error('Missing required environment variables');
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Server configuration error' 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Create admin Supabase client with SERVICE_ROLE_KEY
    const supabaseAdmin = createClient(supabaseUrl, serviceRoleKey, {
      auth: {
        persistSession: false,
        autoRefreshToken: false,
        detectSessionInUrl: false,
      },
    });

    // Parse request body
    let requestBody: AdminUserRequest;
    try {
      requestBody = await req.json();
    } catch {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Invalid request body' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    const { action, userId, userData, invitedBy } = requestBody;

    // Validate action
    if (!['create', 'update', 'deactivate', 'reactivate'].includes(action)) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Invalid action specified' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Handle different operations
    switch (action) {
      case 'create':
        return await handleCreateUser(supabaseAdmin, userData!, invitedBy);
      
      case 'update':
        if (!userId) {
          return new Response(
            JSON.stringify({ success: false, error: 'User ID required for update' }),
            { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
          );
        }
        return await handleUpdateUser(supabaseAdmin, userId, userData!);
      
      case 'deactivate':
        if (!userId) {
          return new Response(
            JSON.stringify({ success: false, error: 'User ID required for deactivation' }),
            { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
          );
        }
        return await handleDeactivateUser(supabaseAdmin, userId);
      
      case 'reactivate':
        if (!userId) {
          return new Response(
            JSON.stringify({ success: false, error: 'User ID required for reactivation' }),
            { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
          );
        }
        return await handleReactivateUser(supabaseAdmin, userId);
      
      default:
        return new Response(
          JSON.stringify({ success: false, error: 'Unsupported action' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
    }

  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: 'Internal server error' 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});

// Helper function to handle user creation
async function handleCreateUser(
  supabaseAdmin: any, 
  userData: any, 
  invitedBy?: string
): Promise<Response> {
  try {
    if (!userData?.email || !userData?.first_name || !userData?.last_name) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Email, first name, and last name are required' 
        }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const permissions = Array.isArray(userData.permissions) ? userData.permissions : [];

    const { data, error } = await supabaseAdmin
      .from('authorized_users')
      .insert({
        email: userData.email,
        first_name: userData.first_name,
        last_name: userData.last_name,
        department: userData.department || null,
        permissions: permissions,
        role_template: userData.role_template || null,
        notes: userData.notes || null,
        invited_by: invitedBy || null,
        is_active: true
      })
      .select()
      .single();

    if (error) {
      let errorMessage = `Failed to create user: ${error.message}`;
      
      if (error.code === '23505' && error.message.includes('authorized_users_email_key')) {
        errorMessage = 'A user with this email already exists';
      }
      
      return new Response(
        JSON.stringify({ success: false, error: errorMessage }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        user: data as AuthorizedUser,
        message: 'User created successfully' 
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ success: false, error: 'Failed to create user' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
}

// Helper function to handle user updates
async function handleUpdateUser(
  supabaseAdmin: any, 
  userId: string, 
  userData: any
): Promise<Response> {
  try {
    const updateData: any = { ...userData };
    
    // Handle permissions array
    if (userData.permissions !== undefined) {
      const permissions = Array.isArray(userData.permissions) ? userData.permissions : [];
      updateData.permissions = permissions;
    }

    const { data, error } = await supabaseAdmin
      .from('authorized_users')
      .update(updateData)
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      let errorMessage = `Failed to update user: ${error.message}`;
      
      if (error.code === '23505' && error.message.includes('authorized_users_email_key')) {
        errorMessage = 'A user with this email already exists';
      }
      
      return new Response(
        JSON.stringify({ success: false, error: errorMessage }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        user: data as AuthorizedUser,
        message: 'User updated successfully' 
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ success: false, error: 'Failed to update user' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
}

// Helper function to handle user deactivation
async function handleDeactivateUser(supabaseAdmin: any, userId: string): Promise<Response> {
  try {
    const { error } = await supabaseAdmin
      .from('authorized_users')
      .update({ is_active: false })
      .eq('id', userId);

    if (error) {
      return new Response(
        JSON.stringify({ success: false, error: `Failed to deactivate user: ${error.message}` }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    return new Response(
      JSON.stringify({ success: true, message: 'User deactivated successfully' }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ success: false, error: 'Failed to deactivate user' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
}

// Helper function to handle user reactivation
async function handleReactivateUser(supabaseAdmin: any, userId: string): Promise<Response> {
  try {
    const { error } = await supabaseAdmin
      .from('authorized_users')
      .update({ is_active: true })
      .eq('id', userId);

    if (error) {
      return new Response(
        JSON.stringify({ success: false, error: `Failed to reactivate user: ${error.message}` }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    return new Response(
      JSON.stringify({ success: true, message: 'User reactivated successfully' }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ success: false, error: 'Failed to reactivate user' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
}