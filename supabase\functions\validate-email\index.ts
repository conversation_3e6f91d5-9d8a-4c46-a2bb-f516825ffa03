import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

// Types for request/response
interface ValidateEmailRequest {
  email: string;
}

interface AuthorizedUser {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  department?: string;
  permissions: string[];
  role_template?: string;
  is_active: boolean;
}

interface ValidateEmailResponse {
  authorized: boolean;
  user?: AuthorizedUser;
  error?: string;
}

/**
 * Email Validation Edge Function
 * 
 * Validates email against authorized_users table using SERVICE_ROLE_KEY
 * Following CLAUDE.md: <250 lines, single responsibility, production-grade
 */
serve(async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        status: 405, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }

  try {
    // Get environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const serviceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

    if (!supabaseUrl || !serviceRoleKey) {
      console.error('Missing required environment variables');
      return new Response(
        JSON.stringify({ 
          authorized: false, 
          error: 'Server configuration error' 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Create admin Supabase client with SERVICE_ROLE_KEY
    // This bypasses RLS policies as documented in Supabase docs
    const supabaseAdmin = createClient(supabaseUrl, serviceRoleKey, {
      auth: {
        persistSession: false,
        autoRefreshToken: false,
        detectSessionInUrl: false,
      },
    });

    // Parse request body
    let requestBody: ValidateEmailRequest;
    try {
      requestBody = await req.json();
    } catch {
      return new Response(
        JSON.stringify({ 
          authorized: false, 
          error: 'Invalid request body' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    const { email } = requestBody;

    // Validate email input
    if (!email || typeof email !== 'string') {
      return new Response(
        JSON.stringify({ 
          authorized: false, 
          error: 'Email is required' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Basic email format validation
    const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
    if (!emailRegex.test(email)) {
      return new Response(
        JSON.stringify({ 
          authorized: false, 
          error: 'Invalid email format' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Query authorized_users table
    // SERVICE_ROLE_KEY bypasses RLS policies automatically
    const { data, error } = await supabaseAdmin
      .from('authorized_users')
      .select(`
        id,
        email,
        first_name,
        last_name,
        department,
        permissions,
        role_template,
        is_active
      `)
      .eq('email', email.toLowerCase().trim())
      .eq('is_active', true)
      .single();

    if (error) {
      // Handle "not found" as unauthorized (don't leak info about valid emails)
      if (error.code === 'PGRST116') {
        return new Response(
          JSON.stringify({ 
            authorized: false,
            error: 'Email not authorized for this system'
          }),
          { 
            status: 200, // 200 status to prevent error handling on frontend
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        );
      }

      // Log actual errors but don't expose details to client
      console.error('Database error:', error);
      return new Response(
        JSON.stringify({ 
          authorized: false, 
          error: 'Failed to validate email authorization' 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Success - user is authorized
    const response: ValidateEmailResponse = {
      authorized: true,
      user: data as AuthorizedUser
    };

    return new Response(
      JSON.stringify(response),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(
      JSON.stringify({ 
        authorized: false, 
        error: 'Internal server error' 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});