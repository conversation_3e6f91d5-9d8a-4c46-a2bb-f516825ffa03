/**
 * PermissionWrapper - Component-level permission wrapper
 * 
 * Following CLAUDE.md guidelines:
 * - Single responsibility: conditional component rendering
 * - <250 lines
 * - Pure functions and immutable data patterns
 */

import React, { useMemo } from 'react';
import { usePermissionsContext } from '../../../contexts/PermissionsContext';
import type { PermissionWrapperProps, PermissionKey } from '../../../types/permissions.types';

// ============================================================================
// PERMISSION WRAPPER COMPONENT
// ============================================================================

/**
 * Conditionally renders children based on permission checks
 * Used for component-level access control (buttons, sections, etc.)
 */
export const PermissionWrapper: React.FC<PermissionWrapperProps> = ({
  permissions,
  requireAll = false,
  children,
  fallback = null
}) => {
  const { checkPermission, checkMultiplePermissions } = usePermissionsContext();

  const hasPermission = useMemo(() => {
    // Single permission check
    if (typeof permissions === 'string') {
      return checkPermission(permissions);
    }

    // Multiple permissions check
    if (permissions.length === 0) {
      return true; // No permissions required
    }

    const permissionResults = checkMultiplePermissions([...permissions]);
    
    if (requireAll) {
      // All permissions must be granted
      return permissions.every(perm => permissionResults[perm]);
    } else {
      // At least one permission must be granted
      return permissions.some(perm => permissionResults[perm]);
    }
  }, [permissions, requireAll, checkPermission, checkMultiplePermissions]);

  return hasPermission ? <>{children}</> : <>{fallback}</>;
};