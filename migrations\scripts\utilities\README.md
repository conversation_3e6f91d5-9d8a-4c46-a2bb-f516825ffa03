# Utility Scripts

General utilities, cleanup tools, verification scripts, and development aids.

## Files in this directory:

### Code Maintenance
- `cleanup-dead-code.js` - Remove unused code and dependencies

### Database Verification
- `verify-permissions-migration.sql` - Verify permission system migration integrity

### System Setup
- `setup-calculation-rules-announcements.sql` - Setup calculation rules and system announcements

## Usage

### Code Cleanup
```bash
# Remove dead code and unused dependencies
node cleanup-dead-code.js
```

### Database Verification
```sql
-- Execute in database to verify migrations
\i verify-permissions-migration.sql
```

### System Setup
```sql
-- Setup calculation rules
\i setup-calculation-rules-announcements.sql
```

## Features

- **Automated cleanup** - Remove unused code automatically
- **Migration verification** - Validate database migration integrity
- **System configuration** - Setup and configure system components
- **Development aids** - Tools to assist with development workflow

## Utility Categories

### Development Tools
- Code analysis and cleanup utilities
- Dependency management tools
- Development environment setup scripts

### Database Tools
- Migration verification and validation
- Data integrity checks
- Performance optimization utilities

### System Configuration
- Application setup and configuration
- Environment-specific customizations
- System integration utilities

## Best Practices

1. **Test utilities** in development environment first
2. **Backup data** before running cleanup utilities
3. **Review changes** made by automated tools
4. **Document utility usage** and expected outcomes

## Notes

- Utilities are designed to be safe and reversible where possible
- Always review script contents before execution
- Keep utilities updated with application changes
- Consider scheduling regular utility runs for maintenance