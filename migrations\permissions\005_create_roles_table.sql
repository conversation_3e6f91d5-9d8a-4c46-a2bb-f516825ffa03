-- Migration: Create roles table for role template system
-- Administrative efficiency: Pre-defined permission sets for common roles
-- Date: 2025-01-08
-- Phase: Authentication System Implementation - Phase 1

-- Create roles table for role templates
CREATE TABLE IF NOT EXISTS roles (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name text UNIQUE NOT NULL,
    display_name text NOT NULL,
    description text,
    
    -- Permission templates stored as JSON arrays
    permissions jsonb DEFAULT '[]'::jsonb,
    
    -- Role management
    is_active boolean DEFAULT true,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    
    -- Constraints
    CONSTRAINT valid_role_name CHECK (LENGTH(TRIM(name)) > 0),
    CONSTRAINT valid_display_name CHECK (LENGTH(TRIM(display_name)) > 0),
    CONSTRAINT valid_role_permissions CHECK (jsonb_typeof(permissions) = 'array')
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_roles_name ON roles(name);
CREATE INDEX IF NOT EXISTS idx_roles_active ON roles(is_active);
CREATE INDEX IF NOT EXISTS idx_roles_permissions ON roles USING GIN(permissions);

-- Create updated_at trigger for roles
CREATE TRIGGER update_roles_updated_at 
    BEFORE UPDATE ON roles 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert default role templates
INSERT INTO roles (name, display_name, description, permissions) VALUES
(
    'admin', 
    'Administrator', 
    'Full system access with all permissions',
    '["system.full_access", "orders.view", "orders.create", "orders.update", "orders.delete", "products.view", "products.create", "products.update", "products.delete", "clients.view", "clients.create", "clients.update", "clients.delete", "analytics.view", "admin.users_create", "admin.permissions_assign"]'::jsonb
),
(
    'manager', 
    'Manager', 
    'Management level access - can view all data and manage most operations',
    '["orders.view", "orders.create", "orders.update", "products.view", "products.create", "products.update", "clients.view", "clients.create", "clients.update", "analytics.view"]'::jsonb
),
(
    'supervisor', 
    'Supervisor', 
    'Supervisory access - can view and create, limited editing',
    '["orders.view", "orders.create", "products.view", "clients.view", "clients.create", "analytics.view"]'::jsonb
),
(
    'viewer', 
    'Viewer', 
    'Read-only access to core business data',
    '["orders.view", "products.view", "clients.view"]'::jsonb
);

-- Add helpful comments
COMMENT ON TABLE roles IS 'Role templates for efficient permission assignment. Templates can be applied to authorized_users.';
COMMENT ON COLUMN roles.permissions IS 'JSON array of permission keys included in this role template';
COMMENT ON COLUMN roles.name IS 'Unique identifier for the role, used in authorized_users.role_template';
COMMENT ON COLUMN roles.display_name IS 'Human-readable name shown in admin interface';