-- Migration: Template Deletion and Product Deprecation System
-- Date: 2025-01-08
-- Purpose: Implement soft deletion for templates with comprehensive product deprecation handling

-- =============================================================================
-- STEP 1: Enhance calculation_templates table for soft deletion
-- =============================================================================

-- Add soft deletion and deprecation tracking columns
ALTER TABLE calculation_templates 
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS deleted_by TEXT,
ADD COLUMN IF NOT EXISTS deletion_reason TEXT;

-- Update status enum to include 'deleted' 
ALTER TABLE calculation_templates 
DROP CONSTRAINT IF EXISTS calculation_templates_status_check;

ALTER TABLE calculation_templates 
ADD CONSTRAINT calculation_templates_status_check 
CHECK (status IN ('active', 'inactive', 'draft', 'deleted'));

-- Index for soft deletion queries
CREATE INDEX IF NOT EXISTS idx_calculation_templates_deleted 
ON calculation_templates(deleted_at, status) 
WHERE deleted_at IS NULL;

-- =============================================================================
-- STEP 2: Create product deprecation tracking
-- =============================================================================

-- Track product deprecation when templates are deleted
CREATE TABLE IF NOT EXISTS product_deprecation_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    
    -- Product identification
    product_category_id UUID NOT NULL,
    product_type_id UUID NOT NULL, 
    size_id UUID NOT NULL,
    product_display_name TEXT,
    
    -- Deprecation details
    deprecation_reason TEXT NOT NULL,
    deprecated_at TIMESTAMPTZ DEFAULT NOW(),
    deprecated_by TEXT,
    
    -- Template context
    causing_template_id UUID,
    causing_template_name TEXT,
    causing_template_category TEXT,
    
    -- Status tracking
    is_deprecated BOOLEAN DEFAULT true,
    revival_required BOOLEAN DEFAULT true,
    
    -- Product state before deprecation
    previous_basic_cost_templates TEXT[],
    previous_additional_cost_templates TEXT[],
    remaining_basic_cost_templates TEXT[],
    remaining_additional_cost_templates TEXT[],
    
    -- Revival tracking
    revived_at TIMESTAMPTZ,
    revived_by TEXT,
    revival_method TEXT, -- 'template_replacement', 'manual_fix', 'bulk_operation'
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_product_deprecation_product 
ON product_deprecation_log(product_category_id, product_type_id, size_id);

CREATE INDEX idx_product_deprecation_status 
ON product_deprecation_log(is_deprecated, revival_required);

CREATE INDEX idx_product_deprecation_template 
ON product_deprecation_log(causing_template_id);

-- =============================================================================
-- STEP 3: Enhanced template applications tracking
-- =============================================================================

-- Add status tracking to template applications
ALTER TABLE template_applications 
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active' CHECK (status IN ('active', 'deprecated', 'replaced')),
ADD COLUMN IF NOT EXISTS deprecated_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS deprecated_reason TEXT,
ADD COLUMN IF NOT EXISTS replacement_template_id UUID REFERENCES calculation_templates(id);

-- Index for deprecated applications
CREATE INDEX idx_template_applications_status 
ON template_applications(status, is_active);

-- =============================================================================
-- STEP 4: Product health status view
-- =============================================================================

-- View to check product health and template coverage
CREATE OR REPLACE VIEW product_template_health AS
SELECT 
    pl.id as product_line_id,
    pl.category_id,
    pl.product_type_id,
    pl.size_id,
    
    -- Product identification
    COALESCE(pc.name, 'Unknown Category') as category_name,
    COALESCE(pt.name, 'Unknown Product') as product_name,
    COALESCE(ps.name, 'Unknown Size') as size_name,
    CONCAT(COALESCE(pc.name, 'Unknown'), ' - ', COALESCE(pt.name, 'Unknown'), ' (', COALESCE(ps.name, 'Unknown'), ')') as full_product_name,
    
    -- Template counts
    COUNT(CASE WHEN ta.status = 'active' AND ct.category = 'basic_cost' AND ct.status = 'active' THEN 1 END) as active_basic_templates,
    COUNT(CASE WHEN ta.status = 'active' AND ct.category = 'additional_cost' AND ct.status = 'active' THEN 1 END) as active_additional_templates,
    COUNT(CASE WHEN ta.status = 'deprecated' THEN 1 END) as deprecated_template_applications,
    
    -- Health indicators
    CASE 
        WHEN COUNT(CASE WHEN ta.status = 'active' AND ct.category = 'basic_cost' AND ct.status = 'active' THEN 1 END) = 0 THEN 'missing_basic_cost'
        WHEN COUNT(CASE WHEN ta.status = 'deprecated' THEN 1 END) > 0 THEN 'has_deprecated_templates'
        WHEN COUNT(CASE WHEN ta.status = 'active' AND ct.status = 'active' THEN 1 END) = 0 THEN 'no_active_templates'
        ELSE 'healthy'
    END as health_status,
    
    -- Deprecation info
    pdl.is_deprecated,
    pdl.deprecation_reason,
    pdl.deprecated_at,
    pdl.revival_required,
    
    -- Template lists
    array_agg(DISTINCT CASE WHEN ta.status = 'active' AND ct.category = 'basic_cost' AND ct.status = 'active' THEN ct.name END) 
        FILTER (WHERE ta.status = 'active' AND ct.category = 'basic_cost' AND ct.status = 'active') as active_basic_template_names,
    array_agg(DISTINCT CASE WHEN ta.status = 'active' AND ct.category = 'additional_cost' AND ct.status = 'active' THEN ct.name END) 
        FILTER (WHERE ta.status = 'active' AND ct.category = 'additional_cost' AND ct.status = 'active') as active_additional_template_names,
    array_agg(DISTINCT CASE WHEN ta.status = 'deprecated' THEN ct.name END) 
        FILTER (WHERE ta.status = 'deprecated') as deprecated_template_names

FROM product_line pl
LEFT JOIN product_categories pc ON pl.category_id = pc.id
LEFT JOIN product_types pt ON pl.product_type_id = pt.id  
LEFT JOIN product_sizes ps ON pl.size_id = ps.id
LEFT JOIN template_applications ta ON pl.category_id = ta.product_category_id 
    AND pl.product_type_id = ta.product_type_id 
    AND pl.size_id = ta.size_id 
    AND ta.is_active = true
LEFT JOIN calculation_templates ct ON ta.template_id = ct.id
LEFT JOIN product_deprecation_log pdl ON pl.category_id = pdl.product_category_id 
    AND pl.product_type_id = pdl.product_type_id 
    AND pl.size_id = pdl.size_id 
    AND pdl.is_deprecated = true

GROUP BY pl.id, pl.category_id, pl.product_type_id, pl.size_id, 
         pc.name, pt.name, ps.name, 
         pdl.is_deprecated, pdl.deprecation_reason, pdl.deprecated_at, pdl.revival_required;

-- =============================================================================
-- STEP 5: Template deletion function with product deprecation
-- =============================================================================

CREATE OR REPLACE FUNCTION soft_delete_template_with_deprecation(
    p_template_id UUID,
    p_deleted_by TEXT DEFAULT 'user',
    p_deletion_reason TEXT DEFAULT 'User requested deletion'
)
RETURNS TABLE (
    template_deleted BOOLEAN,
    applications_deprecated INT,
    products_deprecated INT,
    products_requiring_revival INT,
    deprecation_summary JSONB
) AS $$
DECLARE
    v_template_record RECORD;
    v_affected_products RECORD;
    v_applications_count INT := 0;
    v_products_deprecated INT := 0;
    v_products_requiring_revival INT := 0;
    v_summary JSONB := '[]'::jsonb;
BEGIN
    -- Get template details
    SELECT id, name, category, status INTO v_template_record
    FROM calculation_templates 
    WHERE id = p_template_id AND status = 'active';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Template not found or already deleted: %', p_template_id;
    END IF;
    
    -- Step 1: Soft delete the template
    UPDATE calculation_templates 
    SET 
        status = 'deleted',
        deleted_at = NOW(),
        deleted_by = p_deleted_by,
        deletion_reason = p_deletion_reason,
        updated_at = NOW()
    WHERE id = p_template_id;
    
    -- Step 2: Mark template applications as deprecated
    UPDATE template_applications 
    SET 
        status = 'deprecated',
        deprecated_at = NOW(),
        deprecated_reason = 'Template was deleted: ' || p_deletion_reason
    WHERE template_id = p_template_id AND status = 'active';
    
    GET DIAGNOSTICS v_applications_count = ROW_COUNT;
    
    -- Step 3: Analyze affected products and determine deprecation needs
    FOR v_affected_products IN
        SELECT DISTINCT 
            ta.product_category_id,
            ta.product_type_id,
            ta.size_id,
            CONCAT(COALESCE(pc.name, 'Unknown'), ' - ', COALESCE(pt.name, 'Unknown'), ' (', COALESCE(ps.name, 'Unknown'), ')') as product_name
        FROM template_applications ta
        LEFT JOIN product_categories pc ON ta.product_category_id = pc.id
        LEFT JOIN product_types pt ON ta.product_type_id = pt.id
        LEFT JOIN product_sizes ps ON ta.size_id = ps.id
        WHERE ta.template_id = p_template_id
    LOOP
        DECLARE
            v_remaining_basic_templates TEXT[];
            v_remaining_additional_templates TEXT[];
            v_needs_revival BOOLEAN := false;
            v_deprecation_reason TEXT;
        BEGIN
            -- Check remaining active templates for this product
            SELECT 
                array_agg(DISTINCT ct.name) FILTER (WHERE ct.category = 'basic_cost'),
                array_agg(DISTINCT ct.name) FILTER (WHERE ct.category = 'additional_cost')
            INTO v_remaining_basic_templates, v_remaining_additional_templates
            FROM template_applications ta
            JOIN calculation_templates ct ON ta.template_id = ct.id
            WHERE ta.product_category_id = v_affected_products.product_category_id
              AND ta.product_type_id = v_affected_products.product_type_id
              AND ta.size_id = v_affected_products.size_id
              AND ta.status = 'active'
              AND ct.status = 'active';
            
            -- Determine deprecation logic
            IF v_template_record.category = 'basic_cost' THEN
                IF COALESCE(array_length(v_remaining_basic_templates, 1), 0) = 0 THEN
                    v_needs_revival := true;
                    v_deprecation_reason := 'Product lost its basic cost template and needs revival';
                ELSE
                    v_deprecation_reason := 'Basic cost template removed but other basic templates remain';
                END IF;
            ELSE
                v_deprecation_reason := 'Additional cost template removed';
            END IF;
            
            -- Insert deprecation log
            INSERT INTO product_deprecation_log (
                product_category_id,
                product_type_id,
                size_id,
                product_display_name,
                deprecation_reason,
                deprecated_by,
                causing_template_id,
                causing_template_name,
                causing_template_category,
                revival_required,
                remaining_basic_cost_templates,
                remaining_additional_cost_templates
            ) VALUES (
                v_affected_products.product_category_id,
                v_affected_products.product_type_id,
                v_affected_products.size_id,
                v_affected_products.product_name,
                v_deprecation_reason,
                p_deleted_by,
                p_template_id,
                v_template_record.name,
                v_template_record.category,
                v_needs_revival,
                v_remaining_basic_templates,
                v_remaining_additional_templates
            );
            
            v_products_deprecated := v_products_deprecated + 1;
            
            IF v_needs_revival THEN
                v_products_requiring_revival := v_products_requiring_revival + 1;
            END IF;
            
            -- Add to summary
            v_summary := v_summary || jsonb_build_object(
                'product', v_affected_products.product_name,
                'reason', v_deprecation_reason,
                'needsRevival', v_needs_revival,
                'remainingBasicTemplates', COALESCE(v_remaining_basic_templates, ARRAY[]::TEXT[]),
                'remainingAdditionalTemplates', COALESCE(v_remaining_additional_templates, ARRAY[]::TEXT[])
            );
        END;
    END LOOP;
    
    -- Return results
    RETURN QUERY SELECT 
        true as template_deleted,
        v_applications_count as applications_deprecated,
        v_products_deprecated as products_deprecated,
        v_products_requiring_revival as products_requiring_revival,
        v_summary as deprecation_summary;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- STEP 6: Product revival function
-- =============================================================================

CREATE OR REPLACE FUNCTION revive_deprecated_product(
    p_product_category_id UUID,
    p_product_type_id UUID,
    p_size_id UUID,
    p_revival_method TEXT DEFAULT 'manual_fix',
    p_revived_by TEXT DEFAULT 'user'
)
RETURNS TABLE (
    product_revived BOOLEAN,
    revival_details JSONB
) AS $$
DECLARE
    v_deprecation_record RECORD;
BEGIN
    -- Find active deprecation record
    SELECT * INTO v_deprecation_record
    FROM product_deprecation_log
    WHERE product_category_id = p_product_category_id
      AND product_type_id = p_product_type_id
      AND size_id = p_size_id
      AND is_deprecated = true
    ORDER BY deprecated_at DESC
    LIMIT 1;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'No active deprecation found for this product';
    END IF;
    
    -- Mark as revived
    UPDATE product_deprecation_log
    SET 
        is_deprecated = false,
        revival_required = false,
        revived_at = NOW(),
        revived_by = p_revived_by,
        revival_method = p_revival_method,
        updated_at = NOW()
    WHERE id = v_deprecation_record.id;
    
    RETURN QUERY SELECT 
        true as product_revived,
        jsonb_build_object(
            'productName', v_deprecation_record.product_display_name,
            'deprecatedAt', v_deprecation_record.deprecated_at,
            'revivedAt', NOW(),
            'revivalMethod', p_revival_method
        ) as revival_details;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- STEP 7: Helper functions
-- =============================================================================

-- Function to get deprecated products requiring revival
CREATE OR REPLACE FUNCTION get_products_requiring_revival()
RETURNS TABLE (
    product_category_id UUID,
    product_type_id UUID,
    size_id UUID,
    product_display_name TEXT,
    deprecation_reason TEXT,
    deprecated_at TIMESTAMPTZ,
    causing_template_name TEXT,
    remaining_basic_templates TEXT[],
    remaining_additional_templates TEXT[]
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pdl.product_category_id,
        pdl.product_type_id,
        pdl.size_id,
        pdl.product_display_name,
        pdl.deprecation_reason,
        pdl.deprecated_at,
        pdl.causing_template_name,
        pdl.remaining_basic_cost_templates,
        pdl.remaining_additional_cost_templates
    FROM product_deprecation_log pdl
    WHERE pdl.is_deprecated = true 
      AND pdl.revival_required = true
    ORDER BY pdl.deprecated_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to validate template component (used by template validation)
CREATE OR REPLACE FUNCTION validate_template_component(component_code TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    -- Check if component exists in production_cost_components
    IF EXISTS (
        SELECT 1 FROM production_cost_components 
        WHERE code = component_code AND status = 'active'
    ) THEN
        RETURN true;
    END IF;
    
    -- Check if it's a valid product code (for combination templates)
    IF EXISTS (
        SELECT 1 FROM product_line pl
        JOIN product_categories pc ON pl.category_id = pc.id
        JOIN product_types pt ON pl.product_type_id = pt.id
        WHERE pc.code || '_' || pt.code = component_code
           OR pt.code = component_code
    ) THEN
        RETURN true;
    END IF;
    
    RETURN false;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- STEP 8: Update existing views
-- =============================================================================

-- Update template_details_view to include deletion info
CREATE OR REPLACE VIEW template_details_view AS
SELECT 
    ct.*,
    COUNT(ta.id) FILTER (WHERE ta.is_active = true AND ta.status = 'active') as active_applications,
    COUNT(ta.id) FILTER (WHERE ta.status = 'deprecated') as deprecated_applications,
    COUNT(DISTINCT CONCAT(ta.product_category_id, '_', ta.product_type_id, '_', ta.size_id)) 
        FILTER (WHERE ta.is_active = true AND ta.status = 'active') as unique_products_count
FROM calculation_templates ct
LEFT JOIN template_applications ta ON ct.id = ta.template_id
GROUP BY ct.id;

-- =============================================================================
-- STEP 9: Permissions and indexes
-- =============================================================================

-- Ensure proper permissions (adjust based on your auth setup)
-- GRANT SELECT, INSERT, UPDATE ON product_deprecation_log TO authenticated;
-- GRANT EXECUTE ON FUNCTION soft_delete_template_with_deprecation TO authenticated;
-- GRANT EXECUTE ON FUNCTION revive_deprecated_product TO authenticated;

-- Final indexes for performance
CREATE INDEX IF NOT EXISTS idx_template_applications_product_combo 
ON template_applications(product_category_id, product_type_id, size_id, status);

-- Migration completion message
DO $$
BEGIN
    RAISE NOTICE 'Template deletion and product deprecation system migration completed!';
    RAISE NOTICE 'New features:';
    RAISE NOTICE '- Soft deletion for templates with full audit trail';
    RAISE NOTICE '- Product deprecation tracking when templates are deleted';
    RAISE NOTICE '- Revival mechanism for deprecated products';
    RAISE NOTICE '- Health monitoring for products missing basic cost templates';
    RAISE NOTICE 'Use soft_delete_template_with_deprecation() for safe template deletion';
    RAISE NOTICE 'Use product_template_health view to monitor product health';
END
$$;