# Permission Migrations

Authentication, authorization, security policies, and user management system changes.

## Files in this directory:

### Permission System Core
- `001_create_permissions_system.sql` - Initial permission system setup
- `002_seed_permissions_data.sql` - Permission data seeding
- `003_add_delete_permissions.sql` - Delete permission functionality

### User Management
- `004_create_authorized_users_table.sql` - Authorized users table
- `005_create_roles_table.sql` - Create roles system
- `010_safe_profile_enhancements.sql` - Safe profile enhancements
- `011_setup_automatic_profile_creation.sql` - Automatic profile creation setup
- `012_add_auth_user_id_field.sql` - Auth user ID field
- `013_create_user_audit_logs.sql` - User audit logging system

### Row Level Security (RLS)
- `006_secure_rls_basic.sql` - Basic RLS security setup
- `007_comprehensive_rls_granular_policies.sql` - Comprehensive RLS policies
- `008_views_rls_security.sql` - RLS for database views
- `009_fix_authorized_users_rls.sql` - Fix authorized users RLS

### Permission System V2
- `014_permission_system_v2_migration.sql` - Permission system v2 migration
- `015_modernize_permissions_system_v2.sql` - Permission system v2 modernization

## Execution Order

Execute permission migrations **after** schema and data migrations:

1. Permission system setup (`001_*` through `005_*`)
2. Basic security (`006_secure_rls_basic.sql`)
3. Advanced security (`007_*`, `008_*`, `009_*`)
4. Profile management (`010_*`, `011_*`, `012_*`)
5. Audit system (`013_*`)
6. System v2 upgrade (`014_*`, `015_*`)

## Dependencies

- Requires schema migrations for user-related tables
- Depends on data migrations for basic application setup
- RLS policies require existing tables and data structure

## Security Considerations

- **Critical**: Test all RLS policies in development first
- Verify user access patterns before deploying
- Monitor performance impact of RLS policies
- Keep audit logs for security compliance

## Notes

- These migrations implement security controls
- May impact application performance (especially RLS)
- Requires thorough testing of user workflows
- Consider creating test users to validate permissions