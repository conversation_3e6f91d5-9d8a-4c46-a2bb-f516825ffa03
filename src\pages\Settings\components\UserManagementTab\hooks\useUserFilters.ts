import { useState, useMemo } from 'react'
import type { AuthorizedUser } from '../../../../../services/admin'
import type { UserFilters } from '../types'

export function useUserFilters(users: AuthorizedUser[]) {
  const [filters, setFilters] = useState<UserFilters>({
    search: '',
    activeFilter: null
  })

  const filteredUsers = useMemo(() => {
    return users.filter(user => {
      const matchesSearch = !filters.search || 
        user.email.toLowerCase().includes(filters.search.toLowerCase()) ||
        `${user.first_name} ${user.last_name}`.toLowerCase().includes(filters.search.toLowerCase()) ||
        user.department?.toLowerCase().includes(filters.search.toLowerCase())
      
      const matchesActive = filters.activeFilter === null || user.is_active === filters.activeFilter
      
      return matchesSearch && matchesActive
    })
  }, [users, filters])

  const updateSearch = (search: string) => {
    setFilters(prev => ({ ...prev, search }))
  }

  const updateActiveFilter = (activeFilter: boolean | null) => {
    setFilters(prev => ({ ...prev, activeFilter }))
  }

  return {
    filters,
    filteredUsers,
    updateSearch,
    updateActiveFilter
  }
}