# Developer Guide: New Permission System

## Quick Start

### Basic Permission Check
```typescript
import { usePermissions } from '@/hooks/permissions/usePermissions';

const MyComponent = () => {
  const { hasPermission } = usePermissions();
  
  return (
    <div>
      {hasPermission('orders.create') && (
        <Button>Create Order</Button>
      )}
    </div>
  );
};
```

### Multiple Permission Check (OR Logic)
```typescript
// User needs ANY of these permissions
{hasPermission(['orders.edit', 'orders.delete']) && (
  <EditActions />
)}
```

### All Permissions Check (AND Logic)  
```typescript
const { hasAllPermissions } = usePermissions();

// User needs ALL of these permissions
{hasAllPermissions(['orders.edit', 'analytics.export']) && (
  <AdvancedFeatures />
)}
```

## Permission Reference

### Resource Categories

#### Orders (4 permissions)
- `orders.view` - View order information and history
- `orders.create` - Create new orders
- `orders.edit` - Modify existing orders (replaces old granular permissions)
- `orders.delete` - Delete orders permanently

#### Products (4 permissions)
- `products.view` - View product catalog
- `products.create` - Add new products
- `products.edit` - Modify existing products (includes pricing)
- `products.delete` - Delete products permanently

#### Clients (4 permissions)
- `clients.view` - View client information
- `clients.create` - Add new clients
- `clients.edit` - Modify client information
- `clients.delete` - Delete clients permanently

#### Analytics (2 permissions)
- `analytics.view` - View reports and analytics
- `analytics.export` - Export analytics data

#### Settings (2 permissions)
- `settings.view` - View system settings
- `settings.edit` - Modify system settings

#### Admin (3 permissions)
- `admin.users` - Manage user accounts
- `admin.permissions` - Assign permissions to users
- `system.admin` - Full system access (grants everything)

## Role Reference

### Viewer Role
```typescript
permissions: ['orders.view', 'products.view', 'clients.view', 'analytics.view']
```
**Use case**: Read-only access for stakeholders, external users

### Operator Role
```typescript
permissions: [
  'orders.view', 'orders.create', 'orders.edit', 
  'products.view',
  'clients.view', 'clients.create', 'clients.edit', 
  'analytics.view'
]
```
**Use case**: Daily operations staff, data entry, customer service

### Manager Role
```typescript
permissions: [
  'orders.view', 'orders.create', 'orders.edit', 'orders.delete',
  'products.view', 'products.create', 'products.edit',
  'clients.view', 'clients.create', 'clients.edit', 'clients.delete',
  'analytics.view', 'analytics.export',
  'settings.view'
]
```
**Use case**: Department managers, team leads

### Admin Role
```typescript
permissions: ['system.admin']
```
**Use case**: System administrators, IT staff
**Note**: `system.admin` automatically grants ALL permissions

## Common Patterns

### Route Protection
```typescript
import { usePermissions } from '@/hooks/permissions/usePermissions';
import { Navigate } from 'react-router-dom';

const ProtectedRoute = ({ children, permission }) => {
  const { hasPermission, loading } = usePermissions();
  
  if (loading) return <LoadingSpinner />;
  
  if (!hasPermission(permission)) {
    return <Navigate to="/access-denied" replace />;
  }
  
  return children;
};

// Usage
<ProtectedRoute permission="orders.view">
  <OrdersList />
</ProtectedRoute>
```

### Conditional UI Elements
```typescript
const ActionButtons = ({ order }) => {
  const { hasPermission, isAdmin } = usePermissions();
  
  return (
    <ButtonGroup>
      {hasPermission('orders.edit') && (
        <Button variant="primary">Edit</Button>
      )}
      
      {hasPermission('orders.delete') && (
        <Button variant="danger">Delete</Button>
      )}
      
      {/* Admin bypass example */}
      {(hasPermission('analytics.export') || isAdmin) && (
        <Button variant="secondary">Export</Button>
      )}
    </ButtonGroup>
  );
};
```

### Form Field Permissions
```typescript
const OrderForm = () => {
  const { hasPermission } = usePermissions();
  const canEditFinancials = hasPermission('orders.edit');
  
  return (
    <form>
      <Input name="customerName" />
      <Input name="description" />
      
      {canEditFinancials && (
        <>
          <Input name="amount" type="number" />
          <Select name="paymentTerms" />
        </>
      )}
      
      <Button type="submit" disabled={!hasPermission('orders.create')}>
        Create Order
      </Button>
    </form>
  );
};
```

### API Integration
```typescript
const OrderService = {
  async createOrder(orderData) {
    const { hasPermission } = usePermissions();
    
    // Client-side check (UX improvement)
    if (!hasPermission('orders.create')) {
      throw new Error('Insufficient permissions');
    }
    
    // Server will also validate permissions
    return api.post('/orders', orderData);
  }
};
```

## Migration from Old System

### Permission Mapping

| Old Permission | New Permission | Notes |
|----------------|----------------|-------|
| `pages.orders_access` | `orders.view` | Page access → resource access |
| `orders.general_info_edit` | `orders.edit` | Consolidated edit permissions |
| `orders.items_edit` | `orders.edit` | Consolidated edit permissions |
| `orders.payments_manage` | `orders.edit` | Consolidated edit permissions |
| `products.pricing_edit` | `products.edit` | Consolidated edit permissions |
| `pages.products_access` | `products.view` | Page access → resource access |
| `pages.clients_access` | `clients.view` | Page access → resource access |
| `system.full_access` | `system.admin` | Renamed for clarity |

### Component Migration Examples

**Before (Old System):**
```typescript
import { hasPermissionWithHierarchy } from '@/utils/permissionHierarchy';
import { useAuth } from '@/contexts/AuthContext';

const OrderActions = () => {
  const { permissions } = useAuth();
  
  const canEdit = hasPermissionWithHierarchy(permissions, 'orders.general_info_edit');
  const canManagePayments = hasPermissionWithHierarchy(permissions, 'orders.payments_manage');
  
  return (
    <>
      {canEdit && <EditButton />}
      {canManagePayments && <PaymentButton />}
    </>
  );
};
```

**After (New System):**
```typescript
import { usePermissions } from '@/hooks/permissions/usePermissions';

const OrderActions = () => {
  const { hasPermission } = usePermissions();
  
  return (
    <>
      {hasPermission('orders.edit') && <EditButton />}
      {hasPermission('orders.edit') && <PaymentButton />}
    </>
  );
};
```

## Testing Patterns

### Unit Testing Permissions
```typescript
import { render } from '@testing-library/react';
import { PermissionServiceV2 } from '@/services/permissions/PermissionServiceV2';

// Mock permissions for testing
const mockPermissions = (permissions: string[]) => {
  jest.spyOn(PermissionServiceV2, 'hasPermission')
    .mockImplementation((userPerms, required) => 
      permissions.some(p => 
        Array.isArray(required) ? required.includes(p) : required === p
      )
    );
};

describe('OrderActions', () => {
  it('shows edit button for users with orders.edit permission', () => {
    mockPermissions(['orders.edit']);
    
    const { getByText } = render(<OrderActions />);
    expect(getByText('Edit')).toBeInTheDocument();
  });
  
  it('hides edit button for users without orders.edit permission', () => {
    mockPermissions(['orders.view']);
    
    const { queryByText } = render(<OrderActions />);
    expect(queryByText('Edit')).not.toBeInTheDocument();
  });
});
```

### Integration Testing with Different Roles
```typescript
const testWithRole = async (roleName: string, expectedPermissions: string[]) => {
  // Set up test user with role
  await createTestUser({ role: roleName });
  
  // Test each expected permission
  for (const permission of expectedPermissions) {
    const result = await PermissionServiceV2.hasPermission(
      await getUserPermissions(),
      permission
    );
    expect(result).toBe(true);
  }
};

describe('Role Permissions', () => {
  it('viewer role has correct permissions', async () => {
    await testWithRole('viewer', ['orders.view', 'products.view', 'clients.view']);
  });
  
  it('manager role can delete records', async () => {
    await testWithRole('manager', ['orders.delete', 'products.edit', 'clients.delete']);
  });
});
```

## Performance Considerations

### Caching
- Permission checks are cached for 5 minutes
- Cache is automatically cleared when user permissions change
- Use `refreshPermissions()` to manually clear cache

### Optimization Tips
```typescript
// ✅ Good: Check once, use multiple times
const OrdersList = () => {
  const { hasPermission } = usePermissions();
  const canEdit = hasPermission('orders.edit');
  const canDelete = hasPermission('orders.delete');
  
  return orders.map(order => (
    <OrderItem key={order.id} canEdit={canEdit} canDelete={canDelete} />
  ));
};

// ❌ Avoid: Multiple permission checks in render loops  
const OrdersList = () => {
  const { hasPermission } = usePermissions();
  
  return orders.map(order => (
    <OrderItem 
      key={order.id}
      canEdit={hasPermission('orders.edit')}  // Called for every item!
      canDelete={hasPermission('orders.delete')}  // Called for every item!
    />
  ));
};
```

## Debugging

### Permission Check Logging
```typescript
// Enable in development
localStorage.setItem('debug_permissions', 'true');

// View permission checks in console
const { hasPermission } = usePermissions();
console.log('User permissions:', permissions);
console.log('Checking permission:', 'orders.create');
console.log('Result:', hasPermission('orders.create'));
```

### Common Issues

**Issue**: User can see page but buttons don't work
```typescript
// Fix: Ensure consistent permission checking
const canAccessOrders = hasPermission('orders.view');
const canCreateOrders = hasPermission('orders.create');

// Not: mixing old and new permission patterns
```

**Issue**: Admin users don't have access to everything
```typescript
// Fix: Ensure system.admin is assigned correctly
const { isAdmin } = usePermissions();
// isAdmin checks for 'system.admin' permission
```

## Best Practices

1. **Use resource-action naming**: Always follow `resource.action` pattern
2. **Check permissions at component level**: Don't rely on route-level checks alone
3. **Provide fallback UI**: Show appropriate messages when permissions are denied
4. **Test with different roles**: Verify functionality works for all user types
5. **Cache appropriately**: Use the built-in caching, don't implement your own
6. **Stay consistent**: Use the same permission for the same action across components

## Troubleshooting

**Permissions not updating**: Call `refreshPermissions()`
**Component not re-rendering**: Ensure you're using the hook correctly
**Permission denied unexpectedly**: Check if user role has the required permission
**Performance issues**: Avoid permission checks in render loops

This new system eliminates complexity while providing all the functionality needed for a production application.