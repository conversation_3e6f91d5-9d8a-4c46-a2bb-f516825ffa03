import { supabase } from '../../lib/supabase';
import type { AuthorizedUser } from '../admin';

/**
 * Direct Auth Service
 * 
 * Handles post-authentication operations using direct RLS queries
 * instead of Edge Functions to eliminate performance bottlenecks.
 * 
 * Uses the new RLS functions created in migration 039:
 * - is_authenticated() 
 * - user_has_permission()
 * - is_admin()
 */
export class DirectAuthService {
  /**
   * Get current user's authorization data using direct RLS query
   * This replaces Edge Function calls for authenticated users
   */
  static async getCurrentUserAuthorization(): Promise<AuthorizedUser | null> {
    try {
      // Get current user first to avoid race conditions
      const { data: userData, error: userError } = await supabase.auth.getUser();
      
      if (userError || !userData.user?.id) {
        return null;
      }

      // Query authorized_users table using RLS
      // The new RLS policies allow users to see their own record
      const { data, error } = await supabase
        .from('authorized_users')
        .select(`
          id,
          email,
          first_name,
          last_name,
          department,
          permissions,
          role_template,
          is_active,
          notes,
          created_at,
          updated_at
        `)
        .eq('auth_user_id', userData.user.id)
        .eq('is_active', true)
        .single();

      if (error) {
        // User not found or not authorized
        if (error.code === 'PGRST116') {
          return null;
        }
        throw error;
      }

      return data as AuthorizedUser;
    } catch (error) {
      console.error('Error getting current user authorization:', error);
      return null;
    }
  }

  /**
   * Update last login timestamp using direct RLS
   * This replaces the Edge Function call for last login updates
   */
  static async updateLastLogin(): Promise<void> {
    try {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user?.id) return;

      const { error } = await supabase
        .from('authorized_users')
        .update({ 
          last_login_at: new Date().toISOString() 
        })
        .eq('auth_user_id', user.user.id);

      if (error) {
        console.error('Error updating last login:', error);
        // Not critical - don't throw
      }
    } catch (error) {
      console.error('Error updating last login:', error);
      // Not critical - don't throw
    }
  }

  /**
   * Check if current user has a specific permission
   * Uses direct RLS query instead of Edge Function
   */
  static async hasPermission(permission: string): Promise<boolean> {
    try {
      const authData = await this.getCurrentUserAuthorization();
      if (!authData) return false;

      return authData.permissions.includes(permission) || 
             authData.permissions.includes('system.full_access');
    } catch (error) {
      console.error('Error checking permission:', error);
      return false;
    }
  }

  /**
   * Get current user's permissions list
   * Uses direct RLS query for fast permission lookup
   */
  static async getUserPermissions(): Promise<string[]> {
    try {
      const authData = await this.getCurrentUserAuthorization();
      return authData?.permissions || [];
    } catch (error) {
      console.error('Error getting user permissions:', error);
      return [];
    }
  }
}