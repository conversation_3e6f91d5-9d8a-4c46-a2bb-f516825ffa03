-- Migration: Add enable_notes column to order_items table
-- Date: 2025-12-06
-- Description: Adds a boolean column to control whether notes are enabled for individual order items

-- Add the enable_notes column with default value true
ALTER TABLE order_items 
ADD COLUMN IF NOT EXISTS enable_notes BOOLEAN DEFAULT TRUE;

-- Update existing records to have notes enabled by default
UPDATE order_items 
SET enable_notes = TRUE 
WHERE enable_notes IS NULL;

-- Add constraint to ensure enable_notes is not null
ALTER TABLE order_items 
ALTER COLUMN enable_notes SET NOT NULL;

-- Add comment to document the column purpose
COMMENT ON COLUMN order_items.enable_notes IS 'Controls whether item-specific notes are enabled for this order item. When false, the notes UI is hidden.';