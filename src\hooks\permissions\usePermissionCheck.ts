/**
 * usePermissionCheck Hook - Simple permission check hook
 * 
 * Following CLAUDE.md guidelines:
 * - Single responsibility: check one permission
 * - Lightweight wrapper around usePermissions
 * - <50 lines
 */

import { usePermissions } from './usePermissions';
import type { PermissionKey } from '../../types/permissions.types';

// ============================================================================
// SINGLE PERMISSION CHECK HOOK
// ============================================================================

/**
 * Check if user has a specific permission
 * Lightweight hook for single permission checks
 */
export const usePermissionCheck = (permission: PermissionKey): boolean => {
  const { checkPermission } = usePermissions();
  return checkPermission(permission);
};