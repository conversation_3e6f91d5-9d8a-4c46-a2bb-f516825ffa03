-- =============================================================================
-- PERMISSIONS SYSTEM DATA SEEDING
-- Migration: 028_seed_permissions_data.sql
-- 
-- Seeds the permissions registry with 20 total permissions:
-- - 10 Page-level permissions 
-- - 10 Strategic feature permissions
-- =============================================================================

-- Insert Page Access Permissions (10 permissions)
INSERT INTO permissions (key, name, description, category, is_active) VALUES
('pages.orders_access', 'Orders Page Access', 'Complete access to orders page and all order management features', 'Page Access', TRUE),
('pages.products_access', 'Products Page Access', 'Complete access to products page including product management', 'Page Access', TRUE),
('pages.clients_access', 'Clients Page Access', 'Complete access to clients page including client management', 'Page Access', TRUE),
('pages.production_cost_access', 'Production Cost System Access', 'Complete access to the entire production cost system', 'Page Access', TRUE),
('pages.analytics_overview_access', 'Analytics Overview Access', 'Complete access to main analytics overview dashboard', 'Analytics Access', TRUE),
('pages.analytics_sales_access', 'Sales Analytics Access', 'Complete access to sales analytics dashboard', 'Analytics Access', TRUE),
('pages.analytics_production_access', 'Production Analytics Access', 'Complete access to production analytics', 'Analytics Access', TRUE),
('pages.analytics_general_access', 'General Analytics Access', 'Complete access to general business analytics', 'Analytics Access', TRUE),
('pages.settings_access', 'Settings Access', 'Access to system settings and configuration', 'Administrative Controls', TRUE),
('pages.user_management_access', 'User Management Access', 'Access to user and permission management interface', 'Administrative Controls', TRUE)
ON CONFLICT (key) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  category = EXCLUDED.category,
  updated_at = NOW();

-- Insert Strategic Feature Permissions (10 permissions)
INSERT INTO permissions (key, name, description, category, is_active) VALUES
('orders.create', 'Create Orders', 'Create new orders', 'Operational Controls', TRUE),
('orders.general_info_edit', 'Edit Order General Information', 'Modify order basic details (client, dates, status)', 'Order Component Controls', TRUE),
('orders.items_edit', 'Edit Order Items', 'Add, modify, or remove items from orders', 'Order Component Controls', TRUE),
('orders.payments_manage', 'Manage Order Payments', 'Handle payment transactions and financial data', 'Financial Controls', TRUE),
('products.pricing_edit', 'Edit Product Pricing', 'Modify product prices and pricing rules', 'Financial Controls', TRUE),
('clients.create', 'Create Clients', 'Add new client records', 'Operational Controls', TRUE),
('analytics.export', 'Export Analytics Data', 'Download and export analytics data', 'Data Security', TRUE),
('admin.users_create', 'Create Users', 'Add new users to the system', 'Administrative Controls', TRUE),
('admin.permissions_assign', 'Assign Permissions', 'Modify user permissions and roles', 'Administrative Controls', TRUE),
('system.full_access', 'Full System Access', 'Complete administrative access to all system functions', 'System Administration', TRUE)
ON CONFLICT (key) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  category = EXCLUDED.category,
  updated_at = NOW();

-- Verify all permissions were inserted
DO $$
DECLARE
    permission_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO permission_count FROM permissions WHERE is_active = TRUE;
    
    IF permission_count < 20 THEN
        RAISE EXCEPTION 'Expected 20 permissions but only found %', permission_count;
    END IF;
    
    RAISE NOTICE 'SUCCESS: % permissions seeded successfully', permission_count;
END $$;

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_permissions_category ON permissions(category);
CREATE INDEX IF NOT EXISTS idx_permissions_active ON permissions(is_active);
CREATE INDEX IF NOT EXISTS idx_permissions_key_active ON permissions(key, is_active);

-- Grant necessary permissions to authenticated users
GRANT SELECT ON permissions TO authenticated;

COMMENT ON TABLE permissions IS 'Registry of all available permissions in the system - 20 total permissions (10 page-level + 10 feature-level)';