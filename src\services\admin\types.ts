export interface AuthorizedUser {
  readonly id: string
  readonly email: string
  readonly first_name: string
  readonly last_name: string
  readonly department?: string | null
  readonly permissions: readonly string[]
  readonly role_template?: string | null
  readonly is_active: boolean
  readonly invited_by?: string | null
  readonly invited_at: string
  readonly first_login_at?: string | null
  readonly last_login_at?: string | null
  readonly notes?: string | null
  readonly created_at: string
  readonly updated_at: string
}

export interface CreateUserRequest {
  readonly email: string
  readonly first_name: string
  readonly last_name: string
  readonly department?: string
  readonly permissions: readonly string[]
  readonly role_template?: string
  readonly notes?: string
}

export interface UpdateUserRequest {
  readonly first_name?: string
  readonly last_name?: string
  readonly department?: string
  readonly permissions?: readonly string[]
  readonly role_template?: string
  readonly is_active?: boolean
  readonly notes?: string
}

export interface UserStats {
  readonly total_users: number
  readonly active_users: number
  readonly inactive_users: number
  readonly recent_logins: number
}

export interface UserFilters {
  limit?: number
  offset?: number
  search?: string
  role_template?: string
  is_active?: boolean
  department?: string
}