/**
 * Permissions Components - Export Barrel
 * 
 * Following CLAUDE.md guidelines:
 * - Use barrel exports for clean imports
 * - Absolute imports for cross-feature dependencies
 */

export { PermissionGuard } from './PermissionGuard';
export { PermissionWrapper } from './PermissionWrapper';

// Re-export types for convenience
export type {
  PermissionGuardProps,
  PermissionWrapperProps,
  PermissionKey
} from '../../types/permissions.types';