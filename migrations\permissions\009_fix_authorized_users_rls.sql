-- Fix authorized_users RLS policy issue
-- The security advisor detected that authorized_users has RLS enabled but no policies

-- Create admin-only policies for authorized_users table
-- This table should only be accessible to users with full system access

-- Policy for reading authorized users data (admin/system access only)
CREATE POLICY "authorized_users_admin_select" ON authorized_users
  FOR SELECT TO authenticated
  USING (
    -- Only allow access if user has system.full_access permission
    (SELECT permissions FROM authorized_users 
     WHERE email = get_current_user_email() AND is_active = true) ? 'system.full_access'
  );

-- Policy for inserting new authorized users (admin/system access only)
CREATE POLICY "authorized_users_admin_insert" ON authorized_users
  FOR INSERT TO authenticated
  WITH CHECK (
    -- Only allow access if user has system.full_access permission
    (SELECT permissions FROM authorized_users 
     WHERE email = get_current_user_email() AND is_active = true) ? 'system.full_access'
  );

-- Policy for updating authorized users (admin/system access only)
CREATE POLICY "authorized_users_admin_update" ON authorized_users
  FOR UPDATE TO authenticated
  USING (
    -- Only allow access if user has system.full_access permission
    (SELECT permissions FROM authorized_users 
     WHERE email = get_current_user_email() AND is_active = true) ? 'system.full_access'
  )
  WITH CHECK (
    -- Only allow access if user has system.full_access permission
    (SELECT permissions FROM authorized_users 
     WHERE email = get_current_user_email() AND is_active = true) ? 'system.full_access'
  );

-- Policy for deleting authorized users (admin/system access only)
CREATE POLICY "authorized_users_admin_delete" ON authorized_users
  FOR DELETE TO authenticated
  USING (
    -- Only allow access if user has system.full_access permission
    (SELECT permissions FROM authorized_users 
     WHERE email = get_current_user_email() AND is_active = true) ? 'system.full_access'
  );

-- Add comment to document the security approach
COMMENT ON TABLE authorized_users IS 'Critical system table: Only accessible to users with system.full_access permission. Contains authorized user accounts and their permissions.';

RAISE NOTICE 'Fixed authorized_users RLS policies - now requires system.full_access permission for all operations';