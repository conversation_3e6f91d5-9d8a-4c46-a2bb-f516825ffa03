import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

// Types matching the frontend UserStateDetectionService
interface DetectUserStateRequest {
  email: string;
}

interface AuthorizedUser {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  department?: string;
  permissions: string[];
  role_template?: string;
  is_active: boolean;
}

interface AuthUser {
  id: string;
  email: string;
  confirmed_at: string | null;
  created_at: string;
}

interface Profile {
  id: string;
  email: string;
  full_name: string | null;
  role: string | null;
}

type UserAuthState = 
  | 'unauthorized'        
  | 'authorized_only'     
  | 'signup_started'      
  | 'signup_incomplete'   
  | 'account_complete';   

interface UserStateResult {
  state: UserAuthState;
  authorizedUser?: AuthorizedUser;
  authUser?: AuthUser;
  profile?: Profile;
  message: string;
  canSignup: boolean;
  canLogin: boolean;
  nextAction: string;
}

/**
 * User State Detection Edge Function
 * 
 * Comprehensive user authentication state detection using SERVICE_ROLE_KEY
 * Bypasses RLS to reliably determine user state across all tables
 * Following CLAUDE.md: <250 lines, single responsibility, production-grade
 */
serve(async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        status: 405, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }

  try {
    // Get environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const serviceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

    if (!supabaseUrl || !serviceRoleKey) {
      console.error('Missing required environment variables');
      return new Response(
        JSON.stringify({ 
          error: 'Server configuration error' 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Create admin Supabase client with SERVICE_ROLE_KEY
    const supabaseAdmin = createClient(supabaseUrl, serviceRoleKey, {
      auth: {
        persistSession: false,
        autoRefreshToken: false,
        detectSessionInUrl: false,
      },
    });

    // Parse request body
    let requestBody: DetectUserStateRequest;
    try {
      requestBody = await req.json();
    } catch {
      return new Response(
        JSON.stringify({ error: 'Invalid request body' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    const { email } = requestBody;

    // Validate email input
    if (!email || typeof email !== 'string') {
      return new Response(
        JSON.stringify({ error: 'Email is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    const normalizedEmail = email.toLowerCase().trim();

    // Step 1: Check authorized_users table (permission to participate)
    const { data: authorizedUser, error: authError } = await supabaseAdmin
      .from('authorized_users')
      .select(`
        id,
        email,
        first_name,
        last_name,
        department,
        permissions,
        role_template,
        is_active
      `)
      .eq('email', normalizedEmail)
      .eq('is_active', true)
      .single();

    if (authError) {
      if (authError.code === 'PGRST116') { // Not found
        const result: UserStateResult = {
          state: 'unauthorized',
          message: 'Email not authorized for this system',
          canSignup: false,
          canLogin: false,
          nextAction: 'Contact administrator for access'
        };
        return new Response(JSON.stringify(result), {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }
      
      console.error('Database error checking authorized_users:', authError);
      return new Response(
        JSON.stringify({ error: 'Failed to check user authorization' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Step 2: Check auth.users table (Supabase authentication record)
    const { data: authUsers, error: authUsersError } = await supabaseAdmin.auth.admin.listUsers();
    
    if (authUsersError) {
      console.error('Error checking auth.users:', authUsersError);
      return new Response(
        JSON.stringify({ error: 'Failed to check authentication status' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    const authUser = authUsers.users?.find(u => u.email?.toLowerCase() === normalizedEmail);

    // Step 3: Check profiles table
    const { data: profiles, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('id, email, full_name, role, created_at')
      .eq('email', normalizedEmail);

    if (profileError) {
      console.error('Error checking profiles:', profileError);
      // Don't throw - treat as no profile exists
    }

    const profile = profiles && profiles.length > 0 ? profiles[0] : null;

    // Step 4: Determine state based on comprehensive analysis
    if (!authUser) {
      // No auth.users record = ready for signup
      const result: UserStateResult = {
        state: 'authorized_only',
        authorizedUser,
        message: 'Ready to create account',
        canSignup: true,
        canLogin: false,
        nextAction: 'Start signup process'
      };
      return new Response(JSON.stringify(result), {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    if (!authUser.confirmed_at) {
      // Auth user exists but unconfirmed
      const result: UserStateResult = {
        state: 'signup_started',
        authorizedUser,
        authUser: {
          id: authUser.id,
          email: authUser.email || '',
          confirmed_at: authUser.confirmed_at,
          created_at: authUser.created_at
        },
        message: 'Email confirmation required',
        canSignup: true,
        canLogin: false,
        nextAction: 'Check email and confirm account'
      };
      return new Response(JSON.stringify(result), {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    if (!profile || !profile.full_name || !profile.role) {
      // Confirmed auth user but incomplete profile
      const result: UserStateResult = {
        state: 'signup_incomplete',
        authorizedUser,
        authUser: {
          id: authUser.id,
          email: authUser.email || '',
          confirmed_at: authUser.confirmed_at,
          created_at: authUser.created_at
        },
        profile,
        message: 'Account exists but profile incomplete',
        canSignup: true, // Can complete signup
        canLogin: true,  // Allow login for admin roles
        nextAction: 'Complete profile setup'
      };
      return new Response(JSON.stringify(result), {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Complete account - ready for login
    const result: UserStateResult = {
      state: 'account_complete',
      authorizedUser,
      authUser: {
        id: authUser.id,
        email: authUser.email || '',
        confirmed_at: authUser.confirmed_at,
        created_at: authUser.created_at
      },
      profile,
      message: 'Account fully configured',
      canSignup: false,
      canLogin: true,
      nextAction: 'Use login form'
    };

    return new Response(JSON.stringify(result), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});