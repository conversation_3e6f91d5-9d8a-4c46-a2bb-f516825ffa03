# Backup Scripts

Database backup utilities for data protection and recovery.

## Files in this directory:

### Cross-Platform Backup Scripts
- `backup-database.sh` - Unix/Linux backup script
- `backup-database.ps1` - PowerShell backup script for Windows
- `full-database-backup.ps1` - Comprehensive backup solution

### Simple Backup Scripts
- `simple-backup.ps1` - Lightweight backup utility

## Usage

### Unix/Linux Systems
```bash
./backup-database.sh
```

### Windows Systems
```powershell
.\backup-database.ps1
# or for comprehensive backup
.\full-database-backup.ps1
```

## Features

- **Automated timestamping** - Backups include date/time stamps
- **Compression** - Backups are compressed to save storage
- **Error handling** - Scripts include error detection and reporting
- **Cross-platform support** - Both Unix and Windows environments

## Backup Strategy

1. **Daily automated backups** using cron/Task Scheduler
2. **Pre-migration backups** before applying schema changes
3. **Full backups** before major application deployments
4. **Point-in-time backups** during critical operations

## Configuration

- Update database connection parameters in scripts
- Configure backup retention policies
- Set appropriate file permissions for backup directories
- Test restore procedures regularly

## Notes

- Store backups in secure, off-site locations
- Verify backup integrity regularly
- Document restore procedures
- Consider encryption for sensitive data backups