-- Migration 012: General Analytics Overview Data Function
-- Creates comprehensive database function for General Analytics overview tab
-- Provides business health metrics, category performance, and profit trends

-- Function to get comprehensive general analytics data
CREATE OR REPLACE FUNCTION analytics_get_general_overview_data(
    p_start_date DATE,
    p_end_date DATE,
    p_granularity TEXT DEFAULT 'daily'
) RETURNS TABLE(
    business_health JSONB,
    category_performance JSONB,
    profit_trends JSONB,
    top_products JSONB,
    system_health JSONB
) 
LANGUAGE plpgsql
AS $$
DECLARE
    v_start_date DATE := p_start_date;
    v_end_date DATE := p_end_date;
    v_prev_start_date DATE;
    v_prev_end_date DATE;
    v_date_diff INTEGER;
    v_business_health JSONB;
    v_category_performance JSONB;
    v_profit_trends JSONB;
    v_top_products JSONB;
    v_system_health JSONB;
BEGIN
    -- Calculate previous period for comparison
    v_date_diff := v_end_date - v_start_date;
    v_prev_start_date := v_start_date - v_date_diff;
    v_prev_end_date := v_start_date - 1;

    -- 1. BUSINESS HEALTH METRICS
    WITH current_period AS (
        SELECT 
            -- Revenue metrics (using discount_amount which is the actual final amount)
            COALESCE(SUM(oi.discount_amount), 0) as total_revenue,
            COUNT(DISTINCT o.order_id) as total_orders,
            COUNT(DISTINCT o.client_name) as unique_clients,
            COALESCE(AVG(oi.discount_amount), 0) as avg_order_value,
            -- Simple production cost calculation using cost_price as fallback
            COALESCE(SUM(oi.cost_price * oi.qty), 0) as total_production_cost,
            -- Payment metrics
            COALESCE(SUM(o.cash_paid), 0) as total_cash_received,
            COALESCE(SUM(o.balance), 0) as total_outstanding
        FROM orders o
        LEFT JOIN order_items oi ON o.order_id = oi.order_id
        WHERE o.order_date BETWEEN v_start_date AND v_end_date
            AND o.status != 'Cancelled'
    ),
    previous_period AS (
        SELECT 
            COALESCE(SUM(oi.discount_amount), 0) as prev_total_revenue,
            COUNT(DISTINCT o.order_id) as prev_total_orders,
            COUNT(DISTINCT o.client_name) as prev_unique_clients,
            COALESCE(AVG(oi.discount_amount), 0) as prev_avg_order_value,
            COALESCE(SUM(oi.cost_price * oi.qty), 0) as prev_total_production_cost
        FROM orders o
        LEFT JOIN order_items oi ON o.order_id = oi.order_id
        WHERE o.order_date BETWEEN v_prev_start_date AND v_prev_end_date
            AND o.status != 'Cancelled'
    )
    SELECT jsonb_build_object(
        'totalRevenue', curr.total_revenue,
        'totalProfit', GREATEST(curr.total_revenue - curr.total_production_cost, 0),
        'totalOrders', curr.total_orders,
        'uniqueClients', curr.unique_clients,
        'avgOrderValue', curr.avg_order_value,
        'avgProfitMargin', CASE 
            WHEN curr.total_revenue > 0 
            THEN ((curr.total_revenue - curr.total_production_cost) / curr.total_revenue) * 100 
            ELSE 0 
        END,
        'totalCashReceived', curr.total_cash_received,
        'totalOutstanding', curr.total_outstanding,
        'revenueGrowthRate', CASE 
            WHEN prev.prev_total_revenue > 0 
            THEN ((curr.total_revenue - prev.prev_total_revenue) / prev.prev_total_revenue) * 100 
            ELSE 0 
        END,
        'profitGrowthRate', CASE 
            WHEN (prev.prev_total_revenue - prev.prev_total_production_cost) > 0 
            THEN (((curr.total_revenue - curr.total_production_cost) - (prev.prev_total_revenue - prev.prev_total_production_cost)) / (prev.prev_total_revenue - prev.prev_total_production_cost)) * 100 
            ELSE 0 
        END
    ) INTO v_business_health
    FROM current_period curr, previous_period prev;

    -- 2. CATEGORY PERFORMANCE 
    WITH category_stats AS (
        SELECT 
            oi.product as category,
            COALESCE(SUM(oi.discount_amount), 0) as revenue,
            COUNT(DISTINCT o.order_id) as orders,
            COALESCE(SUM(oi.qty), 0) as units_sold,
            COALESCE(SUM(oi.cost_price * oi.qty), 0) as production_cost
        FROM orders o
        LEFT JOIN order_items oi ON o.order_id = oi.order_id
        WHERE o.order_date BETWEEN v_start_date AND v_end_date
            AND o.status != 'Cancelled'
            AND oi.product IS NOT NULL
        GROUP BY oi.product
    ),
    total_revenue AS (
        SELECT SUM(revenue) as total FROM category_stats
    )
    SELECT jsonb_agg(
        jsonb_build_object(
            'category', cs.category,
            'revenue', cs.revenue,
            'profit', GREATEST(cs.revenue - cs.production_cost, 0),
            'orders', cs.orders,
            'unitsSold', cs.units_sold,
            'profitMargin', CASE 
                WHEN cs.revenue > 0 
                THEN ((cs.revenue - cs.production_cost) / cs.revenue) * 100 
                ELSE 0 
            END,
            'marketShare', CASE 
                WHEN tr.total > 0 
                THEN (cs.revenue / tr.total) * 100 
                ELSE 0 
            END,
            'growthRate', 0
        ) ORDER BY cs.revenue DESC
    ) INTO v_category_performance
    FROM category_stats cs, total_revenue tr;

    -- 3. PROFIT TRENDS (simplified daily version)
    WITH RECURSIVE date_series AS (
        SELECT v_start_date as date_val
        UNION ALL
        SELECT date_val + 1
        FROM date_series
        WHERE date_val + 1 <= v_end_date
    ),
    profit_by_period AS (
        SELECT 
            ds.date_val as period,
            COALESCE(SUM(oi.discount_amount), 0) as revenue,
            COUNT(DISTINCT o.order_id) as orders,
            COALESCE(SUM(oi.cost_price * oi.qty), 0) as production_cost
        FROM date_series ds
        LEFT JOIN orders o ON o.order_date = ds.date_val
            AND o.status != 'Cancelled'
        LEFT JOIN order_items oi ON o.order_id = oi.order_id
        GROUP BY ds.date_val
        ORDER BY ds.date_val
    )
    SELECT jsonb_agg(
        jsonb_build_object(
            'date', pbp.period,
            'revenue', pbp.revenue,
            'profit', GREATEST(pbp.revenue - pbp.production_cost, 0),
            'profitMargin', CASE 
                WHEN pbp.revenue > 0 
                THEN ((pbp.revenue - pbp.production_cost) / pbp.revenue) * 100 
                ELSE 0 
            END,
            'orders', pbp.orders,
            'averageOrderValue', CASE 
                WHEN pbp.orders > 0 
                THEN pbp.revenue / pbp.orders 
                ELSE 0 
            END
        ) ORDER BY pbp.period
    ) INTO v_profit_trends
    FROM profit_by_period pbp;

    -- 4. TOP PRODUCTS (by revenue)
    WITH product_performance AS (
        SELECT 
            oi.product_type as product_type,
            oi.product as category,
            COALESCE(SUM(oi.discount_amount), 0) as revenue,
            COUNT(DISTINCT o.order_id) as orders,
            COALESCE(SUM(oi.qty), 0) as units_sold,
            COALESCE(SUM(oi.cost_price * oi.qty), 0) as production_cost
        FROM orders o
        LEFT JOIN order_items oi ON o.order_id = oi.order_id
        WHERE o.order_date BETWEEN v_start_date AND v_end_date
            AND o.status != 'Cancelled'
            AND oi.product_type IS NOT NULL
        GROUP BY oi.product_type, oi.product
        ORDER BY revenue DESC
        LIMIT 10
    )
    SELECT jsonb_agg(
        jsonb_build_object(
            'productType', pp.product_type,
            'category', pp.category,
            'revenue', pp.revenue,
            'profit', GREATEST(pp.revenue - pp.production_cost, 0),
            'orders', pp.orders,
            'unitsSold', pp.units_sold,
            'profitMargin', CASE 
                WHEN pp.revenue > 0 
                THEN ((pp.revenue - pp.production_cost) / pp.revenue) * 100 
                ELSE 0 
            END,
            'growthRate', 0
        )
    ) INTO v_top_products
    FROM product_performance pp;

    -- 5. SYSTEM HEALTH METRICS
    WITH data_quality AS (
        SELECT 
            COUNT(*) as total_orders,
            COUNT(*) FILTER (WHERE cash_paid IS NOT NULL) as orders_with_payment_data,
            COUNT(*) FILTER (WHERE total_amount IS NOT NULL AND total_amount > 0) as orders_with_amount,
            COUNT(DISTINCT client_name) as unique_clients,
            COUNT(*) FILTER (WHERE status = 'Cancelled') as cancelled_orders
        FROM orders
        WHERE order_date BETWEEN v_start_date AND v_end_date
    )
    SELECT jsonb_build_object(
        'dataQuality', jsonb_build_object(
            'totalOrders', dq.total_orders,
            'paymentDataCompleteness', CASE 
                WHEN dq.total_orders > 0 
                THEN (dq.orders_with_payment_data::FLOAT / dq.total_orders) * 100 
                ELSE 0 
            END,
            'amountDataCompleteness', CASE 
                WHEN dq.total_orders > 0 
                THEN (dq.orders_with_amount::FLOAT / dq.total_orders) * 100 
                ELSE 0 
            END,
            'uniqueClients', dq.unique_clients,
            'cancelledOrdersRate', CASE 
                WHEN dq.total_orders > 0 
                THEN (dq.cancelled_orders::FLOAT / dq.total_orders) * 100 
                ELSE 0 
            END
        )
    ) INTO v_system_health
    FROM data_quality dq;

    -- Return all analytics data
    RETURN QUERY
    SELECT 
        v_business_health,
        v_category_performance,
        v_profit_trends,
        v_top_products,
        v_system_health;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION analytics_get_general_overview_data(DATE, DATE, TEXT) TO anon, authenticated;

-- Add helpful comment
COMMENT ON FUNCTION analytics_get_general_overview_data(DATE, DATE, TEXT) IS 'Comprehensive analytics function for General Analytics overview tab. Returns business health metrics, category performance, profit trends, top products, and system health indicators.';