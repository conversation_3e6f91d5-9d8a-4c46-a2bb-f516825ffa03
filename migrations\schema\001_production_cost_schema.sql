-- Production Cost Rules System - Database Migration
-- This migration adds support for production cost calculations
-- while maintaining backward compatibility with existing pricing rules

-- ============================================
-- STEP 1: Create Component Categories
-- ============================================

CREATE TABLE IF NOT EXISTS component_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    code TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('production', 'additional')),
    description TEXT,
    display_order INTEGER DEFAULT 0,
    icon TEXT,
    color TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Seed component categories
INSERT INTO component_categories (code, name, type, display_order, color) VALUES
('materials', 'Materials', 'production', 1, '#3B82F6'),
('hardware', 'Hardware', 'production', 2, '#10B981'),
('labor', 'Labor', 'production', 3, '#F59E0B'),
('operations', 'Operations', 'production', 4, '#8B5CF6'),
('overhead', 'Overhead', 'production', 5, '#EC4899'),
('setup', 'Setup Costs', 'additional', 6, '#F97316'),
('service', 'Service Charges', 'additional', 7, '#EF4444'),
('logistics', 'Logistics', 'additional', 8, '#06B6D4'),
('discounts', 'Discounts', 'additional', 9, '#84CC16')
ON CONFLICT (code) DO NOTHING;

-- ============================================
-- STEP 2: Enhance Production Cost Components
-- ============================================

ALTER TABLE production_cost_components
ADD COLUMN IF NOT EXISTS category_id UUID REFERENCES component_categories(id),
ADD COLUMN IF NOT EXISTS component_type TEXT DEFAULT 'production' CHECK (component_type IN ('production', 'additional')),
ADD COLUMN IF NOT EXISTS is_percentage BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS calculation_order INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS applicable_to JSONB DEFAULT '{"all": true}'::jsonb;

-- Update existing components with categories
UPDATE production_cost_components c
SET category_id = cat.id,
    component_type = CASE 
        WHEN c.category IN ('Materials', 'Hardware', 'Labor', 'Operations', 'Overhead') THEN 'production'
        ELSE 'additional'
    END
FROM component_categories cat
WHERE LOWER(c.category) = cat.code
  AND c.category_id IS NULL;

-- ============================================
-- STEP 3: Create Production Rules Metadata Table
-- ============================================

CREATE TABLE IF NOT EXISTS production_rules_meta (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    rule_id UUID REFERENCES calculation_rules(id) ON DELETE CASCADE,
    
    -- Rule metadata
    method_type TEXT,
    auto_description TEXT,
    
    -- Scope definition
    scope_type TEXT,
    product_categories TEXT[],
    product_types TEXT[],
    product_ids UUID[],
    size_filter TEXT[],
    
    -- For combination rules
    is_combination BOOLEAN DEFAULT false,
    base_products JSONB,
    combination_additional_components TEXT[],
    
    -- Versioning
    version INTEGER DEFAULT 1,
    parent_version_id UUID REFERENCES production_rules_meta(id),
    effective_from DATE DEFAULT CURRENT_DATE,
    effective_to DATE,
    is_active BOOLEAN DEFAULT true,
    change_reason TEXT,
    
    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by TEXT
);

CREATE INDEX idx_production_rules_active ON production_rules_meta(rule_id, is_active);
CREATE INDEX idx_production_rules_scope ON production_rules_meta(scope_type, is_active);

-- ============================================
-- STEP 4: Component Value History
-- ============================================

CREATE TABLE IF NOT EXISTS component_value_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    component_value_id UUID REFERENCES production_cost_component_values(id),
    component_id UUID REFERENCES production_cost_components(id),
    product_id UUID REFERENCES products(id),
    old_value NUMERIC,
    new_value NUMERIC NOT NULL,
    change_date TIMESTAMPTZ DEFAULT NOW(),
    change_reason TEXT,
    changed_by TEXT
);

-- Add tracking columns to component values
ALTER TABLE production_cost_component_values
ADD COLUMN IF NOT EXISTS is_current BOOLEAN DEFAULT true;

-- Index for current values
CREATE INDEX IF NOT EXISTS idx_component_values_current 
ON production_cost_component_values(product_id, component_id) 
WHERE is_current = true;

-- ============================================
-- STEP 5: Formula Templates
-- ============================================

CREATE TABLE IF NOT EXISTS formula_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    category TEXT NOT NULL,
    description TEXT,
    formula_pattern TEXT NOT NULL,
    variables JSONB NOT NULL,
    example_formula TEXT,
    applicable_to JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Seed common formula templates
INSERT INTO formula_templates (name, category, description, formula_pattern, variables, example_formula) VALUES
('Simple Sum', 'basic', 'Add all component values', 'SUM({components})', '{"components": "array of component codes"}', 'wood_frame + board + paper_ink'),
('Material Cost Plus Labor', 'standard', 'Sum materials and add labor percentage', 'SUM({materials}) + ({labor} * {markup})', '{"materials": "material components", "labor": "labor component", "markup": "percentage markup"}', '(wood_frame + board + paper_ink) + (labour_rent * 0.15)'),
('Tiered Quantity Pricing', 'advanced', 'Different formulas based on quantity', 'IF(qty <= {tier1_max}, {tier1_formula}, IF(qty <= {tier2_max}, {tier2_formula}, {tier3_formula}))', '{"tier1_max": "max quantity for tier 1", "tier1_formula": "formula for tier 1"}', 'IF(qty <= 5, base_cost * 1.2, IF(qty <= 10, base_cost * 1.1, base_cost))')
ON CONFLICT DO NOTHING;

-- ============================================
-- STEP 6: Rule Test Cases
-- ============================================

CREATE TABLE IF NOT EXISTS rule_test_cases (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    rule_id UUID REFERENCES calculation_rules(id),
    test_name TEXT NOT NULL,
    test_description TEXT,
    input_values JSONB NOT NULL,
    expected_result NUMERIC NOT NULL,
    last_run_result NUMERIC,
    last_run_date TIMESTAMPTZ,
    is_passing BOOLEAN,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================
-- STEP 7: Enhance Production Cost Calculations
-- ============================================

ALTER TABLE production_cost_calculations
ADD COLUMN IF NOT EXISTS order_item_id UUID,
ADD COLUMN IF NOT EXISTS quantity INTEGER,
ADD COLUMN IF NOT EXISTS base_production_cost NUMERIC,
ADD COLUMN IF NOT EXISTS additional_costs JSONB,
ADD COLUMN IF NOT EXISTS rule_version INTEGER,
ADD COLUMN IF NOT EXISTS calculation_context JSONB;

-- ============================================
-- STEP 8: Create Helper Functions
-- ============================================

-- Function to get current component values for a product
CREATE OR REPLACE FUNCTION get_current_component_values(p_product_id UUID)
RETURNS TABLE (
    component_code TEXT,
    component_name TEXT,
    value NUMERIC,
    category TEXT,
    component_type TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.code,
        c.name,
        cv.value,
        cat.name,
        c.component_type
    FROM production_cost_components c
    JOIN production_cost_component_values cv ON c.id = cv.component_id
    JOIN component_categories cat ON c.category_id = cat.id
    WHERE cv.product_id = p_product_id
      AND cv.is_current = true
    ORDER BY cat.display_order, c.calculation_order, c.name;
END;
$$ LANGUAGE plpgsql;

-- Function to validate formula components exist
CREATE OR REPLACE FUNCTION validate_formula_components(
    p_component_codes TEXT[],
    p_product_id UUID
) RETURNS JSONB AS $$
DECLARE
    v_missing_components TEXT[];
    v_result JSONB;
BEGIN
    -- Find components that don't have values for this product
    SELECT array_agg(code)
    INTO v_missing_components
    FROM unnest(p_component_codes) AS code
    WHERE NOT EXISTS (
        SELECT 1 
        FROM production_cost_components c
        JOIN production_cost_component_values cv ON c.id = cv.component_id
        WHERE c.code = code
          AND cv.product_id = p_product_id
          AND cv.is_current = true
    );
    
    IF v_missing_components IS NOT NULL THEN
        v_result = jsonb_build_object(
            'valid', false,
            'missing_components', v_missing_components
        );
    ELSE
        v_result = jsonb_build_object('valid', true);
    END IF;
    
    RETURN v_result;
END;
$$ LANGUAGE plpgsql;

-- ============================================
-- STEP 9: Create Views for Easy Querying
-- ============================================

-- View for production cost rules
CREATE OR REPLACE VIEW production_cost_rules_view AS
SELECT 
    cr.id,
    cr.charge_type,
    cr.rule_type,
    cr.description,
    cr.calculation_formula,
    cr.component_codes,
    cr.condition_type,
    cr.condition_operator,
    cr.condition_value,
    cr.tiers,
    cr.status,
    pm.method_type,
    pm.auto_description,
    pm.scope_type,
    pm.product_categories,
    pm.product_types,
    pm.size_filter,
    pm.is_combination,
    pm.base_products,
    pm.version,
    pm.is_active
FROM calculation_rules cr
LEFT JOIN production_rules_meta pm ON cr.id = pm.rule_id
WHERE cr.rule_scope = 'production_cost'
  AND (pm.is_active IS TRUE OR pm.is_active IS NULL);

-- View for component values with categories
CREATE OR REPLACE VIEW component_values_view AS
SELECT 
    cv.id,
    cv.product_id,
    p.item_type AS product_name,
    p.size AS product_size,
    c.code AS component_code,
    c.name AS component_name,
    cat.name AS category_name,
    cat.color AS category_color,
    c.component_type,
    cv.value,
    cv.effective_date,
    cv.notes
FROM production_cost_component_values cv
JOIN production_cost_components c ON cv.component_id = c.id
JOIN component_categories cat ON c.category_id = cat.id
LEFT JOIN products p ON cv.product_id = p.id
WHERE cv.is_current = true
ORDER BY cat.display_order, c.calculation_order;

-- ============================================
-- STEP 10: Add Indexes for Performance
-- ============================================

CREATE INDEX IF NOT EXISTS idx_calc_rules_scope_status 
ON calculation_rules(rule_scope, status) 
WHERE rule_scope = 'production_cost';

CREATE INDEX IF NOT EXISTS idx_prod_rules_meta_product_types 
ON production_rules_meta USING GIN (product_types);

CREATE INDEX IF NOT EXISTS idx_prod_rules_meta_categories 
ON production_rules_meta USING GIN (product_categories);

-- Grant permissions (adjust based on your roles)
-- GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA public TO authenticated;
-- GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;