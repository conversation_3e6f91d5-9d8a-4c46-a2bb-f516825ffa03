# Database Migrations & SQL Management

Centralized location for all database migrations, scripts, backups, and SQL-related files.

## Directory Structure

```
migrations/
├── schema/          # Database structure changes
├── data/           # Data transformations & business rules
├── permissions/    # Authentication & authorization
├── fixes/          # Bug fixes & corrections  
├── features/       # New functionality implementations
├── scripts/        # Utility and maintenance scripts
│   ├── backup/     # Database backup utilities
│   ├── maintenance/ # Migration runners & maintenance
│   └── utilities/  # General utility scripts
├── backups/        # Database backup files
└── schema/         # Core schema files
```

## Migration Categories

### 🏗️ Schema Migrations (`/schema/`)
- Database structure changes
- Table creation and modifications
- Core architectural updates
- **Execute FIRST** - foundation for all other migrations

### 📊 Data Migrations (`/data/`)
- Business logic implementations
- Data transformations
- Application behavior modifications
- **Execute SECOND** - after schema is established

### 🔒 Permission Migrations (`/permissions/`)
- Authentication systems
- Authorization policies
- User management
- Row Level Security (RLS)
- **Execute THIRD** - after core data structure exists

### ✨ Feature Migrations (`/features/`)
- New functionality
- Analytics systems
- Enhancement features
- **Execute FOURTH** - optional enhancements

### 🔧 Fix Migrations (`/fixes/`)
- Bug fixes and corrections
- Data corruption repairs
- **Execute AS NEEDED** - can be applied out of sequence

## Execution Order

1. **Schema migrations** (structural foundation)
2. **Data migrations** (business logic setup)
3. **Permission migrations** (security implementation)
4. **Feature migrations** (enhancements)
5. **Fix migrations** (as needed for corrections)

## Quick Start

### Running All Migrations
```bash
# 1. Backup database first
./scripts/backup/backup-database.sh

# 2. Run migrations in order
# Execute all files in schema/ first
# Then data/, permissions/, features/
# Apply fixes/ as needed
```

### Individual Categories
```bash
# Schema changes only
psql -d database -f schema/001_production_cost_schema.sql

# Data transformations
psql -d database -f data/002_business_rule_enforcement.sql
```

## File Organization Standards

### Naming Convention
- **Migrations**: `###_descriptive_name.sql` (sequential numbering)
- **Scripts**: `descriptive-name.(sh|ps1|js|sql)` (hyphen-separated)
- **Backups**: `YYYYMMDD_HHMMSS_backup_type/` (timestamp-based)

### Documentation Requirements
Each subdirectory contains:
- `README.md` - Purpose, usage, and file descriptions
- Organized file listings with dependencies
- Execution instructions and best practices

## Best Practices

### Before Migration
1. 📋 **Create backup** using scripts in `/scripts/backup/`
2. 🧪 **Test in development** environment first
3. 📖 **Read README files** in relevant subdirectories
4. ✅ **Check dependencies** between migrations

### During Migration
1. 📊 **Monitor logs** for errors or warnings
2. 🔍 **Verify results** after each major step
3. ⚡ **Watch performance** impact during execution
4. 🛑 **Stop immediately** if critical errors occur

### After Migration
1. ✨ **Validate functionality** in application
2. 📈 **Monitor performance** metrics
3. 📝 **Update documentation** if needed
4. 🗂️ **Archive old backups** per retention policy

## Common Tasks

### Database Setup (New Installation)
```bash
# 1. Create database backup location
mkdir -p migrations/backups/

# 2. Execute schema migrations in order
# 3. Execute data migrations 
# 4. Execute permission migrations
# 5. Execute feature migrations (optional)
```

### Regular Maintenance
```bash
# Weekly backup
./scripts/backup/backup-database.sh

# Cleanup utilities
node scripts/utilities/cleanup-dead-code.js

# Verify migration integrity  
psql -d database -f scripts/utilities/verify-permissions-migration.sql
```

## Troubleshooting

### Migration Failures
1. Check migration logs for specific error messages
2. Verify database connectivity and permissions
3. Ensure prerequisite migrations are completed
4. Restore from backup if necessary and retry

### Performance Issues
1. Monitor query performance during migrations
2. Consider running during low-traffic periods
3. Add appropriate database indexes if needed
4. Review RLS policies for performance impact

## Security Notes

- 🔐 **Backup encryption** for sensitive data
- 🛡️ **Test RLS policies** thoroughly before production
- 📋 **Audit migration logs** for security compliance
- 🔒 **Secure backup storage** and access controls

---

**Need Help?** Check the README.md file in each subdirectory for specific guidance and detailed information about file contents and usage.