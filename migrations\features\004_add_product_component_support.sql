-- Migration: Add Product Component Support to Templates
-- Date: 2025-01-07
-- Purpose: Enable templates to use products as components with their production costs
-- Impact: Enhances calculation_production_cost function and template validation

-- =============================================================================
-- ENHANCEMENT: Update calculate_production_cost function to support product codes
-- =============================================================================

CREATE OR REPLACE FUNCTION calculate_production_cost(
  p_category_id UUID,
  p_product_type_id UUID, 
  p_size_id UUID
) RETURNS NUMERIC AS $$
DECLARE
  template_record RECORD;
  component_cost NUMERIC := 0;
  product_cost NUMERIC := 0;
  total_cost NUMERIC := 0;
  component_code TEXT;
  product_parts TEXT[];
  product_category_name TEXT;
  product_type_name TEXT;
  product_size_name TEXT;
BEGIN
  -- Get all active templates applied to this product combination
  FOR template_record IN
    SELECT ct.*, ta.applied_at
    FROM calculation_templates ct
    JOIN template_applications ta ON ta.template_id = ct.id
    WHERE ta.category_id = p_category_id
      AND ta.product_type_id = p_product_type_id
      AND ta.size_id = p_size_id
      AND ta.is_active = true
      AND ct.status = 'active'
    ORDER BY ta.applied_at ASC
  LOOP
    -- For each template, process its selected components
    IF template_record.selected_components IS NOT NULL THEN
      
      -- Process each selected component
      FOREACH component_code IN ARRAY template_record.selected_components
      LOOP
        component_cost := 0;
        
        -- Check if this is a product code (hyphen-separated format)
        IF component_code LIKE '%-%-%' THEN
          -- Parse product code: "category-product_type-size"
          product_parts := string_to_array(component_code, '-');
          
          IF array_length(product_parts, 1) = 3 THEN
            product_category_name := replace(product_parts[1], '_', ' ');
            product_type_name := replace(product_parts[2], '_', ' ');
            product_size_name := replace(product_parts[3], '_', ' ');
            
            -- Find matching product and get its current production cost
            SELECT COALESCE(pl.total_production_cost, 0) INTO product_cost
            FROM product_line pl
            JOIN product_attributes cat ON cat.id = pl.category_id 
            JOIN product_attributes pt ON pt.id = pl.product_type_id
            JOIN product_attributes sz ON sz.id = pl.size_id
            WHERE LOWER(replace(cat.value, ' ', '_')) = LOWER(product_parts[1])
              AND LOWER(replace(pt.value, ' ', '_')) = LOWER(product_parts[2])
              AND LOWER(replace(sz.value, ' ', '_')) = LOWER(product_parts[3])
              AND pl.is_active = true
            LIMIT 1;
            
            component_cost := COALESCE(product_cost, 0);
          END IF;
          
        ELSE
          -- Handle traditional component code
          SELECT COALESCE(SUM(pcv.value), 0) INTO component_cost
          FROM production_cost_component_values pcv
          JOIN production_cost_components pcc ON pcc.id = pcv.component_id
          WHERE pcv.product_category_id = p_category_id
            AND pcv.product_type_id = p_product_type_id
            AND pcv.size_id = p_size_id
            AND pcv.is_current = true
            AND pcc.status = 'active'
            AND pcc.code = component_code;
        END IF;
        
        -- Add this component's cost to the template total
        total_cost := total_cost + component_cost;
      END LOOP;
      
    END IF;
  END LOOP;
  
  RETURN total_cost;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- HELPER FUNCTION: Validate component codes (both traditional and product codes)
-- =============================================================================

CREATE OR REPLACE FUNCTION validate_template_component(component_code TEXT) 
RETURNS BOOLEAN AS $$
DECLARE
  component_exists BOOLEAN := false;
  product_parts TEXT[];
  product_exists BOOLEAN := false;
BEGIN
  -- Check if it's a product code format
  IF component_code LIKE '%-%-%' THEN
    product_parts := string_to_array(component_code, '-');
    
    IF array_length(product_parts, 1) = 3 THEN
      -- Check if product exists
      SELECT EXISTS(
        SELECT 1 FROM product_line pl
        JOIN product_attributes cat ON cat.id = pl.category_id 
        JOIN product_attributes pt ON pt.id = pl.product_type_id
        JOIN product_attributes sz ON sz.id = pl.size_id
        WHERE LOWER(replace(cat.value, ' ', '_')) = LOWER(product_parts[1])
          AND LOWER(replace(pt.value, ' ', '_')) = LOWER(product_parts[2])
          AND LOWER(replace(sz.value, ' ', '_')) = LOWER(product_parts[3])
          AND pl.is_active = true
      ) INTO product_exists;
      
      RETURN product_exists;
    END IF;
    
    RETURN false;
  ELSE
    -- Check traditional component
    SELECT EXISTS(
      SELECT 1 FROM production_cost_components 
      WHERE code = component_code AND status = 'active'
    ) INTO component_exists;
    
    RETURN component_exists;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- VERIFICATION QUERIES
-- =============================================================================

-- Test the enhanced function with a sample product
DO $$
DECLARE
  test_cost NUMERIC;
BEGIN
  -- This will test both traditional components and product codes
  RAISE NOTICE 'Enhanced calculate_production_cost function created successfully';
  RAISE NOTICE 'Function now supports both traditional components and product codes';
END $$;

-- Add helpful comments
COMMENT ON FUNCTION calculate_production_cost(UUID, UUID, UUID) IS 
'Enhanced function that calculates production costs from templates supporting both traditional components and product codes (hyphen-separated format)';

COMMENT ON FUNCTION validate_template_component(TEXT) IS 
'Validates that a component code exists - supports both traditional components and product codes';