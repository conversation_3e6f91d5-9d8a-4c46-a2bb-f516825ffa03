import React from 'react';
import { Loader2 } from 'lucide-react';

interface LoadingIndicatorProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  className?: string;
  showText?: boolean;
  fullScreen?: boolean;
}

/**
 * Reusable LoadingIndicator component for consistent loading states
 * Following CLAUDE.md guidelines - focused functionality under 250 lines
 */
export const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  size = 'md',
  text = 'Loading...',
  className = '',
  showText = true,
  fullScreen = false
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };

  const content = (
    <div className={`flex items-center justify-center gap-2 ${className}`}>
      <Loader2 className={`${sizeClasses[size]} animate-spin text-gray-600`} />
      {showText && (
        <span className={`${textSizeClasses[size]} text-gray-600 font-medium`}>
          {text}
        </span>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="w-full max-w-sm mx-4">
          <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
            <div className="relative">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-gray-200 mx-auto"></div>
              <div className="absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center">
                <div className="w-8 h-8 rounded-full bg-[#F0ECFD] flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-[#613AEB]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                  </svg>
                </div>
              </div>
            </div>
            <p className="mt-6 text-gray-600 font-medium">{text}</p>
            <p className="mt-2 text-gray-400 text-sm mb-0">Please wait a moment</p>
          </div>
        </div>
      </div>
    );
  }

  return content;
};

/**
 * Button-specific loading indicator for inline use
 */
interface ButtonLoadingProps {
  text?: string;
  className?: string;
}

export const ButtonLoading: React.FC<ButtonLoadingProps> = ({
  text = 'Loading...',
  className = ''
}) => (
  <div className={`flex items-center justify-center ${className}`}>
    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
    {text}
  </div>
);

/**
 * Card-based loading indicator for auth forms
 */
interface AuthLoadingProps {
  title?: string;
  subtitle?: string;
  className?: string;
}

export const AuthLoading: React.FC<AuthLoadingProps> = ({
  title = 'Authenticating...',
  subtitle = 'Please wait a moment',
  className = ''
}) => (
  <div className={`min-h-screen flex items-center justify-center bg-gray-100 ${className}`}>
    <div className="w-full max-w-sm mx-4">
      <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
        <div className="animate-spin rounded-full h-16 w-16 border-4 border-gray-200 mx-auto mb-4"></div>
        <p className="text-gray-600 font-medium">{title}</p>
        {subtitle && <p className="mt-2 text-gray-400 text-sm mb-0">{subtitle}</p>}
      </div>
    </div>
  </div>
);