-- Comprehensive RLS Implementation - Granular Policies  
-- Following Supabase best practices for specific policy types
-- Phase 2: Granular SELECT/INSERT/UPDATE/DELETE policies
-- Based on actual existing tables in the database

-- ============================================================================
-- CATEGORY 3: SYSTEM CONFIGURATION TABLES
-- ============================================================================

-- Permissions Table (system configuration)
ALTER TABLE permissions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "permissions_select" ON permissions
  FOR SELECT TO authenticated
  USING (is_authorized_user());

-- Permissions are read-only for regular users
-- No INSERT/UPDATE/DELETE policies = blocked by default

-- Roles Table (system configuration)  
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "roles_select" ON roles
  FOR SELECT TO authenticated
  USING (is_authorized_user());

-- Roles are read-only for regular users
-- No INSERT/UPDATE/DELETE policies = blocked by default

-- ============================================================================
-- CATEGORY 4: PRODUCTION COST SYSTEM (Actual existing tables)
-- ============================================================================

-- Production Cost Components Table
ALTER TABLE production_cost_components ENABLE ROW LEVEL SECURITY;

CREATE POLICY "production_cost_components_select" ON production_cost_components
  FOR SELECT TO authenticated
  USING (is_authorized_user());

CREATE POLICY "production_cost_components_insert" ON production_cost_components
  FOR INSERT TO authenticated
  WITH CHECK (is_authorized_user());

CREATE POLICY "production_cost_components_update" ON production_cost_components
  FOR UPDATE TO authenticated
  USING (is_authorized_user())
  WITH CHECK (is_authorized_user());

CREATE POLICY "production_cost_components_delete" ON production_cost_components
  FOR DELETE TO authenticated
  USING (is_authorized_user());

-- Production Cost Component Values Table
ALTER TABLE production_cost_component_values ENABLE ROW LEVEL SECURITY;

CREATE POLICY "production_cost_component_values_select" ON production_cost_component_values
  FOR SELECT TO authenticated
  USING (is_authorized_user());

CREATE POLICY "production_cost_component_values_insert" ON production_cost_component_values
  FOR INSERT TO authenticated
  WITH CHECK (is_authorized_user());

CREATE POLICY "production_cost_component_values_update" ON production_cost_component_values
  FOR UPDATE TO authenticated
  USING (is_authorized_user())
  WITH CHECK (is_authorized_user());

CREATE POLICY "production_cost_component_values_delete" ON production_cost_component_values
  FOR DELETE TO authenticated
  USING (is_authorized_user());

-- Production Cost Calculations Table
ALTER TABLE production_cost_calculations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "production_cost_calculations_select" ON production_cost_calculations
  FOR SELECT TO authenticated
  USING (is_authorized_user());

CREATE POLICY "production_cost_calculations_insert" ON production_cost_calculations
  FOR INSERT TO authenticated
  WITH CHECK (is_authorized_user());

CREATE POLICY "production_cost_calculations_update" ON production_cost_calculations
  FOR UPDATE TO authenticated
  USING (is_authorized_user())
  WITH CHECK (is_authorized_user());

CREATE POLICY "production_cost_calculations_delete" ON production_cost_calculations
  FOR DELETE TO authenticated
  USING (is_authorized_user());

-- Product Line Table
ALTER TABLE product_line ENABLE ROW LEVEL SECURITY;

CREATE POLICY "product_line_select" ON product_line
  FOR SELECT TO authenticated
  USING (is_authorized_user());

CREATE POLICY "product_line_insert" ON product_line
  FOR INSERT TO authenticated
  WITH CHECK (is_authorized_user());

CREATE POLICY "product_line_update" ON product_line
  FOR UPDATE TO authenticated
  USING (is_authorized_user())
  WITH CHECK (is_authorized_user());

CREATE POLICY "product_line_delete" ON product_line
  FOR DELETE TO authenticated
  USING (is_authorized_user());

-- Calculation Rules Table
ALTER TABLE calculation_rules ENABLE ROW LEVEL SECURITY;

CREATE POLICY "calculation_rules_select" ON calculation_rules
  FOR SELECT TO authenticated
  USING (is_authorized_user());

CREATE POLICY "calculation_rules_insert" ON calculation_rules
  FOR INSERT TO authenticated
  WITH CHECK (is_authorized_user());

CREATE POLICY "calculation_rules_update" ON calculation_rules
  FOR UPDATE TO authenticated
  USING (is_authorized_user())
  WITH CHECK (is_authorized_user());

CREATE POLICY "calculation_rules_delete" ON calculation_rules
  FOR DELETE TO authenticated
  USING (is_authorized_user());

-- ============================================================================
-- CATEGORY 5: ANALYTICS DATA (Read-heavy computed data)
-- ============================================================================

-- Analytics Category Performance Table
ALTER TABLE analytics_category_performance ENABLE ROW LEVEL SECURITY;

CREATE POLICY "analytics_category_performance_select" ON analytics_category_performance
  FOR SELECT TO authenticated
  USING (is_authorized_user());

CREATE POLICY "analytics_category_performance_insert" ON analytics_category_performance
  FOR INSERT TO authenticated
  WITH CHECK (is_authorized_user());

CREATE POLICY "analytics_category_performance_update" ON analytics_category_performance
  FOR UPDATE TO authenticated
  USING (is_authorized_user())
  WITH CHECK (is_authorized_user());

CREATE POLICY "analytics_category_performance_delete" ON analytics_category_performance
  FOR DELETE TO authenticated
  USING (is_authorized_user());

-- Analytics Client Performance Table
ALTER TABLE analytics_client_performance ENABLE ROW LEVEL SECURITY;

CREATE POLICY "analytics_client_performance_select" ON analytics_client_performance
  FOR SELECT TO authenticated
  USING (is_authorized_user());

CREATE POLICY "analytics_client_performance_insert" ON analytics_client_performance
  FOR INSERT TO authenticated
  WITH CHECK (is_authorized_user());

CREATE POLICY "analytics_client_performance_update" ON analytics_client_performance
  FOR UPDATE TO authenticated
  USING (is_authorized_user())
  WITH CHECK (is_authorized_user());

CREATE POLICY "analytics_client_performance_delete" ON analytics_client_performance
  FOR DELETE TO authenticated
  USING (is_authorized_user());

-- Analytics Daily Metrics Table  
ALTER TABLE analytics_daily_metrics ENABLE ROW LEVEL SECURITY;

CREATE POLICY "analytics_daily_metrics_select" ON analytics_daily_metrics
  FOR SELECT TO authenticated
  USING (is_authorized_user());

CREATE POLICY "analytics_daily_metrics_insert" ON analytics_daily_metrics
  FOR INSERT TO authenticated
  WITH CHECK (is_authorized_user());

CREATE POLICY "analytics_daily_metrics_update" ON analytics_daily_metrics
  FOR UPDATE TO authenticated
  USING (is_authorized_user())
  WITH CHECK (is_authorized_user());

CREATE POLICY "analytics_daily_metrics_delete" ON analytics_daily_metrics
  FOR DELETE TO authenticated
  USING (is_authorized_user());

-- Analytics Product Performance Table
ALTER TABLE analytics_product_performance ENABLE ROW LEVEL SECURITY;

CREATE POLICY "analytics_product_performance_select" ON analytics_product_performance
  FOR SELECT TO authenticated
  USING (is_authorized_user());

CREATE POLICY "analytics_product_performance_insert" ON analytics_product_performance
  FOR INSERT TO authenticated
  WITH CHECK (is_authorized_user());

CREATE POLICY "analytics_product_performance_update" ON analytics_product_performance
  FOR UPDATE TO authenticated
  USING (is_authorized_user())
  WITH CHECK (is_authorized_user());

CREATE POLICY "analytics_product_performance_delete" ON analytics_product_performance
  FOR DELETE TO authenticated
  USING (is_authorized_user());

-- ============================================================================
-- CATEGORY 6: AUDIT/SYSTEM TABLES (Specialized access patterns)
-- ============================================================================

-- Production Cost Audit Table
ALTER TABLE production_cost_audit ENABLE ROW LEVEL SECURITY;

CREATE POLICY "production_cost_audit_select" ON production_cost_audit
  FOR SELECT TO authenticated
  USING (is_authorized_user());

CREATE POLICY "production_cost_audit_insert" ON production_cost_audit
  FOR INSERT TO authenticated
  WITH CHECK (is_authorized_user());

-- Audit tables typically don't allow updates or deletes (immutable)
-- No UPDATE/DELETE policies = blocked by default

-- Data Integrity Audit Table
ALTER TABLE data_integrity_audit ENABLE ROW LEVEL SECURITY;

CREATE POLICY "data_integrity_audit_select" ON data_integrity_audit
  FOR SELECT TO authenticated
  USING (is_authorized_user());

CREATE POLICY "data_integrity_audit_insert" ON data_integrity_audit
  FOR INSERT TO authenticated  
  WITH CHECK (is_authorized_user());

-- No UPDATE/DELETE policies for audit table integrity

-- ============================================================================
-- CATEGORY 7: ATTRIBUTES AND METADATA
-- ============================================================================

-- Product Attributes Table
ALTER TABLE product_attributes ENABLE ROW LEVEL SECURITY;

CREATE POLICY "product_attributes_select" ON product_attributes
  FOR SELECT TO authenticated
  USING (is_authorized_user());

CREATE POLICY "product_attributes_insert" ON product_attributes
  FOR INSERT TO authenticated
  WITH CHECK (is_authorized_user());

CREATE POLICY "product_attributes_update" ON product_attributes
  FOR UPDATE TO authenticated
  USING (is_authorized_user())
  WITH CHECK (is_authorized_user());

CREATE POLICY "product_attributes_delete" ON product_attributes
  FOR DELETE TO authenticated
  USING (is_authorized_user());

-- ============================================================================
-- CATEGORY 8: SYSTEM MISCELLANEOUS TABLES  
-- ============================================================================

-- Component Categories Table  
ALTER TABLE component_categories ENABLE ROW LEVEL SECURITY;

CREATE POLICY "component_categories_select" ON component_categories
  FOR SELECT TO authenticated
  USING (is_authorized_user());

CREATE POLICY "component_categories_insert" ON component_categories
  FOR INSERT TO authenticated
  WITH CHECK (is_authorized_user());

CREATE POLICY "component_categories_update" ON component_categories
  FOR UPDATE TO authenticated
  USING (is_authorized_user())
  WITH CHECK (is_authorized_user());

CREATE POLICY "component_categories_delete" ON component_categories
  FOR DELETE TO authenticated
  USING (is_authorized_user());

-- Production Cost Editing Sessions Table
ALTER TABLE production_cost_editing_sessions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "production_cost_editing_sessions_select" ON production_cost_editing_sessions
  FOR SELECT TO authenticated
  USING (is_authorized_user());

CREATE POLICY "production_cost_editing_sessions_insert" ON production_cost_editing_sessions
  FOR INSERT TO authenticated
  WITH CHECK (is_authorized_user());

CREATE POLICY "production_cost_editing_sessions_update" ON production_cost_editing_sessions
  FOR UPDATE TO authenticated
  USING (is_authorized_user())
  WITH CHECK (is_authorized_user());

CREATE POLICY "production_cost_editing_sessions_delete" ON production_cost_editing_sessions
  FOR DELETE TO authenticated
  USING (is_authorized_user());

-- System Announcements Table
ALTER TABLE system_announcements ENABLE ROW LEVEL SECURITY;

CREATE POLICY "system_announcements_select" ON system_announcements
  FOR SELECT TO authenticated
  USING (is_authorized_user());

-- System announcements are typically read-only for regular users
-- Only admins should be able to insert/update/delete announcements
-- No write policies = blocked by default for regular users

-- Rule Test Cases Table
ALTER TABLE rule_test_cases ENABLE ROW LEVEL SECURITY;

CREATE POLICY "rule_test_cases_select" ON rule_test_cases
  FOR SELECT TO authenticated
  USING (is_authorized_user());

CREATE POLICY "rule_test_cases_insert" ON rule_test_cases
  FOR INSERT TO authenticated
  WITH CHECK (is_authorized_user());

CREATE POLICY "rule_test_cases_update" ON rule_test_cases
  FOR UPDATE TO authenticated
  USING (is_authorized_user())
  WITH CHECK (is_authorized_user());

CREATE POLICY "rule_test_cases_delete" ON rule_test_cases
  FOR DELETE TO authenticated
  USING (is_authorized_user());

-- ============================================================================
-- CATEGORY 9: ERROR AND HISTORY TABLES
-- ============================================================================

-- Calculation Rules Errors Table
ALTER TABLE calculation_rules_errors ENABLE ROW LEVEL SECURITY;

CREATE POLICY "calculation_rules_errors_select" ON calculation_rules_errors
  FOR SELECT TO authenticated
  USING (is_authorized_user());

CREATE POLICY "calculation_rules_errors_insert" ON calculation_rules_errors
  FOR INSERT TO authenticated
  WITH CHECK (is_authorized_user());

-- Error tables are typically append-only (no update/delete)

-- Component Value History Table
ALTER TABLE component_value_history ENABLE ROW LEVEL SECURITY;

CREATE POLICY "component_value_history_select" ON component_value_history
  FOR SELECT TO authenticated
  USING (is_authorized_user());

CREATE POLICY "component_value_history_insert" ON component_value_history
  FOR INSERT TO authenticated
  WITH CHECK (is_authorized_user());

-- History tables are typically append-only (no update/delete)

-- Production Cost Calculation Errors Table  
ALTER TABLE production_cost_calculation_errors ENABLE ROW LEVEL SECURITY;

CREATE POLICY "production_cost_calculation_errors_select" ON production_cost_calculation_errors
  FOR SELECT TO authenticated
  USING (is_authorized_user());

CREATE POLICY "production_cost_calculation_errors_insert" ON production_cost_calculation_errors
  FOR INSERT TO authenticated
  WITH CHECK (is_authorized_user());

-- Error tables are typically append-only

-- Migration Errors Table
ALTER TABLE migration_errors ENABLE ROW LEVEL SECURITY;

CREATE POLICY "migration_errors_select" ON migration_errors
  FOR SELECT TO authenticated
  USING (is_authorized_user());

CREATE POLICY "migration_errors_insert" ON migration_errors
  FOR INSERT TO authenticated
  WITH CHECK (is_authorized_user());

-- Migration error tables are append-only

-- Product Deprecation Log Table
ALTER TABLE product_deprecation_log ENABLE ROW LEVEL SECURITY;

CREATE POLICY "product_deprecation_log_select" ON product_deprecation_log
  FOR SELECT TO authenticated
  USING (is_authorized_user());

CREATE POLICY "product_deprecation_log_insert" ON product_deprecation_log
  FOR INSERT TO authenticated
  WITH CHECK (is_authorized_user());

-- Log tables are typically append-only

-- ============================================================================
-- PERFORMANCE OPTIMIZATIONS
-- ============================================================================

-- Add performance indexes for RLS policies following Supabase best practices
-- These indexes support the is_authorized_user() function lookups

-- Index for authorized_users email lookups (used in is_authorized_user function)
-- This is critical for RLS performance
CREATE INDEX IF NOT EXISTS idx_authorized_users_email_active 
  ON authorized_users(email) WHERE is_active = true;

-- Note: Only creating essential indexes to avoid column dependency issues
-- Additional indexes can be added in future migrations after verifying column existence

-- ============================================================================
-- MIGRATION VALIDATION & LOGGING
-- ============================================================================

-- Verify all required functions exist
DO $$
BEGIN
  -- Check if our authorization functions exist
  IF NOT EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'is_authorized_user') THEN
    RAISE EXCEPTION 'Required function is_authorized_user() not found';
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'get_current_user_email') THEN
    RAISE EXCEPTION 'Required function get_current_user_email() not found';
  END IF;
  
  -- Log successful validation
  RAISE NOTICE 'RLS migration validation completed successfully';
END
$$;

-- Add comments to document the RLS strategy
COMMENT ON FUNCTION is_authorized_user() IS 'RLS function - checks if current user is authorized and active. Used in all organizational data policies.';
COMMENT ON FUNCTION get_current_user_email() IS 'RLS helper function - extracts current user email from JWT for authorization checks.';

-- Final summary comment
DO $$
DECLARE
  tables_with_rls INTEGER;
  policies_created INTEGER;
BEGIN
  -- Count tables with RLS enabled
  SELECT COUNT(*) INTO tables_with_rls
  FROM pg_class c
  JOIN pg_namespace n ON c.relnamespace = n.oid
  WHERE n.nspname = 'public'
  AND c.relkind = 'r'
  AND c.relrowsecurity = true;
  
  -- Count policies created  
  SELECT COUNT(*) INTO policies_created
  FROM pg_policies
  WHERE schemaname = 'public';
  
  RAISE NOTICE 'RLS Migration Summary: % tables protected with % policies total', tables_with_rls, policies_created;
END
$$;