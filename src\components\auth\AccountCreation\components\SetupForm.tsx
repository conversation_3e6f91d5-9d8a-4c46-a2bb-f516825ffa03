import React from 'react';
import { Button } from '../../../ui/button';
import { Input } from '../../../ui/input';
import { Label } from '../../../ui/label';
import { PasswordField } from '../../../ui/password-field';
import { ButtonLoading } from '../../../ui/loading-indicator';
import { ErrorMessage } from '../../../ui/message-display';
import type { AuthorizedUser } from '../../../../services/preAuthService';

interface SetupFormProps {
  email: string;
  authorizedUser: AuthorizedUser;
  
  // Admin fields
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  
  // Regular user fields
  pin: string;
  confirmPin: string;
  
  // Handlers
  onPasswordChange: (value: string) => void;
  onConfirmPasswordChange: (value: string) => void;
  onFirstNameChange: (value: string) => void;
  onLastNameChange: (value: string) => void;
  onPinChange: (value: string) => void;
  onConfirmPinChange: (value: string) => void;
  onKeyPress: (e: React.KeyboardEvent) => void;
  onSubmit: () => void;
  
  // State
  isCreating: boolean;
  error: string | null;
}

export const SetupForm: React.FC<SetupFormProps> = ({
  email,
  authorizedUser,
  password,
  confirmPassword,
  firstName,
  lastName,
  pin,
  confirmPin,
  onPasswordChange,
  onConfirmPasswordChange,
  onFirstNameChange,
  onLastNameChange,
  onPinChange,
  onConfirmPinChange,
  onKeyPress,
  onSubmit,
  isCreating,
  error
}) => {
  const isAdmin = authorizedUser?.role === 'admin' || authorizedUser?.role === 'administrator';

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="email" className="text-black text-sm font-medium">
          Email Address
        </Label>
        <Input
          id="email"
          type="email"
          value={email}
          disabled
          className="bg-gray-100 border-0 rounded-lg px-4 py-3 text-gray-600"
        />
        <p className="text-xs text-gray-500 mb-0">This email is pre-authorized for access</p>
      </div>

      {/* Name Fields - Side by side layout */}
      {isAdmin ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="firstName" className="text-black text-sm font-medium">
              First Name
            </Label>
            <Input
              id="firstName"
              type="text"
              value={firstName}
              onChange={(e) => onFirstNameChange(e.target.value)}
              onKeyPress={onKeyPress}
              placeholder="First name"
              className="bg-gray-100 border-0 rounded-lg px-4 py-3 text-gray-700 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-black transition-colors"
              disabled={isCreating}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="lastName" className="text-black text-sm font-medium">
              Last Name
            </Label>
            <Input
              id="lastName"
              type="text"
              value={lastName}
              onChange={(e) => onLastNameChange(e.target.value)}
              onKeyPress={onKeyPress}
              placeholder="Last name"
              className="bg-gray-100 border-0 rounded-lg px-4 py-3 text-gray-700 placeholder-gray-500 focus:bg-white focus:ring-2 focus:ring-black transition-colors"
              disabled={isCreating}
            />
          </div>
        </div>
      ) : (
        <div className="space-y-2">
          <Label htmlFor="name" className="text-black text-sm font-medium">
            Full Name
          </Label>
          <Input
            id="name"
            type="text"
            value={`${authorizedUser.first_name} ${authorizedUser.last_name}`}
            disabled
            className="bg-gray-100 border-0 rounded-lg px-4 py-3 text-gray-600"
          />
        </div>
      )}

      {/* Password Fields - Only for Admins */}
      {isAdmin ? (
        <div className="space-y-4">
          <PasswordField
            id="password"
            label="Password"
            placeholder="Create password"
            value={password}
            onChange={onPasswordChange}
            onKeyPress={onKeyPress}
            disabled={isCreating}
            required
            autoComplete="new-password"
            showStrengthIndicator
          />

          <PasswordField
            id="confirmPassword"
            label="Confirm Password"
            placeholder="Confirm password"
            value={confirmPassword}
            onChange={onConfirmPasswordChange}
            onKeyPress={onKeyPress}
            disabled={isCreating}
            required
            autoComplete="new-password"
            showMatchIndicator
            matchPassword={password}
          />
        </div>
      ) : (
        // PIN Fields for Regular Users  
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="pin" className="text-black text-sm font-medium">
              Create PIN (4-6 digits)
            </Label>
            <Input
              id="pin"
              type="password"
              value={pin}
              onChange={(e) => onPinChange(e.target.value.replace(/\D/g, '').slice(0, 6))}
              onKeyPress={onKeyPress}
              placeholder="Enter PIN"
              className="bg-gray-100 border-0 rounded-lg px-4 py-3 text-center text-lg tracking-wider"
              disabled={isCreating}
              maxLength={6}
              pattern="[0-9]*"
              inputMode="numeric"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="confirmPin" className="text-black text-sm font-medium">
              Confirm PIN
            </Label>
            <Input
              id="confirmPin"
              type="password"
              value={confirmPin}
              onChange={(e) => onConfirmPinChange(e.target.value.replace(/\D/g, '').slice(0, 6))}
              onKeyPress={onKeyPress}
              placeholder="Confirm PIN"
              className="bg-gray-100 border-0 rounded-lg px-4 py-3 text-center text-lg tracking-wider"
              disabled={isCreating}
              maxLength={6}
              pattern="[0-9]*"
              inputMode="numeric"
            />
            {pin && confirmPin && pin !== confirmPin && (
              <p className="text-red-500 text-xs">PINs do not match</p>
            )}
            {pin && pin.length < 4 && (
              <p className="text-gray-500 text-xs">PIN must be at least 4 digits</p>
            )}
          </div>
        </div>
      )}

      <Button
        onClick={onSubmit}
        disabled={isCreating || (
          isAdmin 
            ? (!password || !confirmPassword || password !== confirmPassword)
            : (!pin || !confirmPin || pin !== confirmPin || pin.length < 4)
        )}
        className="w-full bg-black hover:bg-gray-800 text-white py-3 rounded-lg font-medium transition-all duration-200 mt-6"
      >
        {isCreating ? (
          <ButtonLoading text="Creating Account..." />
        ) : (
          'Create Account'
        )}
      </Button>

      {error && (
        <ErrorMessage message={error} className="mt-4" />
      )}
    </div>
  );
};