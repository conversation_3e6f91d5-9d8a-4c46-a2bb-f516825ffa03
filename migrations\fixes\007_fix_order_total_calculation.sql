-- Fix the order total calculation to use discount_amount instead of original_amount
-- The discount_amount is the final price after discounts (what customer actually pays)
-- The original_amount is the base price before discounts

CREATE OR REPLACE FUNCTION update_order_financials()
RETURNS TRIGGER AS $$
DECLARE
    total NUMERIC := 0;
    paid NUMERIC := 0;
BEGIN
    -- Calculate total amount from order items using discount_amount (final price)
    -- If discount_amount is NULL, fall back to original_amount
    SELECT COALESCE(SUM(COALESCE(discount_amount, original_amount)), 0) INTO total
    FROM order_items
    WHERE order_id = NEW.order_id;
    
    -- Calculate amount paid from order payments
    SELECT COALESCE(SUM(payment_amount), 0) INTO paid
    FROM order_payments
    WHERE order_id = NEW.order_id;
    
    -- Update the order with calculated values
    UPDATE orders
    SET 
        total_amount = total,
        cash_paid = paid,
        payment_status = CASE
            WHEN total = 0 THEN 'Pending'  -- Handle zero total
            WHEN paid = 0 THEN 'Pending'
            WHEN paid >= total THEN 'Paid'
            ELSE 'Partially Paid'
        END
    WHERE order_id = NEW.order_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Update all existing orders to recalculate their totals
UPDATE orders SET order_id = order_id;  -- This will trigger the function for all orders