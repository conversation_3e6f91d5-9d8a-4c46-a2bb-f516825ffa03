-- Migration 005: Editing Session Management and Auto-Deprecation Conflict Prevention
-- This migration adds session management to prevent auto-deprecation conflicts

-- ============================================
-- STEP 1: Create Editing Sessions Table
-- ============================================

CREATE TABLE IF NOT EXISTS production_cost_editing_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    
    -- Session identification
    session_id TEXT NOT NULL,
    user_id TEXT,
    
    -- Product combination being edited
    product_category_id UUID REFERENCES product_attributes(id),
    product_type_id UUID REFERENCES product_attributes(id),
    size_id UUID REFERENCES product_attributes(id),
    
    -- Session metadata
    operation_type TEXT NOT NULL CHECK (operation_type IN ('component_edit', 'template_application', 'batch_update')),
    editor_context JSONB,
    
    -- Timing
    started_at TIMESTAMPTZ DEFAULT NOW(),
    last_activity_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '30 minutes'),
    
    -- Status
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'expired', 'aborted')),
    
    -- Conflict prevention
    prevent_auto_deprecation BOOLEAN DEFAULT true,
    
    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_editing_sessions_active ON production_cost_editing_sessions(status, expires_at) 
WHERE status = 'active';

CREATE INDEX idx_editing_sessions_product ON production_cost_editing_sessions(
    product_category_id, product_type_id, size_id, status
) WHERE status = 'active';

CREATE INDEX idx_editing_sessions_user ON production_cost_editing_sessions(user_id, status)
WHERE status = 'active';

-- Unique constraint to prevent multiple active sessions for same product combination
CREATE UNIQUE INDEX unique_active_editing_session ON production_cost_editing_sessions(
    product_category_id, product_type_id, size_id
) WHERE status = 'active';

-- ============================================
-- STEP 2: Session Management Functions
-- ============================================

-- Function to start an editing session
CREATE OR REPLACE FUNCTION start_editing_session(
    p_session_id TEXT,
    p_user_id TEXT,
    p_category_id UUID,
    p_product_type_id UUID,
    p_size_id UUID,
    p_operation_type TEXT DEFAULT 'component_edit',
    p_editor_context JSONB DEFAULT '{}'::jsonb
) RETURNS UUID AS $$
DECLARE
    v_session_uuid UUID;
    v_existing_session UUID;
BEGIN
    -- Check for existing active session for this product combination
    SELECT id INTO v_existing_session
    FROM production_cost_editing_sessions
    WHERE product_category_id = p_category_id
      AND product_type_id = p_product_type_id
      AND size_id = p_size_id
      AND status = 'active'
      AND expires_at > NOW();
    
    IF v_existing_session IS NOT NULL THEN
        -- If same user, update existing session
        IF EXISTS (
            SELECT 1 FROM production_cost_editing_sessions 
            WHERE id = v_existing_session AND user_id = p_user_id
        ) THEN
            UPDATE production_cost_editing_sessions
            SET last_activity_at = NOW(),
                expires_at = NOW() + INTERVAL '30 minutes',
                updated_at = NOW()
            WHERE id = v_existing_session;
            
            RETURN v_existing_session;
        ELSE
            -- Different user - throw conflict error
            RAISE EXCEPTION 'EDITING_CONFLICT: Product combination is currently being edited by another user. Session ID: %', v_existing_session;
        END IF;
    END IF;
    
    -- Create new editing session
    INSERT INTO production_cost_editing_sessions (
        session_id,
        user_id,
        product_category_id,
        product_type_id,
        size_id,
        operation_type,
        editor_context
    ) VALUES (
        p_session_id,
        p_user_id,
        p_category_id,
        p_product_type_id,
        p_size_id,
        p_operation_type,
        p_editor_context
    ) RETURNING id INTO v_session_uuid;
    
    RETURN v_session_uuid;
END;
$$ LANGUAGE plpgsql;

-- Function to end an editing session
CREATE OR REPLACE FUNCTION end_editing_session(
    p_session_id TEXT,
    p_status TEXT DEFAULT 'completed'
) RETURNS BOOLEAN AS $$
BEGIN
    UPDATE production_cost_editing_sessions
    SET status = p_status,
        updated_at = NOW()
    WHERE session_id = p_session_id
      AND status = 'active';
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Function to check if auto-deprecation is blocked for a product
CREATE OR REPLACE FUNCTION is_auto_deprecation_blocked(
    p_category_id UUID,
    p_product_type_id UUID,
    p_size_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
    v_blocked BOOLEAN;
BEGIN
    -- Check if there's an active editing session that prevents auto-deprecation
    SELECT EXISTS (
        SELECT 1 FROM production_cost_editing_sessions
        WHERE product_category_id = p_category_id
          AND product_type_id = p_product_type_id
          AND size_id = p_size_id
          AND status = 'active'
          AND prevent_auto_deprecation = true
          AND expires_at > NOW()
    ) INTO v_blocked;
    
    RETURN v_blocked;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up expired sessions
CREATE OR REPLACE FUNCTION cleanup_expired_editing_sessions() RETURNS INTEGER AS $$
DECLARE
    v_expired_count INTEGER;
BEGIN
    UPDATE production_cost_editing_sessions
    SET status = 'expired',
        updated_at = NOW()
    WHERE status = 'active'
      AND expires_at <= NOW();
    
    GET DIAGNOSTICS v_expired_count = ROW_COUNT;
    
    RETURN v_expired_count;
END;
$$ LANGUAGE plpgsql;

-- ============================================
-- STEP 3: Update Auto-Deprecation Function
-- ============================================

-- Enhanced auto-deprecation function with session awareness
CREATE OR REPLACE FUNCTION auto_deprecate_on_cost_removal() RETURNS TRIGGER AS $$
BEGIN
    -- Check if auto-deprecation is temporarily disabled for this session
    IF current_setting('production_cost.skip_auto_deprecation', true) = 'true' THEN
        RETURN OLD;
    END IF;
    
    -- Check if there's an active editing session that blocks auto-deprecation
    IF is_auto_deprecation_blocked(
        OLD.product_category_id,
        OLD.product_type_id,
        OLD.size_id
    ) THEN
        RAISE WARNING 'Auto-deprecation blocked: Product is currently being edited. Session exists for category_id: %, product_type_id: %, size_id: %',
            OLD.product_category_id, OLD.product_type_id, OLD.size_id;
        RETURN OLD;
    END IF;
    
    -- When all cost data is removed, mark combination as deprecated
    UPDATE product_line 
    SET 
        deprecation_status = 'deprecated_cost_removed',
        deprecation_reason = 'All production cost data removed',
        deprecated_at = NOW(),
        deprecated_by = 'system_auto'
    WHERE category_id = OLD.product_category_id 
      AND product_type_id = OLD.product_type_id 
      AND size_id = OLD.size_id
      AND deprecation_status = 'active'  -- Only deprecate active combinations
      AND NOT EXISTS (
        SELECT 1 FROM production_cost_component_values 
        WHERE product_category_id = OLD.product_category_id 
          AND product_type_id = OLD.product_type_id 
          AND size_id = OLD.size_id 
          AND is_current = true
      );
    
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- ============================================
-- STEP 4: Session Activity Tracking
-- ============================================

-- Function to update session activity
CREATE OR REPLACE FUNCTION update_session_activity(
    p_session_id TEXT
) RETURNS BOOLEAN AS $$
BEGIN
    UPDATE production_cost_editing_sessions
    SET last_activity_at = NOW(),
        expires_at = NOW() + INTERVAL '30 minutes',
        updated_at = NOW()
    WHERE session_id = p_session_id
      AND status = 'active';
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update session activity on component value changes
CREATE OR REPLACE FUNCTION track_editing_activity() RETURNS TRIGGER AS $$
DECLARE
    v_session_id TEXT;
BEGIN
    -- Find active editing session for this product combination
    SELECT session_id INTO v_session_id
    FROM production_cost_editing_sessions
    WHERE product_category_id = COALESCE(NEW.product_category_id, OLD.product_category_id)
      AND product_type_id = COALESCE(NEW.product_type_id, OLD.product_type_id)
      AND size_id = COALESCE(NEW.size_id, OLD.size_id)
      AND status = 'active'
      AND expires_at > NOW()
    LIMIT 1;
    
    -- Update session activity if found
    IF v_session_id IS NOT NULL THEN
        PERFORM update_session_activity(v_session_id);
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Add trigger to track activity
DROP TRIGGER IF EXISTS track_component_editing_activity ON production_cost_component_values;
CREATE TRIGGER track_component_editing_activity
    AFTER INSERT OR UPDATE OR DELETE ON production_cost_component_values
    FOR EACH ROW
    EXECUTE FUNCTION track_editing_activity();

-- ============================================
-- STEP 5: Session Cleanup Scheduler
-- ============================================

-- View for monitoring editing sessions
CREATE OR REPLACE VIEW editing_sessions_monitor AS
SELECT 
    es.id,
    es.session_id,
    es.user_id,
    pa1.value as category_name,
    pa2.value as product_type_name,
    pa3.value as size_name,
    es.operation_type,
    es.started_at,
    es.last_activity_at,
    es.expires_at,
    es.status,
    es.prevent_auto_deprecation,
    EXTRACT(EPOCH FROM (es.expires_at - NOW())) / 60 as minutes_until_expiry
FROM production_cost_editing_sessions es
LEFT JOIN product_attributes pa1 ON es.product_category_id = pa1.id
LEFT JOIN product_attributes pa2 ON es.product_type_id = pa2.id
LEFT JOIN product_attributes pa3 ON es.size_id = pa3.id
ORDER BY es.started_at DESC;

-- ============================================
-- STEP 6: Enhanced Auto-Deprecation Service Function
-- ============================================

-- Session-aware auto-deprecation function
CREATE OR REPLACE FUNCTION auto_deprecate_with_session_check(
    p_category_id UUID,
    p_product_type_id UUID,
    p_size_id UUID,
    p_reason TEXT DEFAULT 'Auto-deprecated: Basic cost data removed'
) RETURNS JSONB AS $$
DECLARE
    v_result JSONB;
    v_has_basic_costs BOOLEAN;
    v_is_blocked BOOLEAN;
BEGIN
    -- Clean up expired sessions first
    PERFORM cleanup_expired_editing_sessions();
    
    -- Check if auto-deprecation is blocked by active editing session
    v_is_blocked := is_auto_deprecation_blocked(p_category_id, p_product_type_id, p_size_id);
    
    IF v_is_blocked THEN
        v_result := jsonb_build_object(
            'success', false,
            'reason', 'blocked_by_editing_session',
            'message', 'Auto-deprecation blocked: Product is currently being edited'
        );
        RETURN v_result;
    END IF;
    
    -- Check if product has basic costs (labor + materials)
    SELECT EXISTS (
        SELECT 1 FROM production_cost_component_values pcv
        JOIN production_cost_components pc ON pcv.component_id = pc.id
        WHERE pcv.product_category_id = p_category_id
          AND pcv.product_type_id = p_product_type_id
          AND pcv.size_id = p_size_id
          AND pcv.is_current = true
          AND pc.category IN ('labor', 'materials')
        GROUP BY pcv.product_category_id, pcv.product_type_id, pcv.size_id
        HAVING COUNT(DISTINCT pc.category) >= 2  -- Must have both labor AND materials
    ) INTO v_has_basic_costs;
    
    IF v_has_basic_costs THEN
        v_result := jsonb_build_object(
            'success', false,
            'reason', 'has_basic_costs',
            'message', 'Product has sufficient basic costs - no deprecation needed'
        );
        RETURN v_result;
    END IF;
    
    -- Proceed with auto-deprecation
    UPDATE product_line 
    SET 
        deprecation_status = 'deprecated_cost_removed',
        deprecation_reason = p_reason,
        deprecated_at = NOW(),
        deprecated_by = 'auto_safeguard',
        updated_at = NOW()
    WHERE category_id = p_category_id 
      AND product_type_id = p_product_type_id 
      AND size_id = p_size_id
      AND deprecation_status = 'active';
    
    v_result := jsonb_build_object(
        'success', true,
        'reason', 'deprecated',
        'message', 'Product successfully auto-deprecated'
    );
    
    RETURN v_result;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
-- GRANT ALL ON production_cost_editing_sessions TO authenticated;
-- GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;