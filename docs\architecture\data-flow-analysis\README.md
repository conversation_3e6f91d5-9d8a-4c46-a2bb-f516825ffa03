# Data Flow Analysis Report: ProductCostTable vs ProductionCostItemViewSheet

## Executive Summary

After analyzing the data flow between the ProductCostTable and ProductionCostItemViewSheet, I've identified **significant data source inconsistencies** that explain calculation discrepancies and missing data issues.

## Key Findings

### 1. **Data Source Mismatch**
- **Table Data Source**: `useProductLineProductionCosts` hook → `product_line_view` + combination-based queries
- **View Sheet Data Source**: `getComponentValuesForCombination` → direct component values query
- **Problem**: The table uses pre-aggregated/calculated data while the view sheet fetches raw component data

### 2. **Calculation Method Differences**

#### Table Calculations (useProductLineProductionCosts.ts):
```typescript
// Uses template-based calculations with simple_sum
const templateCosts = appliedTemplates.map(template => {
  if (template.cost_value && template.cost_value > 0) {
    // Use stored calculation result if available
    return template.cost_value;
  }
  
  // Calculate using simple sum of component values for this template
  if (template.selected_components && Array.isArray(template.selected_components)) {
    const templateSum = template.selected_components.reduce((sum, componentCode) => {
      return sum + (componentValues[componentCode] || 0);
    }, 0);
    return templateSum;
  }
  
  return 0;
});

// Total cost is sum of all template calculations
const totalCost = templateCosts.reduce((sum, cost) => sum + cost, 0);
```

#### View Sheet Calculations (ProductionCostItemViewSheet.tsx):
```typescript
// Uses raw component sum - NO template awareness
const totalCost = product?.total_production_cost || 0;
// Component details loaded separately via getComponentValuesForCombination
const details = await getComponentValuesForCombination(categoryId, productTypeId, sizeId);
```

### 3. **Data Flow Architecture Issues**

#### Current Flow:
```
Database Tables:
├── product_line_view (aggregated view)
├── production_cost_component_values (raw component data)
├── template_applications (applied templates)
└── template_calculation_results (stored calculations)

Table Component:
useProductLineProductionCosts → Fetches from multiple sources → Calculates with template logic

View Sheet:
getComponentValuesForCombination → Fetches only raw component values → No template awareness
```

## Detailed Issues

### 1. **Component Count Inconsistency**
- **Table**: Uses `componentCountMap` with combination-based aggregation
- **View Sheet**: Uses `componentDetails.length` from direct query
- **Impact**: Different component counts displayed

### 2. **Cost Calculation Logic Mismatch**
- **Table**: Template-aware calculations with `simple_sum` method
- **View Sheet**: Raw component sum without template context
- **Impact**: Different total costs displayed

### 3. **Template Context Missing in View Sheet**
- **Table**: Full template application context with `applied_templates`
- **View Sheet**: No template selection awareness for component filtering
- **Impact**: Shows all components instead of template-selected ones

### 4. **Data Transformation Inconsistencies**

#### Table Data Structure:
```typescript
{
  id: item.id,
  total_production_cost: totalCost, // Template-calculated
  components_count: actualComponentsCount, // Combination-based count
  component_values: componentValues, // Combination-based mapping
  applied_templates: appliedTemplates, // Full template context
  // ... other fields
}
```

#### View Sheet Expected Structure:
```typescript
{
  // Expects template-calculated costs but gets raw component data
  total_production_cost: number,
  category_id: string,
  product_type_id: string,
  size_id: string
}
```

## Root Cause Analysis

### Primary Issue: Dual Data Architecture
The system maintains two parallel data access patterns:
1. **Product Line Pattern**: Combination-based storage (`category_id + product_type_id + size_id`)
2. **Legacy Product Pattern**: Product ID-based storage

### Secondary Issues:
1. **Missing Template Context in View Sheet**: The view sheet doesn't apply template-selection logic
2. **Inconsistent Calculation Methods**: Table uses template-aware calculations, view sheet uses raw sums
3. **Data Mapping Misalignment**: Different field mappings between data sources

## Recommendations

### 1. **Immediate Fix: Align View Sheet Data Source**
Update `ProductionCostItemViewSheet` to use the same data source as the table:

```typescript
// Instead of loading component details separately
// Use the product data passed from the table which already has correct calculations
const componentDetails = product.component_values || {};
const appliedTemplates = product.applied_templates || [];
```

### 2. **Enhanced Component Loading for View Sheet**
Create a new service method that respects template selections:

```typescript
export const getTemplateAwareComponentValues = async (
  categoryId: string,
  productTypeId: string,
  sizeId: string
) => {
  // 1. Get applied templates
  // 2. Get only components selected by those templates
  // 3. Return filtered component values with template context
};
```

### 3. **Unified Data Access Pattern**
Standardize all components to use the `useProductLineProductionCosts` pattern:

```typescript
// Consistent data structure across all components
interface ProductCostData {
  id: string;
  total_production_cost: number; // Template-calculated
  components_count: number; // Template-aware count
  component_values: Record<string, number>; // Template-selected components only
  applied_templates: TemplateInfo[];
  // ... standardized fields
}
```

### 4. **Service Method Alignment**
Update `getComponentValuesForCombination` to include template awareness:

```typescript
export const getComponentValuesForCombination = async (
  productCategoryId: string,
  productTypeId: string,
  sizeId: string,
  includeTemplateContext: boolean = true
) => {
  // Include template filtering logic when includeTemplateContext is true
};
```

## Impact Assessment

### Current Issues:
- ❌ View sheet shows wrong component counts
- ❌ View sheet shows wrong total costs
- ❌ View sheet displays all components instead of template-selected ones
- ❌ Inconsistent data between table and detail view
- ❌ User confusion due to mismatched numbers

### After Fixes:
- ✅ Consistent calculations across all views
- ✅ Template-aware component filtering
- ✅ Unified data architecture
- ✅ Improved user experience
- ✅ Reliable cost calculations

## Files That Need Updates

1. **src/pages/ProductionCost/components/ProductCostTab/ProductionCostItemViewSheet.tsx**
   - Update to use table's data structure
   - Remove separate component loading
   - Use template-aware calculations

2. **src/pages/ProductionCost/services/productionCost.service.ts**
   - Add template-aware parameter to `getComponentValuesForCombination`
   - Create `getTemplateAwareComponentValues` method

3. **src/pages/ProductionCost/components/ProductCostTab/ProductCostManagerContainer.tsx**
   - Ensure proper data passing to view sheet
   - Validate data structure consistency

## Conclusion

The main issue is architectural: the table and view sheet use different data sources and calculation methods. The table correctly implements template-aware calculations while the view sheet uses raw component queries. Aligning both to use the same template-aware data source will resolve all calculation inconsistencies.

## 1. Data Flow Patterns

### 1.1 Revival Operations Data Flow

```
User Action → Revival Workflow → Validation → Template Application → Database → Cache Invalidation → UI Update
```

**Detailed Flow:**
1. **Initiation**: User triggers revival from deprecated product list
2. **Workflow Setup**: `RevivalWorkflow.tsx` initializes with product data
3. **Template Selection**: User selects basic_cost template via `TemplateSelection`
4. **Value Configuration**: `ConfigureValues` component manages component values
5. **Validation**: `templateValidation.service.ts` validates business rules
6. **Application**: `templateApplicationService.ts` executes atomic transaction
7. **Cache Update**: `cache.service.ts` invalidates relevant SWR caches
8. **UI Refresh**: Components re-render with fresh data

### 1.2 Editing Operations Data Flow

```
User Action → Editor Component → Real-time Validation → Optimistic Update → API Call → Cache Sync → UI Update
```

**Detailed Flow:**
1. **Initiation**: User opens `ProductCostEditor.tsx` for existing product
2. **Data Loading**: `useProductComponentValues` hook fetches current values
3. **Form Interaction**: Real-time validation and calculation via `useCostCalculation`
4. **Optimistic Updates**: UI updates immediately on user input
5. **Persistence**: API call to update component values
6. **Error Handling**: Rollback on failure, confirmation on success
7. **Cache Synchronization**: SWR cache updated with new values

## 2. State Management Architecture

### 2.1 State Layers

```
┌─────────────────────────────────────────┐
│               UI State                  │
│ (Component local state, form state)    │
├─────────────────────────────────────────┤
│              SWR Cache                  │
│ (Server state, automatic revalidation) │
├─────────────────────────────────────────┤
│           React Context                 │
│ (Shared state, authentication)         │
├─────────────────────────────────────────┤
│          Database State                 │
│ (Persistent data, source of truth)     │
└─────────────────────────────────────────┘
```

### 2.2 SWR Integration Patterns

**Key Pattern**: Custom hooks with optimistic updates
- `useProductionCostSWR.ts`: Main components management
- `useComponentValuesSWR.ts`: Component values with caching
- `useTemplatesSWR.ts`: Template data with filtering
- `useProductLineProductionCosts.ts`: Applied template results

**Cache Key Strategy**:
```typescript
// Parameterized cache keys
getProductionCostComponentsKey(filters?: ComponentFilters)
getProductLineProductionCostsKey()
getExistingBasicCostCombinationsKey()
```

### 2.3 Context Architecture

**AuthContext**: Mock authentication (open access mode)
- Provides consistent user object
- Enables auth-ready patterns without actual authentication
- Sets auth ready state immediately

**RealtimeContext**: Selective real-time subscriptions
- Disabled by default for performance
- Route-based subscription filtering
- Circuit breaker pattern for failure handling

## 3. Integration Points

### 3.1 Revival → Main System Integration

```
RevivalWorkflow → TemplateApplicationService → Database Transaction → Cache Invalidation → ProductMatrix Refresh
```

**Key Integration Components:**
- `applyTemplateWithTransaction()`: Atomic template application
- `validateBatchTemplateApplication()`: Safeguard system
- `invalidateProductionCostCaches()`: Cache management
- `ProductCostConfiguration`: Shared configuration UI

### 3.2 Editing → Main System Integration

```
ProductCostEditor → ComponentValues Service → Optimistic Updates → SWR Sync → UI Refresh
```

**Integration Points:**
- `useProductComponentValues`: Data loading
- `batchUpdateComponentValues()`: Bulk updates
- `productCostValidator`: Real-time validation
- `CostCalculationEngine`: Live calculations

## 4. Cache Invalidation & Data Consistency

### 4.1 Cache Management Strategy

**Cache Invalidation Hierarchy:**
1. **Specific Cache**: Target exact data that changed
2. **Related Caches**: Invalidate dependent data
3. **Global Refresh**: Full system refresh as fallback

**Implementation:**
```typescript
// Targeted invalidation
await invalidateSpecificCache(getProductLineProductionCostsKey())

// Batch invalidation  
await invalidateProductionCostCaches()

// Fallback
window.location.reload()
```

### 4.2 Data Consistency Patterns

**Optimistic Updates with Rollback:**
```typescript
// 1. Update UI optimistically
await mutateFn(optimisticData, false)

// 2. Make API call
const result = await apiCall()

// 3. Update with real data or rollback
await mutateFn(result.success ? realData : rollback)
```

**Atomic Transactions:**
- Database-level transactions via `apply_template_with_transaction` RPC
- All-or-nothing approach prevents partial states
- Validation before execution prevents conflicts

## 5. Error Propagation & Handling

### 5.1 Error Handling Layers

```
Database Errors → Service Layer → SWR Error Boundary → Component Error State → User Feedback
```

**Error Types:**
- **Validation Errors**: Business rule violations
- **Network Errors**: API communication failures  
- **Conflict Errors**: Concurrent modification issues
- **System Errors**: Unexpected failures

### 5.2 Error Recovery Patterns

**Template Application Errors:**
```typescript
try {
  result = await applyTemplateWithTransaction(data)
} catch (atomicError) {
  // Fallback to manual transaction
  result = await applyTemplateManualTransaction(data)
}
```

**SWR Error Recovery:**
```typescript
// Automatic retry with exponential backoff
errorRetryCount: 3,
errorRetryInterval: 5000,
shouldRetryOnError: true
```

## 6. Optimistic Updates Implementation

### 6.1 Update Pattern

```typescript
// Standard optimistic update pattern
export async function updateProductionCostComponentSWR(
  id: string,
  data: UpdateComponentData,
  mutateFn?: any
): Promise<ProductionCostComponent> {
  try {
    // 1. Optimistic update
    await mutateFn(optimisticData, false)
    
    // 2. API call
    const result = await updateProductionCostComponent(id, data)
    
    // 3. Real data update
    await mutateFn(realData, false)
    
    return result
  } catch (error) {
    // 4. Rollback on error
    await mutateFn()
    throw error
  }
}
```

### 6.2 User Experience Benefits

- **Immediate Feedback**: UI responds instantly to user actions
- **Progressive Enhancement**: Works without network, syncs when available
- **Error Recovery**: Graceful degradation on failure
- **Consistent State**: SWR ensures data consistency across components

## 7. Data Persistence Points

### 7.1 Database Operations

**Template Application:**
- `product_line` table: Product combinations
- `production_cost_component_values` table: Component values
- `template_applications` table: Applied template tracking
- Business rule triggers: Auto-deprecation safeguards

**Component Editing:**
- Direct updates to `production_cost_component_values`
- Optimistic UI updates via SWR
- Validation before persistence

### 7.2 Transaction Boundaries

**Atomic Template Application:**
```sql
-- Single transaction for all operations
BEGIN;
  INSERT INTO product_line ...;
  INSERT INTO production_cost_component_values ...;
  INSERT INTO template_applications ...;
COMMIT;
```

**Component Updates:**
```sql
-- Individual updates with optimistic UI
UPDATE production_cost_component_values 
SET value = ?, updated_at = NOW()
WHERE id = ?;
```

## 8. Event Handling & User Interaction Flows

### 8.1 Revival Workflow Events

```
ProductList.deprecatedProduct.click
  → RevivalWorkflow.open
  → TemplateSelection.change
  → ConfigureValues.input
  → ReviewAndApply.submit
  → TemplateApplication.execute
  → CacheInvalidation.trigger
  → UI.refresh
```

### 8.2 Editing Workflow Events

```
ProductCostTable.product.edit
  → ProductCostEditor.open
  → ComponentValueEditor.change
  → CostCalculation.update
  → Validation.check
  → Save.optimistic
  → API.call
  → SWR.sync
```

## 9. Authentication & Authorization Integration

### 9.1 Current State (Open Access)

**Mock Authentication:**
- `AuthContext` provides mock user/session
- All API calls succeed without auth checks
- `authReadyState.setReady(true)` enables immediate operation

**Auth-Ready Patterns:**
- `withAuthReady()` wrapper for all API calls
- Consistent auth context consumption
- Easy migration to real authentication

### 9.2 Migration Path for Real Auth

```typescript
// Current (mock)
const mockUser = { id: 'open-access-user' }

// Future (real auth)
const { user } = useAuth() // Real Supabase auth
```

**RLS Integration Ready:**
- Database tables have user_id columns
- Policies can be enabled when auth is activated
- No code changes needed in business logic

## 10. Architecture Assessment

### 10.1 Strengths

1. **Proper Separation of Concerns**: Clear boundaries between UI, business logic, and data
2. **Optimistic Updates**: Excellent user experience with immediate feedback
3. **Error Resilience**: Multiple fallback mechanisms and graceful degradation
4. **Cache Management**: Sophisticated SWR integration with targeted invalidation
5. **Transaction Safety**: Atomic operations prevent data corruption
6. **Validation System**: Comprehensive safeguards prevent business rule violations

### 10.2 Potential Bottlenecks

1. **Complex Cache Dependencies**: Multiple cache keys need coordination
2. **Large Transaction Scope**: Template application can be heavy for many products
3. **Component Value Complexity**: Deep object structures in SWR cache
4. **Real-time Overhead**: When enabled, may create performance issues

### 10.3 Missing Connections

1. **Background Sync**: No offline support for complex operations
2. **Conflict Resolution**: Limited handling of concurrent edits
3. **Performance Monitoring**: No metrics for cache hit rates or operation timing
4. **Audit Trail**: Limited tracking of who changed what when

### 10.4 Recommendations

1. **Add Background Jobs**: For heavy template applications
2. **Implement Optimistic Locking**: Prevent concurrent edit conflicts  
3. **Add Performance Metrics**: Monitor cache effectiveness
4. **Enhance Audit Logging**: Track all changes with user attribution
5. **Consider Partial Template Application**: Batch operations in smaller chunks

## Conclusion

The production cost system demonstrates a mature, well-architected approach to data flow and state management. The revival and editing workflows are properly integrated with the main system through shared components, consistent patterns, and robust error handling. The SWR-based architecture provides excellent user experience while maintaining data consistency and system reliability.

The system is ready for production use with proper safeguards, validation, and performance considerations. The auth-ready patterns make future authentication integration straightforward, and the modular architecture supports continued evolution and enhancement.