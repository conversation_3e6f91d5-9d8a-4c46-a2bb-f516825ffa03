/**
 * Permission Registry Service - Database operations for permissions and roles
 * 
 * Handles CRUD operations for permissions registry and roles
 * Following CLAUDE.md guidelines: <250 lines, single responsibility
 */

import { supabase } from '../../lib/supabase';
import type { Permission, Role } from '../../types/permissions.types';

// ============================================================================
// PERMISSION REGISTRY SERVICE
// ============================================================================

export class PermissionRegistryService {
  /**
   * Get all available permissions from the database
   */
  static async getAllPermissions(): Promise<Permission[]> {
    try {
      const { data, error } = await supabase
        .rpc('get_all_permissions');

      if (error) {
        console.error('Failed to fetch permissions:', error);
        return [];
      }

      return (data || []).map((row: any) => ({
        id: row.id || 0,
        key: row.permission_key,
        name: row.permission_name,
        description: row.description,
        category: row.category,
        isActive: true,
        requiresPermissions: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }));

    } catch (error) {
      console.error('Get all permissions error:', error);
      return [];
    }
  }

  /**
   * Get all available roles from the database
   */
  static async getAllRoles(): Promise<Role[]> {
    try {
      const { data, error } = await supabase
        .from('roles')
        .select('*')
        .eq('is_active', true)
        .order('display_name');

      if (error) {
        console.error('Failed to fetch roles:', error);
        return [];
      }

      return (data || []).map(row => ({
        id: row.id,
        name: row.name,
        displayName: row.display_name,
        description: row.description,
        permissions: row.permissions || [],
        isActive: row.is_active,
        isSystemRole: row.is_system_role,
        createdAt: row.created_at,
        updatedAt: row.updated_at
      }));

    } catch (error) {
      console.error('Get all roles error:', error);
      return [];
    }
  }

  /**
   * Get permission categories summary
   */
  static async getPermissionCategorySummary(): Promise<Array<{
    category: string;
    totalPermissions: number;
    activePermissions: number;
  }>> {
    try {
      const { data, error } = await supabase
        .from('permission_categories_summary')
        .select('*')
        .order('category');

      if (error) {
        console.error('Failed to fetch permission categories:', error);
        return [];
      }

      return (data || []).map(row => ({
        category: row.category,
        totalPermissions: row.total_permissions,
        activePermissions: row.active_permissions
      }));

    } catch (error) {
      console.error('Permission category summary error:', error);
      return [];
    }
  }

  /**
   * Get role complexity summary
   */
  static async getRoleComplexitySummary(): Promise<Array<{
    name: string;
    displayName: string;
    permissionCount: number;
    isActive: boolean;
    isSystemRole: boolean;
  }>> {
    try {
      const { data, error } = await supabase
        .from('role_complexity_summary')
        .select('*')
        .order('permission_count', { ascending: false });

      if (error) {
        console.error('Failed to fetch role complexity:', error);
        return [];
      }

      return (data || []).map(row => ({
        name: row.name,
        displayName: row.display_name,
        permissionCount: row.permission_count,
        isActive: row.is_active,
        isSystemRole: row.is_system_role
      }));

    } catch (error) {
      console.error('Role complexity summary error:', error);
      return [];
    }
  }
}