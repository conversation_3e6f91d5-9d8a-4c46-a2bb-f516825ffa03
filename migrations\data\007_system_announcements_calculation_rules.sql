-- Migration: Dynamic Announcements System for Calculation Rules Errors
-- This migration creates the foundation for database-driven announcements
-- specifically focused on calculation rules error reporting

-- ============================================
-- STEP 1: System Announcements Table
-- ============================================

CREATE TABLE IF NOT EXISTS system_announcements (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    
    -- Core Announcement Details  
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('info', 'warning', 'success', 'update', 'new', 'error')),
    priority TEXT NOT NULL CHECK (priority IN ('low', 'medium', 'high', 'critical')),
    
    -- Categorization (focused on calculation rules for now)
    category TEXT DEFAULT 'general' CHECK (category IN ('general', 'calculation_rules', 'production_cost')),
    source_component TEXT DEFAULT 'system',
    
    -- Targeting & Display
    target_audience TEXT[] DEFAULT ARRAY['admin'], -- Start with admin-only
    display_until TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '7 days'), -- Auto-expire in 7 days
    
    -- Status & Interaction
    is_active BOOLEAN DEFAULT true,
    is_dismissible BOOLEAN DEFAULT true,
    auto_dismiss_after_seconds INTEGER DEFAULT 30, -- 30 second auto-dismiss
    
    -- Error Context (specific to calculation rules)
    error_context JSONB,
    related_entity_type TEXT, -- 'calculation_rule', 'product_category'
    related_entity_id TEXT,
    
    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    created_by TEXT DEFAULT 'system',
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- User Interaction Tracking
    view_count INTEGER DEFAULT 0,
    dismiss_count INTEGER DEFAULT 0
);

-- Indexes for performance
CREATE INDEX idx_announcements_active_priority ON system_announcements(is_active, priority, created_at DESC);
CREATE INDEX idx_announcements_category ON system_announcements(category, is_active);
CREATE INDEX idx_announcements_expiry ON system_announcements(display_until) WHERE display_until IS NOT NULL;

-- ============================================
-- STEP 2: Calculation Rules Error Log
-- ============================================

CREATE TABLE IF NOT EXISTS calculation_rules_errors (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    
    -- Error Classification
    error_type TEXT NOT NULL CHECK (error_type IN (
        'missing_calculation_rule',
        'rule_mismatch', 
        'invalid_rule_type',
        'calculation_failure'
    )),
    error_message TEXT NOT NULL,
    severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    
    -- Context Information
    product_category TEXT,
    expected_rule_type TEXT,
    available_rules JSONB, -- Store what rules were found
    
    -- Order Context (when error occurs during production cost calculation)
    order_item_id UUID,
    quantity INTEGER,
    nos INTEGER,
    batch_operation_id UUID,
    
    -- Resolution Tracking
    resolution_status TEXT DEFAULT 'unresolved' CHECK (resolution_status IN ('unresolved', 'resolved', 'ignored')),
    resolution_notes TEXT,
    resolved_at TIMESTAMPTZ,
    
    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Link to announcement if created
    announcement_id UUID REFERENCES system_announcements(id)
);

-- Indexes
CREATE INDEX idx_calc_errors_type_status ON calculation_rules_errors(error_type, resolution_status);
CREATE INDEX idx_calc_errors_category ON calculation_rules_errors(product_category, created_at);

-- ============================================
-- STEP 3: Helper Functions
-- ============================================

-- Function to increment announcement view count
CREATE OR REPLACE FUNCTION increment_announcement_view_count(announcement_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE system_announcements 
    SET view_count = view_count + 1,
        updated_at = NOW()
    WHERE id = announcement_id;
END;
$$ LANGUAGE plpgsql;

-- Function to create calculation rules error announcement
CREATE OR REPLACE FUNCTION create_calculation_rules_error_announcement(
    p_error_id UUID,
    p_product_category TEXT,
    p_error_type TEXT,
    p_suggested_action TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    v_announcement_id UUID;
    v_title TEXT;
    v_message TEXT;
    v_priority TEXT;
BEGIN
    -- Determine announcement details based on error type
    CASE p_error_type
        WHEN 'missing_calculation_rule' THEN
            v_title := 'Missing Calculation Rule';
            v_message := 'No calculation rule found for "' || p_product_category || '" products. Production costs may be inaccurate.';
            v_priority := 'high';
        WHEN 'rule_mismatch' THEN
            v_title := 'Calculation Rule Mismatch';
            v_message := 'Calculation rule for "' || p_product_category || '" may not match expected format.';
            v_priority := 'medium';
        ELSE
            v_title := 'Calculation Rule Issue';
            v_message := 'Issue detected with calculation rules for "' || p_product_category || '" products.';
            v_priority := 'medium';
    END CASE;

    -- Create the announcement
    INSERT INTO system_announcements (
        title,
        message,
        type,
        priority,
        category,
        source_component,
        target_audience,
        error_context,
        related_entity_type,
        related_entity_id
    ) VALUES (
        v_title,
        v_message,
        'warning',
        v_priority,
        'calculation_rules',
        'production_cost_calculator',
        ARRAY['admin'],
        jsonb_build_object(
            'error_id', p_error_id,
            'product_category', p_product_category,
            'error_type', p_error_type,
            'suggested_action', COALESCE(p_suggested_action, 'Review calculation rules for ' || p_product_category),
            'created_by_system', true
        ),
        'calculation_rule',
        p_product_category
    ) RETURNING id INTO v_announcement_id;

    -- Link the error to the announcement
    UPDATE calculation_rules_errors 
    SET announcement_id = v_announcement_id 
    WHERE id = p_error_id;

    RETURN v_announcement_id;
END;
$$ LANGUAGE plpgsql;

-- ============================================
-- STEP 4: Auto-cleanup Job (Optional)
-- ============================================

-- Function to clean up expired announcements
CREATE OR REPLACE FUNCTION cleanup_expired_announcements()
RETURNS INTEGER AS $$
DECLARE
    v_deleted_count INTEGER;
BEGIN
    -- Deactivate expired announcements
    UPDATE system_announcements 
    SET is_active = false,
        updated_at = NOW()
    WHERE is_active = true 
      AND display_until IS NOT NULL 
      AND display_until < NOW();
    
    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;
    
    RETURN v_deleted_count;
END;
$$ LANGUAGE plpgsql;

-- ============================================
-- STEP 5: Seed Data (Optional - for testing)
-- ============================================

-- Insert a sample announcement to test the system
INSERT INTO system_announcements (
    title,
    message,
    type,
    priority,
    category,
    source_component,
    target_audience,
    error_context
) VALUES (
    'Calculation Rules System Active',
    'Dynamic announcement system is now monitoring calculation rules for production cost calculations.',
    'info',
    'low',
    'calculation_rules',
    'system',
    ARRAY['admin'],
    jsonb_build_object(
        'system_status', 'active',
        'monitoring', 'calculation_rules_errors'
    )
) ON CONFLICT DO NOTHING;