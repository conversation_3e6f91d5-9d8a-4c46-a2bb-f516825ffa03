# Simple Database Backup Script
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$backupDir = "database-backups\BACKUP_$timestamp"

Write-Host "Creating backup directory: $backupDir" -ForegroundColor Green
New-Item -ItemType Directory -Path $backupDir -Force | Out-Null

Write-Host "Note: DATABASE_URL needed in .env file for pg_dump backup" -ForegroundColor Yellow
Write-Host "Format: DATABASE_URL=postgresql://postgres:[password]@db.wheufegilqkbcsixkoka.supabase.co:5432/postgres" -ForegroundColor Yellow

Write-Host "Backup directory created successfully: $backupDir" -ForegroundColor Green
Write-Host "Ready for backup when DATABASE_URL is configured" -ForegroundColor Green